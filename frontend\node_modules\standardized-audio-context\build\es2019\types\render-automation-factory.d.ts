import { TGetAudioParamRendererFunction } from './get-audio-param-renderer-function';
import { TRenderAutomationFunction } from './render-automation-function';
import { TRenderInputsOfAudioParamFunction } from './render-inputs-of-audio-param-function';
export type TRenderAutomationFactory = (getAudioParamRenderer: TGetAudioParamRendererFunction, renderInputsOfAudioParam: TRenderInputsOfAudioParamFunction) => TRenderAutomationFunction;
//# sourceMappingURL=render-automation-factory.d.ts.map
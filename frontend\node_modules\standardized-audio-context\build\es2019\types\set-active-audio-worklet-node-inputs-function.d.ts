import { TActiveInputConnection } from './active-input-connection';
import { TContext } from './context';
import { TNativeAudioWorkletNode } from './native-audio-worklet-node';
export type TSetActiveAudioWorkletNodeInputsFunction = <T extends TContext>(nativeAudioWorkletNode: TNativeAudioWorkletNode, activeInputs: Set<TActiveInputConnection<T>>[]) => void;
//# sourceMappingURL=set-active-audio-worklet-node-inputs-function.d.ts.map
import { IAudioParam } from '../interfaces';
import { TNativeAudioParam } from './native-audio-param';
import { TNativeOfflineAudioContext } from './native-offline-audio-context';
export type TRenderAutomationFunction = (nativeOfflineAudioContext: TNativeOfflineAudioContext, audioParam: IAudioParam, nativeAudioParam: TNativeAudioParam) => Promise<void>;
//# sourceMappingURL=render-automation-function.d.ts.map
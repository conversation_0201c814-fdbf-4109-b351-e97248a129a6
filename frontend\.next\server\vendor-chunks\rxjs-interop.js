"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rxjs-interop";
exports.ids = ["vendor-chunks/rxjs-interop"];
exports.modules = {

/***/ "(ssr)/./node_modules/rxjs-interop/dist/esm/patch.js":
/*!*****************************************************!*\
  !*** ./node_modules/rxjs-interop/dist/esm/patch.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patch: () => (/* binding */ patch)\n/* harmony export */ });\n/* harmony import */ var _symbols__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./symbols */ \"(ssr)/./node_modules/rxjs-interop/dist/esm/symbols.js\");\n\r\nfunction patch(arg) {\r\n    if (!Symbol.observable) {\r\n        if (typeof arg === \"function\" &&\r\n            arg.prototype &&\r\n            arg.prototype[Symbol.observable]) {\r\n            arg.prototype[_symbols__WEBPACK_IMPORTED_MODULE_0__.observable] = arg.prototype[Symbol.observable];\r\n            delete arg.prototype[Symbol.observable];\r\n        }\r\n        else {\r\n            arg[_symbols__WEBPACK_IMPORTED_MODULE_0__.observable] = arg[Symbol.observable];\r\n            delete arg[Symbol.observable];\r\n        }\r\n    }\r\n    return arg;\r\n}\r\n//# sourceMappingURL=patch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnhqcy1pbnRlcm9wL2Rpc3QvZXNtL3BhdGNoLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVDO0FBQ2hDO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsZ0RBQVU7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFVO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxoYWNrb25fdHRzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyeGpzLWludGVyb3BcXGRpc3RcXGVzbVxccGF0Y2guanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JzZXJ2YWJsZSB9IGZyb20gXCIuL3N5bWJvbHNcIjtcclxuZXhwb3J0IGZ1bmN0aW9uIHBhdGNoKGFyZykge1xyXG4gICAgaWYgKCFTeW1ib2wub2JzZXJ2YWJsZSkge1xyXG4gICAgICAgIGlmICh0eXBlb2YgYXJnID09PSBcImZ1bmN0aW9uXCIgJiZcclxuICAgICAgICAgICAgYXJnLnByb3RvdHlwZSAmJlxyXG4gICAgICAgICAgICBhcmcucHJvdG90eXBlW1N5bWJvbC5vYnNlcnZhYmxlXSkge1xyXG4gICAgICAgICAgICBhcmcucHJvdG90eXBlW29ic2VydmFibGVdID0gYXJnLnByb3RvdHlwZVtTeW1ib2wub2JzZXJ2YWJsZV07XHJcbiAgICAgICAgICAgIGRlbGV0ZSBhcmcucHJvdG90eXBlW1N5bWJvbC5vYnNlcnZhYmxlXTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgIGFyZ1tvYnNlcnZhYmxlXSA9IGFyZ1tTeW1ib2wub2JzZXJ2YWJsZV07XHJcbiAgICAgICAgICAgIGRlbGV0ZSBhcmdbU3ltYm9sLm9ic2VydmFibGVdO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiBhcmc7XHJcbn1cclxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGF0Y2guanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rxjs-interop/dist/esm/patch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rxjs-interop/dist/esm/symbols.js":
/*!*******************************************************!*\
  !*** ./node_modules/rxjs-interop/dist/esm/symbols.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observable: () => (/* binding */ observable)\n/* harmony export */ });\nconst observable = Symbol.observable || \"@@observable\";\r\n//# sourceMappingURL=symbols.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnhqcy1pbnRlcm9wL2Rpc3QvZXNtL3N5bWJvbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcaGFja29uX3R0c1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccnhqcy1pbnRlcm9wXFxkaXN0XFxlc21cXHN5bWJvbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IG9ic2VydmFibGUgPSBTeW1ib2wub2JzZXJ2YWJsZSB8fCBcIkBAb2JzZXJ2YWJsZVwiO1xyXG4vLyMgc291cmNlTWFwcGluZ1VSTD1zeW1ib2xzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rxjs-interop/dist/esm/symbols.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rxjs-interop/dist/esm/to-observer.js":
/*!***********************************************************!*\
  !*** ./node_modules/rxjs-interop/dist/esm/to-observer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toObserver: () => (/* binding */ toObserver)\n/* harmony export */ });\nconst noop = () => { };\r\nconst rethrow = (error) => {\r\n    throw error;\r\n};\r\nfunction toObserver(observer) {\r\n    if (observer) {\r\n        if (observer.next && observer.error && observer.complete) {\r\n            return observer;\r\n        }\r\n        return {\r\n            complete: (observer.complete ?? noop).bind(observer),\r\n            error: (observer.error ?? rethrow).bind(observer),\r\n            next: (observer.next ?? noop).bind(observer),\r\n        };\r\n    }\r\n    return {\r\n        complete: noop,\r\n        error: rethrow,\r\n        next: noop,\r\n    };\r\n}\r\n//# sourceMappingURL=to-observer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcnhqcy1pbnRlcm9wL2Rpc3QvZXNtL3RvLW9ic2VydmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxoYWNrb25fdHRzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyeGpzLWludGVyb3BcXGRpc3RcXGVzbVxcdG8tb2JzZXJ2ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgbm9vcCA9ICgpID0+IHsgfTtcclxuY29uc3QgcmV0aHJvdyA9IChlcnJvcikgPT4ge1xyXG4gICAgdGhyb3cgZXJyb3I7XHJcbn07XHJcbmV4cG9ydCBmdW5jdGlvbiB0b09ic2VydmVyKG9ic2VydmVyKSB7XHJcbiAgICBpZiAob2JzZXJ2ZXIpIHtcclxuICAgICAgICBpZiAob2JzZXJ2ZXIubmV4dCAmJiBvYnNlcnZlci5lcnJvciAmJiBvYnNlcnZlci5jb21wbGV0ZSkge1xyXG4gICAgICAgICAgICByZXR1cm4gb2JzZXJ2ZXI7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIGNvbXBsZXRlOiAob2JzZXJ2ZXIuY29tcGxldGUgPz8gbm9vcCkuYmluZChvYnNlcnZlciksXHJcbiAgICAgICAgICAgIGVycm9yOiAob2JzZXJ2ZXIuZXJyb3IgPz8gcmV0aHJvdykuYmluZChvYnNlcnZlciksXHJcbiAgICAgICAgICAgIG5leHQ6IChvYnNlcnZlci5uZXh0ID8/IG5vb3ApLmJpbmQob2JzZXJ2ZXIpLFxyXG4gICAgICAgIH07XHJcbiAgICB9XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAgIGNvbXBsZXRlOiBub29wLFxyXG4gICAgICAgIGVycm9yOiByZXRocm93LFxyXG4gICAgICAgIG5leHQ6IG5vb3AsXHJcbiAgICB9O1xyXG59XHJcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRvLW9ic2VydmVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rxjs-interop/dist/esm/to-observer.js\n");

/***/ })

};
;
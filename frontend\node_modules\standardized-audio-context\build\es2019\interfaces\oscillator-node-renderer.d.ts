import { IAudioNodeRenderer } from './audio-node-renderer';
import { IMinimalOfflineAudioContext } from './minimal-offline-audio-context';
import { IOfflineAudioContext } from './offline-audio-context';
import { IOscillatorNode } from './oscillator-node';
import { IPeriodicWave } from './periodic-wave';
export interface IOscillatorNodeRenderer<T extends IMinimalOfflineAudioContext | IOfflineAudioContext> extends IAudioNodeRenderer<T, IOscillatorNode<T>> {
    periodicWave: null | IPeriodicWave;
    start: number;
    stop: number;
}
//# sourceMappingURL=oscillator-node-renderer.d.ts.map
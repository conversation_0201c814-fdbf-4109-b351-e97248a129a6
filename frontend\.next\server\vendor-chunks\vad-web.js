"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vad-web";
exports.ids = ["vendor-chunks/vad-web"];
exports.modules = {

/***/ "(ssr)/./node_modules/vad-web/dist/chunk-Y2PMAUST.js":
/*!*****************************************************!*\
  !*** ./node_modules/vad-web/dist/chunk-Y2PMAUST.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUDIO_FRAME_SIZE: () => (/* binding */ AUDIO_FRAME_SIZE),\n/* harmony export */   AudioFrameQueue: () => (/* binding */ AudioFrameQueue),\n/* harmony export */   EXIT_THRESHOLD: () => (/* binding */ EXIT_THRESHOLD),\n/* harmony export */   MAX_AUDIO_DURATION_SAMPLES: () => (/* binding */ MAX_AUDIO_DURATION_SAMPLES),\n/* harmony export */   MIN_SPEECH_SAMPLES: () => (/* binding */ MIN_SPEECH_SAMPLES),\n/* harmony export */   SAMPLE_RATE: () => (/* binding */ SAMPLE_RATE),\n/* harmony export */   SAMPLE_RATE_MS: () => (/* binding */ SAMPLE_RATE_MS),\n/* harmony export */   SPEECH_ACTIVE_INTERVAL_MS: () => (/* binding */ SPEECH_ACTIVE_INTERVAL_MS),\n/* harmony export */   SPEECH_PAD_SAMPLES: () => (/* binding */ SPEECH_PAD_SAMPLES),\n/* harmony export */   SPEECH_THRESHOLD: () => (/* binding */ SPEECH_THRESHOLD)\n/* harmony export */ });\n// src/constants.ts\nvar SAMPLE_RATE = 16e3;\nvar SAMPLE_RATE_MS = SAMPLE_RATE / 1e3;\nvar SPEECH_THRESHOLD = 0.3;\nvar EXIT_THRESHOLD = 0.1;\nvar AUDIO_FRAME_SIZE = 512;\nfunction roundToFrameSize(value) {\n  return Math.floor(value / AUDIO_FRAME_SIZE) * AUDIO_FRAME_SIZE;\n}\nvar MAX_AUDIO_DURATION_SECONDS = 30;\nvar MAX_AUDIO_DURATION_SAMPLES = roundToFrameSize(\n  MAX_AUDIO_DURATION_SECONDS * SAMPLE_RATE\n);\nvar SPEECH_PAD_SECONDS = 0.8;\nvar SPEECH_PAD_SAMPLES = roundToFrameSize(\n  SPEECH_PAD_SECONDS * SAMPLE_RATE\n);\nvar MIN_SPEECH_SECONDS = 0.25;\nvar MIN_SPEECH_SAMPLES = roundToFrameSize(\n  MIN_SPEECH_SECONDS * SAMPLE_RATE\n);\nvar SPEECH_ACTIVE_INTERVAL_MS = 1e3;\n\n// src/utils/audio-frame-queue.ts\nvar AudioFrameQueue = class {\n  constructor(frameSize) {\n    this.frameSize = frameSize;\n    this.queue = new Array();\n    this.bufferPosition = 0;\n    this.buffer = new Float32Array(frameSize);\n  }\n  enqueue(input) {\n    if (this.bufferPosition === 0 && input.length === this.frameSize) {\n      this.queue.push(input);\n      return;\n    }\n    let inputPosition = 0;\n    while (inputPosition < input.length) {\n      const inputRemaining = input.length - inputPosition;\n      const bufferRemaining = this.frameSize - this.bufferPosition;\n      const toCopy = Math.min(inputRemaining, bufferRemaining);\n      this.buffer.set(\n        input.subarray(inputPosition, inputPosition + toCopy),\n        this.bufferPosition\n      );\n      this.bufferPosition += toCopy;\n      inputPosition += toCopy;\n      if (this.bufferPosition >= this.frameSize) {\n        this.queue.push(this.buffer);\n        this.buffer = new Float32Array(this.frameSize);\n        this.bufferPosition = 0;\n      }\n    }\n  }\n  dequeue() {\n    return this.queue.shift();\n  }\n  clear() {\n    this.queue.length = 0;\n    this.bufferPosition = 0;\n    this.buffer.fill(0);\n  }\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vad-web/dist/chunk-Y2PMAUST.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vad-web/dist/index.js":
/*!********************************************!*\
  !*** ./node_modules/vad-web/dist/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readAudio: () => (/* binding */ readAudio),\n/* harmony export */   recordAudio: () => (/* binding */ recordAudio)\n/* harmony export */ });\n/* harmony import */ var _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-Y2PMAUST.js */ \"(ssr)/./node_modules/vad-web/dist/chunk-Y2PMAUST.js\");\n/* harmony import */ var p_limit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! p-limit */ \"(ssr)/./node_modules/p-limit/index.js\");\n/* harmony import */ var recorder_audio_worklet__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! recorder-audio-worklet */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/module.js\");\n/* harmony import */ var comlink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! comlink */ \"(ssr)/./node_modules/comlink/dist/esm/comlink.mjs\");\n/* harmony import */ var just_once__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! just-once */ \"(ssr)/./node_modules/just-once/index.mjs\");\n\n\n// src/record-audio.ts\n\n\n\n// src/event.ts\nfunction dispatchEvents(messages, handlers) {\n  for (const message of messages) {\n    dispatchEvent(message, handlers);\n  }\n}\nfunction dispatchEvent(message, handlers) {\n  switch (message.type) {\n    case \"start\":\n      handlers.onSpeechStart?.();\n      break;\n    case \"end\":\n      handlers.onSpeechEnd?.();\n      break;\n    case \"ongoing\":\n      handlers.onSpeechOngoing?.(message.data);\n      break;\n    case \"available\":\n      handlers.onSpeechAvailable?.(message.data);\n      break;\n    default:\n      message;\n  }\n}\n\n// src/processor-main.ts\n\n\nvar getProcessor = (0,just_once__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(() => {\n  const worker = new Worker(new URL(/* worker import */ __webpack_require__.p + __webpack_require__.u(\"vad-web-worker\"), __webpack_require__.b), {\n    type: undefined,\n    name: \"vad-web-worker\"\n  });\n  return (0,comlink__WEBPACK_IMPORTED_MODULE_2__.wrap)(worker);\n});\nvar processor = {\n  process: async (audioData) => {\n    const processor2 = getProcessor();\n    return processor2.process(audioData);\n  },\n  stop: async () => {\n    const processor2 = getProcessor();\n    return processor2.stop();\n  }\n};\n\n// src/record-audio.ts\nvar ERROR_MESSAGE = \"Missing AudioWorklet support. Maybe this is not running in a secure context.\";\nvar disposeFunctions = [];\nvar limit = (0,p_limit__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(1);\nasync function disposeAll() {\n  while (disposeFunctions.length > 0) {\n    const disposeFunction = disposeFunctions.shift();\n    await disposeFunction?.();\n  }\n}\nasync function start(handlers) {\n  await disposeAll();\n  if (typeof AudioWorkletNode === \"undefined\") {\n    throw new TypeError(ERROR_MESSAGE);\n  }\n  const mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });\n  const audioContext = new AudioContext({ sampleRate: _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_4__.SAMPLE_RATE });\n  await (0,recorder_audio_worklet__WEBPACK_IMPORTED_MODULE_0__.addRecorderAudioWorkletModule)(async (url) => {\n    await audioContext.audioWorklet.addModule(url);\n  });\n  const workletNode = (0,recorder_audio_worklet__WEBPACK_IMPORTED_MODULE_0__.createRecorderAudioWorkletNode)(\n    AudioWorkletNode,\n    audioContext,\n    { channelCount: 1 }\n  );\n  const sourceNode = audioContext.createMediaStreamSource(mediaStream);\n  sourceNode.connect(workletNode);\n  const channel = new MessageChannel();\n  const { port1, port2 } = channel;\n  const queue = new _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_4__.AudioFrameQueue(_chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_4__.AUDIO_FRAME_SIZE);\n  port2.onmessage = async (event) => {\n    const data = event.data;\n    const audioData = data?.[0];\n    if (!(audioData instanceof Float32Array)) {\n      return;\n    }\n    queue.enqueue(audioData);\n    while (true) {\n      const frame = queue.dequeue();\n      if (!frame) break;\n      const messages = await processor.process(frame);\n      dispatchEvents(messages, handlers);\n    }\n  };\n  await workletNode.record(port1);\n  disposeFunctions.push(async () => {\n    await workletNode.stop();\n    sourceNode.disconnect();\n    workletNode.disconnect();\n    port1.close();\n    port2.close();\n    mediaStream?.getTracks().forEach((track) => track.stop());\n    await audioContext.close();\n    const messages = await processor.stop();\n    dispatchEvents(messages, handlers);\n  });\n}\nasync function recordAudio(options) {\n  await limit(() => start(options));\n  return () => limit(disposeAll);\n}\n\n// src/utils/sleep.ts\nfunction sleep(ms) {\n  return new Promise((resolve) => setTimeout(resolve, ms));\n}\n\n// src/utils/wait-for-idle.ts\nfunction waitForIdle(timeout = 100) {\n  if (typeof requestIdleCallback !== \"undefined\") {\n    return new Promise(\n      (resolve) => requestIdleCallback(() => resolve(), { timeout })\n    );\n  }\n  return sleep(0);\n}\n\n// src/read-audio.ts\nasync function start2(options) {\n  const { audioData: audioDataBuffer, realTime = false, ...handlers } = options;\n  let disposeFlag = false;\n  const audioContext = new AudioContext({ sampleRate: _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_4__.SAMPLE_RATE });\n  const dispose = async () => {\n    if (disposeFlag) return;\n    disposeFlag = true;\n    await audioContext.close();\n    const messages = await processor.stop();\n    dispatchEvents(messages, handlers);\n  };\n  try {\n    const decoded = await audioContext.decodeAudioData(audioDataBuffer);\n    const sampleRate = decoded.sampleRate;\n    const chunkSize = _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_4__.AUDIO_FRAME_SIZE;\n    const handle = async () => {\n      await waitForIdle();\n      const audioData = decoded.getChannelData(0);\n      await waitForIdle();\n      const start3 = performance.now();\n      for (let i = 0; i < audioData.length; i += chunkSize) {\n        if (disposeFlag) break;\n        const chunk = audioData.slice(i, i + chunkSize);\n        if (realTime) {\n          await waitForIdle();\n          const millisecondsPassed = performance.now() - start3;\n          const audioMillisecondsPassed = i / sampleRate * 1e3;\n          if (millisecondsPassed < audioMillisecondsPassed) {\n            await sleep(audioMillisecondsPassed - millisecondsPassed);\n          }\n        }\n        const messages = await processor.process(chunk);\n        dispatchEvents(messages, handlers);\n      }\n      await dispose();\n    };\n    void handle();\n  } catch (err) {\n    void dispose();\n    throw new Error(`Failed to initialize recording: ${err}`, { cause: err });\n  }\n  return dispose;\n}\nasync function readAudio(options) {\n  return start2(options);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmFkLXdlYi9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFJNkI7O0FBRTdCO0FBQzZCO0FBSUc7O0FBRWhDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUMrQjtBQUNEO0FBQzlCLG1CQUFtQixxREFBSztBQUN4QixvQ0FBb0MsMEdBQXNDO0FBQzFFLFVBQVUsU0FBUTtBQUNsQjtBQUNBLEdBQUc7QUFDSCxTQUFTLDZDQUFJO0FBQ2IsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsWUFBWSxtREFBTTtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0VBQWtFLGFBQWE7QUFDL0UsMENBQTBDLFlBQVksMkRBQVcsRUFBRTtBQUNuRSxRQUFRLHFGQUE2QjtBQUNyQztBQUNBLEdBQUc7QUFDSCxzQkFBc0Isc0ZBQThCO0FBQ3BEO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGVBQWU7QUFDekIsb0JBQW9CLCtEQUFlLENBQUMsZ0VBQWdCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwREFBMEQsU0FBUztBQUNuRTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsVUFBVSw0REFBNEQ7QUFDdEU7QUFDQSwwQ0FBMEMsWUFBWSwyREFBVyxFQUFFO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLGdFQUFnQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLHNCQUFzQjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLHVEQUF1RCxJQUFJLEtBQUssWUFBWTtBQUM1RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFJRSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxoYWNrb25fdHRzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFx2YWQtd2ViXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBBVURJT19GUkFNRV9TSVpFLFxuICBBdWRpb0ZyYW1lUXVldWUsXG4gIFNBTVBMRV9SQVRFXG59IGZyb20gXCIuL2NodW5rLVkyUE1BVVNULmpzXCI7XG5cbi8vIHNyYy9yZWNvcmQtYXVkaW8udHNcbmltcG9ydCBwTGltaXQgZnJvbSBcInAtbGltaXRcIjtcbmltcG9ydCB7XG4gIGFkZFJlY29yZGVyQXVkaW9Xb3JrbGV0TW9kdWxlLFxuICBjcmVhdGVSZWNvcmRlckF1ZGlvV29ya2xldE5vZGVcbn0gZnJvbSBcInJlY29yZGVyLWF1ZGlvLXdvcmtsZXRcIjtcblxuLy8gc3JjL2V2ZW50LnRzXG5mdW5jdGlvbiBkaXNwYXRjaEV2ZW50cyhtZXNzYWdlcywgaGFuZGxlcnMpIHtcbiAgZm9yIChjb25zdCBtZXNzYWdlIG9mIG1lc3NhZ2VzKSB7XG4gICAgZGlzcGF0Y2hFdmVudChtZXNzYWdlLCBoYW5kbGVycyk7XG4gIH1cbn1cbmZ1bmN0aW9uIGRpc3BhdGNoRXZlbnQobWVzc2FnZSwgaGFuZGxlcnMpIHtcbiAgc3dpdGNoIChtZXNzYWdlLnR5cGUpIHtcbiAgICBjYXNlIFwic3RhcnRcIjpcbiAgICAgIGhhbmRsZXJzLm9uU3BlZWNoU3RhcnQ/LigpO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSBcImVuZFwiOlxuICAgICAgaGFuZGxlcnMub25TcGVlY2hFbmQ/LigpO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSBcIm9uZ29pbmdcIjpcbiAgICAgIGhhbmRsZXJzLm9uU3BlZWNoT25nb2luZz8uKG1lc3NhZ2UuZGF0YSk7XG4gICAgICBicmVhaztcbiAgICBjYXNlIFwiYXZhaWxhYmxlXCI6XG4gICAgICBoYW5kbGVycy5vblNwZWVjaEF2YWlsYWJsZT8uKG1lc3NhZ2UuZGF0YSk7XG4gICAgICBicmVhaztcbiAgICBkZWZhdWx0OlxuICAgICAgbWVzc2FnZTtcbiAgfVxufVxuXG4vLyBzcmMvcHJvY2Vzc29yLW1haW4udHNcbmltcG9ydCB7IHdyYXAgfSBmcm9tIFwiY29tbGlua1wiO1xuaW1wb3J0IGNhY2hlIGZyb20gXCJqdXN0LW9uY2VcIjtcbnZhciBnZXRQcm9jZXNzb3IgPSBjYWNoZSgoKSA9PiB7XG4gIGNvbnN0IHdvcmtlciA9IG5ldyBXb3JrZXIobmV3IFVSTChcIi4vdmFkLXdlYi13b3JrZXIuanNcIiwgaW1wb3J0Lm1ldGEudXJsKSwge1xuICAgIHR5cGU6IFwibW9kdWxlXCIsXG4gICAgbmFtZTogXCJ2YWQtd2ViLXdvcmtlclwiXG4gIH0pO1xuICByZXR1cm4gd3JhcCh3b3JrZXIpO1xufSk7XG52YXIgcHJvY2Vzc29yID0ge1xuICBwcm9jZXNzOiBhc3luYyAoYXVkaW9EYXRhKSA9PiB7XG4gICAgY29uc3QgcHJvY2Vzc29yMiA9IGdldFByb2Nlc3NvcigpO1xuICAgIHJldHVybiBwcm9jZXNzb3IyLnByb2Nlc3MoYXVkaW9EYXRhKTtcbiAgfSxcbiAgc3RvcDogYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHByb2Nlc3NvcjIgPSBnZXRQcm9jZXNzb3IoKTtcbiAgICByZXR1cm4gcHJvY2Vzc29yMi5zdG9wKCk7XG4gIH1cbn07XG5cbi8vIHNyYy9yZWNvcmQtYXVkaW8udHNcbnZhciBFUlJPUl9NRVNTQUdFID0gXCJNaXNzaW5nIEF1ZGlvV29ya2xldCBzdXBwb3J0LiBNYXliZSB0aGlzIGlzIG5vdCBydW5uaW5nIGluIGEgc2VjdXJlIGNvbnRleHQuXCI7XG52YXIgZGlzcG9zZUZ1bmN0aW9ucyA9IFtdO1xudmFyIGxpbWl0ID0gcExpbWl0KDEpO1xuYXN5bmMgZnVuY3Rpb24gZGlzcG9zZUFsbCgpIHtcbiAgd2hpbGUgKGRpc3Bvc2VGdW5jdGlvbnMubGVuZ3RoID4gMCkge1xuICAgIGNvbnN0IGRpc3Bvc2VGdW5jdGlvbiA9IGRpc3Bvc2VGdW5jdGlvbnMuc2hpZnQoKTtcbiAgICBhd2FpdCBkaXNwb3NlRnVuY3Rpb24/LigpO1xuICB9XG59XG5hc3luYyBmdW5jdGlvbiBzdGFydChoYW5kbGVycykge1xuICBhd2FpdCBkaXNwb3NlQWxsKCk7XG4gIGlmICh0eXBlb2YgQXVkaW9Xb3JrbGV0Tm9kZSA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoRVJST1JfTUVTU0FHRSk7XG4gIH1cbiAgY29uc3QgbWVkaWFTdHJlYW0gPSBhd2FpdCBuYXZpZ2F0b3IubWVkaWFEZXZpY2VzLmdldFVzZXJNZWRpYSh7IGF1ZGlvOiB0cnVlIH0pO1xuICBjb25zdCBhdWRpb0NvbnRleHQgPSBuZXcgQXVkaW9Db250ZXh0KHsgc2FtcGxlUmF0ZTogU0FNUExFX1JBVEUgfSk7XG4gIGF3YWl0IGFkZFJlY29yZGVyQXVkaW9Xb3JrbGV0TW9kdWxlKGFzeW5jICh1cmwpID0+IHtcbiAgICBhd2FpdCBhdWRpb0NvbnRleHQuYXVkaW9Xb3JrbGV0LmFkZE1vZHVsZSh1cmwpO1xuICB9KTtcbiAgY29uc3Qgd29ya2xldE5vZGUgPSBjcmVhdGVSZWNvcmRlckF1ZGlvV29ya2xldE5vZGUoXG4gICAgQXVkaW9Xb3JrbGV0Tm9kZSxcbiAgICBhdWRpb0NvbnRleHQsXG4gICAgeyBjaGFubmVsQ291bnQ6IDEgfVxuICApO1xuICBjb25zdCBzb3VyY2VOb2RlID0gYXVkaW9Db250ZXh0LmNyZWF0ZU1lZGlhU3RyZWFtU291cmNlKG1lZGlhU3RyZWFtKTtcbiAgc291cmNlTm9kZS5jb25uZWN0KHdvcmtsZXROb2RlKTtcbiAgY29uc3QgY2hhbm5lbCA9IG5ldyBNZXNzYWdlQ2hhbm5lbCgpO1xuICBjb25zdCB7IHBvcnQxLCBwb3J0MiB9ID0gY2hhbm5lbDtcbiAgY29uc3QgcXVldWUgPSBuZXcgQXVkaW9GcmFtZVF1ZXVlKEFVRElPX0ZSQU1FX1NJWkUpO1xuICBwb3J0Mi5vbm1lc3NhZ2UgPSBhc3luYyAoZXZlbnQpID0+IHtcbiAgICBjb25zdCBkYXRhID0gZXZlbnQuZGF0YTtcbiAgICBjb25zdCBhdWRpb0RhdGEgPSBkYXRhPy5bMF07XG4gICAgaWYgKCEoYXVkaW9EYXRhIGluc3RhbmNlb2YgRmxvYXQzMkFycmF5KSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBxdWV1ZS5lbnF1ZXVlKGF1ZGlvRGF0YSk7XG4gICAgd2hpbGUgKHRydWUpIHtcbiAgICAgIGNvbnN0IGZyYW1lID0gcXVldWUuZGVxdWV1ZSgpO1xuICAgICAgaWYgKCFmcmFtZSkgYnJlYWs7XG4gICAgICBjb25zdCBtZXNzYWdlcyA9IGF3YWl0IHByb2Nlc3Nvci5wcm9jZXNzKGZyYW1lKTtcbiAgICAgIGRpc3BhdGNoRXZlbnRzKG1lc3NhZ2VzLCBoYW5kbGVycyk7XG4gICAgfVxuICB9O1xuICBhd2FpdCB3b3JrbGV0Tm9kZS5yZWNvcmQocG9ydDEpO1xuICBkaXNwb3NlRnVuY3Rpb25zLnB1c2goYXN5bmMgKCkgPT4ge1xuICAgIGF3YWl0IHdvcmtsZXROb2RlLnN0b3AoKTtcbiAgICBzb3VyY2VOb2RlLmRpc2Nvbm5lY3QoKTtcbiAgICB3b3JrbGV0Tm9kZS5kaXNjb25uZWN0KCk7XG4gICAgcG9ydDEuY2xvc2UoKTtcbiAgICBwb3J0Mi5jbG9zZSgpO1xuICAgIG1lZGlhU3RyZWFtPy5nZXRUcmFja3MoKS5mb3JFYWNoKCh0cmFjaykgPT4gdHJhY2suc3RvcCgpKTtcbiAgICBhd2FpdCBhdWRpb0NvbnRleHQuY2xvc2UoKTtcbiAgICBjb25zdCBtZXNzYWdlcyA9IGF3YWl0IHByb2Nlc3Nvci5zdG9wKCk7XG4gICAgZGlzcGF0Y2hFdmVudHMobWVzc2FnZXMsIGhhbmRsZXJzKTtcbiAgfSk7XG59XG5hc3luYyBmdW5jdGlvbiByZWNvcmRBdWRpbyhvcHRpb25zKSB7XG4gIGF3YWl0IGxpbWl0KCgpID0+IHN0YXJ0KG9wdGlvbnMpKTtcbiAgcmV0dXJuICgpID0+IGxpbWl0KGRpc3Bvc2VBbGwpO1xufVxuXG4vLyBzcmMvdXRpbHMvc2xlZXAudHNcbmZ1bmN0aW9uIHNsZWVwKG1zKSB7XG4gIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4gc2V0VGltZW91dChyZXNvbHZlLCBtcykpO1xufVxuXG4vLyBzcmMvdXRpbHMvd2FpdC1mb3ItaWRsZS50c1xuZnVuY3Rpb24gd2FpdEZvcklkbGUodGltZW91dCA9IDEwMCkge1xuICBpZiAodHlwZW9mIHJlcXVlc3RJZGxlQ2FsbGJhY2sgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoXG4gICAgICAocmVzb2x2ZSkgPT4gcmVxdWVzdElkbGVDYWxsYmFjaygoKSA9PiByZXNvbHZlKCksIHsgdGltZW91dCB9KVxuICAgICk7XG4gIH1cbiAgcmV0dXJuIHNsZWVwKDApO1xufVxuXG4vLyBzcmMvcmVhZC1hdWRpby50c1xuYXN5bmMgZnVuY3Rpb24gc3RhcnQyKG9wdGlvbnMpIHtcbiAgY29uc3QgeyBhdWRpb0RhdGE6IGF1ZGlvRGF0YUJ1ZmZlciwgcmVhbFRpbWUgPSBmYWxzZSwgLi4uaGFuZGxlcnMgfSA9IG9wdGlvbnM7XG4gIGxldCBkaXNwb3NlRmxhZyA9IGZhbHNlO1xuICBjb25zdCBhdWRpb0NvbnRleHQgPSBuZXcgQXVkaW9Db250ZXh0KHsgc2FtcGxlUmF0ZTogU0FNUExFX1JBVEUgfSk7XG4gIGNvbnN0IGRpc3Bvc2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKGRpc3Bvc2VGbGFnKSByZXR1cm47XG4gICAgZGlzcG9zZUZsYWcgPSB0cnVlO1xuICAgIGF3YWl0IGF1ZGlvQ29udGV4dC5jbG9zZSgpO1xuICAgIGNvbnN0IG1lc3NhZ2VzID0gYXdhaXQgcHJvY2Vzc29yLnN0b3AoKTtcbiAgICBkaXNwYXRjaEV2ZW50cyhtZXNzYWdlcywgaGFuZGxlcnMpO1xuICB9O1xuICB0cnkge1xuICAgIGNvbnN0IGRlY29kZWQgPSBhd2FpdCBhdWRpb0NvbnRleHQuZGVjb2RlQXVkaW9EYXRhKGF1ZGlvRGF0YUJ1ZmZlcik7XG4gICAgY29uc3Qgc2FtcGxlUmF0ZSA9IGRlY29kZWQuc2FtcGxlUmF0ZTtcbiAgICBjb25zdCBjaHVua1NpemUgPSBBVURJT19GUkFNRV9TSVpFO1xuICAgIGNvbnN0IGhhbmRsZSA9IGFzeW5jICgpID0+IHtcbiAgICAgIGF3YWl0IHdhaXRGb3JJZGxlKCk7XG4gICAgICBjb25zdCBhdWRpb0RhdGEgPSBkZWNvZGVkLmdldENoYW5uZWxEYXRhKDApO1xuICAgICAgYXdhaXQgd2FpdEZvcklkbGUoKTtcbiAgICAgIGNvbnN0IHN0YXJ0MyA9IHBlcmZvcm1hbmNlLm5vdygpO1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhdWRpb0RhdGEubGVuZ3RoOyBpICs9IGNodW5rU2l6ZSkge1xuICAgICAgICBpZiAoZGlzcG9zZUZsYWcpIGJyZWFrO1xuICAgICAgICBjb25zdCBjaHVuayA9IGF1ZGlvRGF0YS5zbGljZShpLCBpICsgY2h1bmtTaXplKTtcbiAgICAgICAgaWYgKHJlYWxUaW1lKSB7XG4gICAgICAgICAgYXdhaXQgd2FpdEZvcklkbGUoKTtcbiAgICAgICAgICBjb25zdCBtaWxsaXNlY29uZHNQYXNzZWQgPSBwZXJmb3JtYW5jZS5ub3coKSAtIHN0YXJ0MztcbiAgICAgICAgICBjb25zdCBhdWRpb01pbGxpc2Vjb25kc1Bhc3NlZCA9IGkgLyBzYW1wbGVSYXRlICogMWUzO1xuICAgICAgICAgIGlmIChtaWxsaXNlY29uZHNQYXNzZWQgPCBhdWRpb01pbGxpc2Vjb25kc1Bhc3NlZCkge1xuICAgICAgICAgICAgYXdhaXQgc2xlZXAoYXVkaW9NaWxsaXNlY29uZHNQYXNzZWQgLSBtaWxsaXNlY29uZHNQYXNzZWQpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBtZXNzYWdlcyA9IGF3YWl0IHByb2Nlc3Nvci5wcm9jZXNzKGNodW5rKTtcbiAgICAgICAgZGlzcGF0Y2hFdmVudHMobWVzc2FnZXMsIGhhbmRsZXJzKTtcbiAgICAgIH1cbiAgICAgIGF3YWl0IGRpc3Bvc2UoKTtcbiAgICB9O1xuICAgIHZvaWQgaGFuZGxlKCk7XG4gIH0gY2F0Y2ggKGVycikge1xuICAgIHZvaWQgZGlzcG9zZSgpO1xuICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGluaXRpYWxpemUgcmVjb3JkaW5nOiAke2Vycn1gLCB7IGNhdXNlOiBlcnIgfSk7XG4gIH1cbiAgcmV0dXJuIGRpc3Bvc2U7XG59XG5hc3luYyBmdW5jdGlvbiByZWFkQXVkaW8ob3B0aW9ucykge1xuICByZXR1cm4gc3RhcnQyKG9wdGlvbnMpO1xufVxuZXhwb3J0IHtcbiAgcmVhZEF1ZGlvLFxuICByZWNvcmRBdWRpb1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vad-web/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/vad-web/dist/vad-web-worker.js":
/*!*****************************************************!*\
  !*** ./node_modules/vad-web/dist/vad-web-worker.js ***!
  \*****************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-Y2PMAUST.js */ \"(ssr)/./node_modules/vad-web/dist/chunk-Y2PMAUST.js\");\n/* harmony import */ var comlink__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! comlink */ \"(ssr)/./node_modules/comlink/dist/esm/comlink.mjs\");\n/* harmony import */ var _huggingface_transformers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @huggingface/transformers */ \"@huggingface/transformers\");\n/* harmony import */ var just_once__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! just-once */ \"(ssr)/./node_modules/just-once/index.mjs\");\n/* harmony import */ var p_limit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! p-limit */ \"(ssr)/./node_modules/p-limit/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_huggingface_transformers__WEBPACK_IMPORTED_MODULE_0__]);\n_huggingface_transformers__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// src/processor-worker.ts\n\n\n// src/silero-vad.ts\n\n\n\nvar getModel = (0,just_once__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async () => {\n  const silero_vad = await _huggingface_transformers__WEBPACK_IMPORTED_MODULE_0__.AutoModel.from_pretrained(\n    \"onnx-community/silero-vad\",\n    {\n      config: new _huggingface_transformers__WEBPACK_IMPORTED_MODULE_0__.PretrainedConfig({ model_type: \"custom\" }),\n      dtype: \"fp32\"\n      // Full-precision\n    }\n  );\n  return silero_vad;\n});\nvar limit = (0,p_limit__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(1);\nvar SileroVAD = class {\n  constructor() {\n    this.sr = new _huggingface_transformers__WEBPACK_IMPORTED_MODULE_0__.Tensor(\"int64\", [_chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.SAMPLE_RATE], []);\n    this.state = new _huggingface_transformers__WEBPACK_IMPORTED_MODULE_0__.Tensor(\n      \"float32\",\n      new Float32Array(2 * 1 * 128),\n      [2, 1, 128]\n    );\n  }\n  async process(audioFrame, wasSpeech) {\n    const input = new _huggingface_transformers__WEBPACK_IMPORTED_MODULE_0__.Tensor(\"float32\", audioFrame, [1, audioFrame.length]);\n    const { stateN, output } = await limit(async () => {\n      const model = await getModel();\n      return model({ input, sr: this.sr, state: this.state });\n    });\n    this.state = stateN;\n    const speechScore = output.data[0];\n    return (\n      // Case 1: We are above the threshold (definitely speech)\n      speechScore > _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.SPEECH_THRESHOLD || // Case 2: We are in the process of recording, and the probability is above the negative (exit) threshold\n      wasSpeech && speechScore >= _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.EXIT_THRESHOLD\n    );\n  }\n};\n\n// src/utils/audio-data-buffer.ts\nvar AudioDataBuffer = class {\n  /**\n   * Creates an AudioDataBuffer with a given capacity.\n   * @param capacity The maximum number of samples the buffer can hold.\n   */\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.size = 0;\n    this.writeIndex = 0;\n    this.buffer = new Float32Array(capacity);\n  }\n  /**\n   * Checks if the buffer has enough space to store `len` additional samples\n   * without discarding existing ones.\n   * @param len The number of samples to check.\n   * @returns True if `len` samples can fit, false otherwise.\n   */\n  hasCapacity(len) {\n    return this.size + len <= this.capacity;\n  }\n  /**\n   * Writes audio data to the buffer. If the data size exceeds the buffer's capacity,\n   * older samples will be discarded.\n   * @param audioData The audio samples to write.\n   */\n  write(audioData) {\n    const len = audioData.length;\n    if (len > this.capacity) {\n      const startIdx = len - this.capacity;\n      this.buffer.set(audioData.subarray(startIdx));\n      this.size = this.capacity;\n      this.writeIndex = 0;\n      return;\n    }\n    if (this.writeIndex + len <= this.capacity) {\n      this.buffer.set(audioData, this.writeIndex);\n      this.writeIndex = (this.writeIndex + len) % this.capacity;\n    } else {\n      const firstPartLength = this.capacity - this.writeIndex;\n      this.buffer.set(audioData.subarray(0, firstPartLength), this.writeIndex);\n      this.buffer.set(audioData.subarray(firstPartLength), 0);\n      this.writeIndex = len - firstPartLength;\n    }\n    this.size = Math.min(this.capacity, this.size + len);\n  }\n  /**\n   * Reads samples from the buffer within the specified range.\n   *\n   * @param start Starting position from the most recent sample (0 is most recent)\n   * @param end Ending position from the most recent sample (exclusive)\n   * @returns A Float32Array containing the requested samples.\n   */\n  read(start = 0, end) {\n    end = Math.min(end ?? this.size, this.size);\n    start = Math.max(0, Math.min(start, end));\n    const readLen = end - start;\n    const result = new Float32Array(readLen);\n    const readIndex = (this.writeIndex - this.size + start + this.capacity) % this.capacity;\n    if (readIndex + readLen <= this.capacity) {\n      result.set(this.buffer.subarray(readIndex, readIndex + readLen));\n    } else {\n      const firstPartLen = this.capacity - readIndex;\n      const secondPartLen = readLen - firstPartLen;\n      result.set(this.buffer.subarray(readIndex, this.capacity));\n      result.set(this.buffer.subarray(0, secondPartLen), firstPartLen);\n    }\n    return result;\n  }\n  /**\n   * Clears the buffer.\n   */\n  clear() {\n    this.size = 0;\n    this.writeIndex = 0;\n  }\n  get length() {\n    return this.size;\n  }\n};\n\n// src/processor.ts\nvar VADProcessor = class {\n  constructor() {\n    this.vad = new SileroVAD();\n    this.buffer = new AudioDataBuffer(_chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.MAX_AUDIO_DURATION_SAMPLES);\n    this.wasSpeech = false;\n    this.frameQueue = new _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.AudioFrameQueue(_chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.AUDIO_FRAME_SIZE);\n    this.preSpeechSamples = 0;\n    this.speechSamples = 0;\n    this.postSpeechSamples = 0;\n    this.lastSpeechActiveMessageTime = 0;\n  }\n  /**\n   * Processes the audio data.\n   *\n   * @returns A list of messages that occurred during the processing.\n   */\n  async process(audioData) {\n    this.frameQueue.enqueue(audioData);\n    const messages = [];\n    while (true) {\n      const frame = this.frameQueue.dequeue();\n      if (!frame) break;\n      await this.processFrame(frame, messages);\n    }\n    return messages;\n  }\n  /**\n   * Stops the VAD processor and handles the last unfinished speech if any.\n   */\n  stop() {\n    const messages = [];\n    this.handleAudioData(messages, true);\n    return messages;\n  }\n  async processFrame(audioFrame, messages) {\n    const isSpeech = await this.vad.process(audioFrame, this.wasSpeech);\n    this.buffer.write(audioFrame);\n    if (isSpeech && !this.wasSpeech) {\n      this.wasSpeech = true;\n      messages.push({ type: \"start\" });\n    } else if (!isSpeech && this.wasSpeech) {\n      this.wasSpeech = false;\n      messages.push({ type: \"end\" });\n    }\n    if (this.wasSpeech) {\n      this.speechSamples += this.postSpeechSamples + audioFrame.length;\n      this.postSpeechSamples = 0;\n    } else if (this.speechSamples) {\n      this.postSpeechSamples += audioFrame.length;\n    } else {\n      this.preSpeechSamples += audioFrame.length;\n    }\n    this.handleAudioData(messages, false);\n    return;\n  }\n  handleAudioData(messages, force) {\n    if (this.speechSamples < _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.MIN_SPEECH_SAMPLES) {\n      return;\n    }\n    const now = performance.now();\n    let speechData;\n    if (now - this.lastSpeechActiveMessageTime > _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.SPEECH_ACTIVE_INTERVAL_MS) {\n      this.lastSpeechActiveMessageTime = now;\n      speechData = this.getAudioData(now);\n      messages.push({ type: \"ongoing\", data: speechData });\n    }\n    if (force || this.postSpeechSamples > _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.SPEECH_PAD_SAMPLES || this.speechSamples + _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.SPEECH_PAD_SAMPLES * 2 >= _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.MAX_AUDIO_DURATION_SAMPLES) {\n      speechData = speechData || this.getAudioData(now);\n      messages.push({ type: \"available\", data: speechData });\n      this.reset();\n    }\n  }\n  getAudioData(now) {\n    const pickedSamples = Math.min(\n      this.buffer.length,\n      Math.min(this.preSpeechSamples, _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.SPEECH_PAD_SAMPLES) + this.speechSamples + this.postSpeechSamples\n    );\n    const endIndex = this.buffer.length;\n    const startIndex = endIndex - pickedSamples;\n    const endTime = now;\n    const startTime = endTime - pickedSamples / _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.SAMPLE_RATE_MS;\n    return {\n      startTime,\n      endTime,\n      audioData: this.buffer.read(startIndex, endIndex),\n      sampleRate: _chunk_Y2PMAUST_js__WEBPACK_IMPORTED_MODULE_3__.SAMPLE_RATE\n    };\n  }\n  reset() {\n    this.preSpeechSamples = 0;\n    this.speechSamples = 0;\n    this.postSpeechSamples = 0;\n    this.wasSpeech = false;\n  }\n};\n\n// src/processor-worker.ts\nfunction exposeProcessor() {\n  const processor = new VADProcessor();\n  (0,comlink__WEBPACK_IMPORTED_MODULE_4__.expose)(processor);\n}\n\n// src/vad-web-worker.js\nexposeProcessor();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdmFkLXdlYi9kaXN0L3ZhZC13ZWItd29ya2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQVc2Qjs7QUFFN0I7QUFDaUM7O0FBRWpDO0FBS21DO0FBQ0w7QUFDRDtBQUM3QixlQUFlLHFEQUFLO0FBQ3BCLDJCQUEyQixnRUFBUztBQUNwQztBQUNBO0FBQ0Esa0JBQWtCLHVFQUFnQixHQUFHLHNCQUFzQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELFlBQVksbURBQU07QUFDbEI7QUFDQTtBQUNBLGtCQUFrQiw2REFBTSxXQUFXLDJEQUFXO0FBQzlDLHFCQUFxQiw2REFBTTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQU07QUFDNUIsWUFBWSxpQkFBaUI7QUFDN0I7QUFDQSxxQkFBcUIsdUNBQXVDO0FBQzVELEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixnRUFBZ0I7QUFDcEMsa0NBQWtDLDhEQUFjO0FBQ2hEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQywwRUFBMEI7QUFDaEU7QUFDQSwwQkFBMEIsK0RBQWUsQ0FBQyxnRUFBZ0I7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLGVBQWU7QUFDckMsTUFBTTtBQUNOO0FBQ0Esc0JBQXNCLGFBQWE7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLGtFQUFrQjtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCx5RUFBeUI7QUFDMUU7QUFDQTtBQUNBLHNCQUFzQixtQ0FBbUM7QUFDekQ7QUFDQSwwQ0FBMEMsa0VBQWtCLHlCQUF5QixrRUFBa0IsUUFBUSwwRUFBMEI7QUFDekk7QUFDQSxzQkFBc0IscUNBQXFDO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxrRUFBa0I7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsOERBQWM7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsMkRBQVc7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsK0NBQU07QUFDUjs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHZhZC13ZWJcXGRpc3RcXHZhZC13ZWItd29ya2VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIEFVRElPX0ZSQU1FX1NJWkUsXG4gIEF1ZGlvRnJhbWVRdWV1ZSxcbiAgRVhJVF9USFJFU0hPTEQsXG4gIE1BWF9BVURJT19EVVJBVElPTl9TQU1QTEVTLFxuICBNSU5fU1BFRUNIX1NBTVBMRVMsXG4gIFNBTVBMRV9SQVRFLFxuICBTQU1QTEVfUkFURV9NUyxcbiAgU1BFRUNIX0FDVElWRV9JTlRFUlZBTF9NUyxcbiAgU1BFRUNIX1BBRF9TQU1QTEVTLFxuICBTUEVFQ0hfVEhSRVNIT0xEXG59IGZyb20gXCIuL2NodW5rLVkyUE1BVVNULmpzXCI7XG5cbi8vIHNyYy9wcm9jZXNzb3Itd29ya2VyLnRzXG5pbXBvcnQgeyBleHBvc2UgfSBmcm9tIFwiY29tbGlua1wiO1xuXG4vLyBzcmMvc2lsZXJvLXZhZC50c1xuaW1wb3J0IHtcbiAgQXV0b01vZGVsLFxuICBQcmV0cmFpbmVkQ29uZmlnLFxuICBUZW5zb3Jcbn0gZnJvbSBcIkBodWdnaW5nZmFjZS90cmFuc2Zvcm1lcnNcIjtcbmltcG9ydCBjYWNoZSBmcm9tIFwianVzdC1vbmNlXCI7XG5pbXBvcnQgcExpbWl0IGZyb20gXCJwLWxpbWl0XCI7XG52YXIgZ2V0TW9kZWwgPSBjYWNoZShhc3luYyAoKSA9PiB7XG4gIGNvbnN0IHNpbGVyb192YWQgPSBhd2FpdCBBdXRvTW9kZWwuZnJvbV9wcmV0cmFpbmVkKFxuICAgIFwib25ueC1jb21tdW5pdHkvc2lsZXJvLXZhZFwiLFxuICAgIHtcbiAgICAgIGNvbmZpZzogbmV3IFByZXRyYWluZWRDb25maWcoeyBtb2RlbF90eXBlOiBcImN1c3RvbVwiIH0pLFxuICAgICAgZHR5cGU6IFwiZnAzMlwiXG4gICAgICAvLyBGdWxsLXByZWNpc2lvblxuICAgIH1cbiAgKTtcbiAgcmV0dXJuIHNpbGVyb192YWQ7XG59KTtcbnZhciBsaW1pdCA9IHBMaW1pdCgxKTtcbnZhciBTaWxlcm9WQUQgPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuc3IgPSBuZXcgVGVuc29yKFwiaW50NjRcIiwgW1NBTVBMRV9SQVRFXSwgW10pO1xuICAgIHRoaXMuc3RhdGUgPSBuZXcgVGVuc29yKFxuICAgICAgXCJmbG9hdDMyXCIsXG4gICAgICBuZXcgRmxvYXQzMkFycmF5KDIgKiAxICogMTI4KSxcbiAgICAgIFsyLCAxLCAxMjhdXG4gICAgKTtcbiAgfVxuICBhc3luYyBwcm9jZXNzKGF1ZGlvRnJhbWUsIHdhc1NwZWVjaCkge1xuICAgIGNvbnN0IGlucHV0ID0gbmV3IFRlbnNvcihcImZsb2F0MzJcIiwgYXVkaW9GcmFtZSwgWzEsIGF1ZGlvRnJhbWUubGVuZ3RoXSk7XG4gICAgY29uc3QgeyBzdGF0ZU4sIG91dHB1dCB9ID0gYXdhaXQgbGltaXQoYXN5bmMgKCkgPT4ge1xuICAgICAgY29uc3QgbW9kZWwgPSBhd2FpdCBnZXRNb2RlbCgpO1xuICAgICAgcmV0dXJuIG1vZGVsKHsgaW5wdXQsIHNyOiB0aGlzLnNyLCBzdGF0ZTogdGhpcy5zdGF0ZSB9KTtcbiAgICB9KTtcbiAgICB0aGlzLnN0YXRlID0gc3RhdGVOO1xuICAgIGNvbnN0IHNwZWVjaFNjb3JlID0gb3V0cHV0LmRhdGFbMF07XG4gICAgcmV0dXJuIChcbiAgICAgIC8vIENhc2UgMTogV2UgYXJlIGFib3ZlIHRoZSB0aHJlc2hvbGQgKGRlZmluaXRlbHkgc3BlZWNoKVxuICAgICAgc3BlZWNoU2NvcmUgPiBTUEVFQ0hfVEhSRVNIT0xEIHx8IC8vIENhc2UgMjogV2UgYXJlIGluIHRoZSBwcm9jZXNzIG9mIHJlY29yZGluZywgYW5kIHRoZSBwcm9iYWJpbGl0eSBpcyBhYm92ZSB0aGUgbmVnYXRpdmUgKGV4aXQpIHRocmVzaG9sZFxuICAgICAgd2FzU3BlZWNoICYmIHNwZWVjaFNjb3JlID49IEVYSVRfVEhSRVNIT0xEXG4gICAgKTtcbiAgfVxufTtcblxuLy8gc3JjL3V0aWxzL2F1ZGlvLWRhdGEtYnVmZmVyLnRzXG52YXIgQXVkaW9EYXRhQnVmZmVyID0gY2xhc3Mge1xuICAvKipcbiAgICogQ3JlYXRlcyBhbiBBdWRpb0RhdGFCdWZmZXIgd2l0aCBhIGdpdmVuIGNhcGFjaXR5LlxuICAgKiBAcGFyYW0gY2FwYWNpdHkgVGhlIG1heGltdW0gbnVtYmVyIG9mIHNhbXBsZXMgdGhlIGJ1ZmZlciBjYW4gaG9sZC5cbiAgICovXG4gIGNvbnN0cnVjdG9yKGNhcGFjaXR5KSB7XG4gICAgdGhpcy5jYXBhY2l0eSA9IGNhcGFjaXR5O1xuICAgIHRoaXMuc2l6ZSA9IDA7XG4gICAgdGhpcy53cml0ZUluZGV4ID0gMDtcbiAgICB0aGlzLmJ1ZmZlciA9IG5ldyBGbG9hdDMyQXJyYXkoY2FwYWNpdHkpO1xuICB9XG4gIC8qKlxuICAgKiBDaGVja3MgaWYgdGhlIGJ1ZmZlciBoYXMgZW5vdWdoIHNwYWNlIHRvIHN0b3JlIGBsZW5gIGFkZGl0aW9uYWwgc2FtcGxlc1xuICAgKiB3aXRob3V0IGRpc2NhcmRpbmcgZXhpc3Rpbmcgb25lcy5cbiAgICogQHBhcmFtIGxlbiBUaGUgbnVtYmVyIG9mIHNhbXBsZXMgdG8gY2hlY2suXG4gICAqIEByZXR1cm5zIFRydWUgaWYgYGxlbmAgc2FtcGxlcyBjYW4gZml0LCBmYWxzZSBvdGhlcndpc2UuXG4gICAqL1xuICBoYXNDYXBhY2l0eShsZW4pIHtcbiAgICByZXR1cm4gdGhpcy5zaXplICsgbGVuIDw9IHRoaXMuY2FwYWNpdHk7XG4gIH1cbiAgLyoqXG4gICAqIFdyaXRlcyBhdWRpbyBkYXRhIHRvIHRoZSBidWZmZXIuIElmIHRoZSBkYXRhIHNpemUgZXhjZWVkcyB0aGUgYnVmZmVyJ3MgY2FwYWNpdHksXG4gICAqIG9sZGVyIHNhbXBsZXMgd2lsbCBiZSBkaXNjYXJkZWQuXG4gICAqIEBwYXJhbSBhdWRpb0RhdGEgVGhlIGF1ZGlvIHNhbXBsZXMgdG8gd3JpdGUuXG4gICAqL1xuICB3cml0ZShhdWRpb0RhdGEpIHtcbiAgICBjb25zdCBsZW4gPSBhdWRpb0RhdGEubGVuZ3RoO1xuICAgIGlmIChsZW4gPiB0aGlzLmNhcGFjaXR5KSB7XG4gICAgICBjb25zdCBzdGFydElkeCA9IGxlbiAtIHRoaXMuY2FwYWNpdHk7XG4gICAgICB0aGlzLmJ1ZmZlci5zZXQoYXVkaW9EYXRhLnN1YmFycmF5KHN0YXJ0SWR4KSk7XG4gICAgICB0aGlzLnNpemUgPSB0aGlzLmNhcGFjaXR5O1xuICAgICAgdGhpcy53cml0ZUluZGV4ID0gMDtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgaWYgKHRoaXMud3JpdGVJbmRleCArIGxlbiA8PSB0aGlzLmNhcGFjaXR5KSB7XG4gICAgICB0aGlzLmJ1ZmZlci5zZXQoYXVkaW9EYXRhLCB0aGlzLndyaXRlSW5kZXgpO1xuICAgICAgdGhpcy53cml0ZUluZGV4ID0gKHRoaXMud3JpdGVJbmRleCArIGxlbikgJSB0aGlzLmNhcGFjaXR5O1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCBmaXJzdFBhcnRMZW5ndGggPSB0aGlzLmNhcGFjaXR5IC0gdGhpcy53cml0ZUluZGV4O1xuICAgICAgdGhpcy5idWZmZXIuc2V0KGF1ZGlvRGF0YS5zdWJhcnJheSgwLCBmaXJzdFBhcnRMZW5ndGgpLCB0aGlzLndyaXRlSW5kZXgpO1xuICAgICAgdGhpcy5idWZmZXIuc2V0KGF1ZGlvRGF0YS5zdWJhcnJheShmaXJzdFBhcnRMZW5ndGgpLCAwKTtcbiAgICAgIHRoaXMud3JpdGVJbmRleCA9IGxlbiAtIGZpcnN0UGFydExlbmd0aDtcbiAgICB9XG4gICAgdGhpcy5zaXplID0gTWF0aC5taW4odGhpcy5jYXBhY2l0eSwgdGhpcy5zaXplICsgbGVuKTtcbiAgfVxuICAvKipcbiAgICogUmVhZHMgc2FtcGxlcyBmcm9tIHRoZSBidWZmZXIgd2l0aGluIHRoZSBzcGVjaWZpZWQgcmFuZ2UuXG4gICAqXG4gICAqIEBwYXJhbSBzdGFydCBTdGFydGluZyBwb3NpdGlvbiBmcm9tIHRoZSBtb3N0IHJlY2VudCBzYW1wbGUgKDAgaXMgbW9zdCByZWNlbnQpXG4gICAqIEBwYXJhbSBlbmQgRW5kaW5nIHBvc2l0aW9uIGZyb20gdGhlIG1vc3QgcmVjZW50IHNhbXBsZSAoZXhjbHVzaXZlKVxuICAgKiBAcmV0dXJucyBBIEZsb2F0MzJBcnJheSBjb250YWluaW5nIHRoZSByZXF1ZXN0ZWQgc2FtcGxlcy5cbiAgICovXG4gIHJlYWQoc3RhcnQgPSAwLCBlbmQpIHtcbiAgICBlbmQgPSBNYXRoLm1pbihlbmQgPz8gdGhpcy5zaXplLCB0aGlzLnNpemUpO1xuICAgIHN0YXJ0ID0gTWF0aC5tYXgoMCwgTWF0aC5taW4oc3RhcnQsIGVuZCkpO1xuICAgIGNvbnN0IHJlYWRMZW4gPSBlbmQgLSBzdGFydDtcbiAgICBjb25zdCByZXN1bHQgPSBuZXcgRmxvYXQzMkFycmF5KHJlYWRMZW4pO1xuICAgIGNvbnN0IHJlYWRJbmRleCA9ICh0aGlzLndyaXRlSW5kZXggLSB0aGlzLnNpemUgKyBzdGFydCArIHRoaXMuY2FwYWNpdHkpICUgdGhpcy5jYXBhY2l0eTtcbiAgICBpZiAocmVhZEluZGV4ICsgcmVhZExlbiA8PSB0aGlzLmNhcGFjaXR5KSB7XG4gICAgICByZXN1bHQuc2V0KHRoaXMuYnVmZmVyLnN1YmFycmF5KHJlYWRJbmRleCwgcmVhZEluZGV4ICsgcmVhZExlbikpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCBmaXJzdFBhcnRMZW4gPSB0aGlzLmNhcGFjaXR5IC0gcmVhZEluZGV4O1xuICAgICAgY29uc3Qgc2Vjb25kUGFydExlbiA9IHJlYWRMZW4gLSBmaXJzdFBhcnRMZW47XG4gICAgICByZXN1bHQuc2V0KHRoaXMuYnVmZmVyLnN1YmFycmF5KHJlYWRJbmRleCwgdGhpcy5jYXBhY2l0eSkpO1xuICAgICAgcmVzdWx0LnNldCh0aGlzLmJ1ZmZlci5zdWJhcnJheSgwLCBzZWNvbmRQYXJ0TGVuKSwgZmlyc3RQYXJ0TGVuKTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuICAvKipcbiAgICogQ2xlYXJzIHRoZSBidWZmZXIuXG4gICAqL1xuICBjbGVhcigpIHtcbiAgICB0aGlzLnNpemUgPSAwO1xuICAgIHRoaXMud3JpdGVJbmRleCA9IDA7XG4gIH1cbiAgZ2V0IGxlbmd0aCgpIHtcbiAgICByZXR1cm4gdGhpcy5zaXplO1xuICB9XG59O1xuXG4vLyBzcmMvcHJvY2Vzc29yLnRzXG52YXIgVkFEUHJvY2Vzc29yID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLnZhZCA9IG5ldyBTaWxlcm9WQUQoKTtcbiAgICB0aGlzLmJ1ZmZlciA9IG5ldyBBdWRpb0RhdGFCdWZmZXIoTUFYX0FVRElPX0RVUkFUSU9OX1NBTVBMRVMpO1xuICAgIHRoaXMud2FzU3BlZWNoID0gZmFsc2U7XG4gICAgdGhpcy5mcmFtZVF1ZXVlID0gbmV3IEF1ZGlvRnJhbWVRdWV1ZShBVURJT19GUkFNRV9TSVpFKTtcbiAgICB0aGlzLnByZVNwZWVjaFNhbXBsZXMgPSAwO1xuICAgIHRoaXMuc3BlZWNoU2FtcGxlcyA9IDA7XG4gICAgdGhpcy5wb3N0U3BlZWNoU2FtcGxlcyA9IDA7XG4gICAgdGhpcy5sYXN0U3BlZWNoQWN0aXZlTWVzc2FnZVRpbWUgPSAwO1xuICB9XG4gIC8qKlxuICAgKiBQcm9jZXNzZXMgdGhlIGF1ZGlvIGRhdGEuXG4gICAqXG4gICAqIEByZXR1cm5zIEEgbGlzdCBvZiBtZXNzYWdlcyB0aGF0IG9jY3VycmVkIGR1cmluZyB0aGUgcHJvY2Vzc2luZy5cbiAgICovXG4gIGFzeW5jIHByb2Nlc3MoYXVkaW9EYXRhKSB7XG4gICAgdGhpcy5mcmFtZVF1ZXVlLmVucXVldWUoYXVkaW9EYXRhKTtcbiAgICBjb25zdCBtZXNzYWdlcyA9IFtdO1xuICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICBjb25zdCBmcmFtZSA9IHRoaXMuZnJhbWVRdWV1ZS5kZXF1ZXVlKCk7XG4gICAgICBpZiAoIWZyYW1lKSBicmVhaztcbiAgICAgIGF3YWl0IHRoaXMucHJvY2Vzc0ZyYW1lKGZyYW1lLCBtZXNzYWdlcyk7XG4gICAgfVxuICAgIHJldHVybiBtZXNzYWdlcztcbiAgfVxuICAvKipcbiAgICogU3RvcHMgdGhlIFZBRCBwcm9jZXNzb3IgYW5kIGhhbmRsZXMgdGhlIGxhc3QgdW5maW5pc2hlZCBzcGVlY2ggaWYgYW55LlxuICAgKi9cbiAgc3RvcCgpIHtcbiAgICBjb25zdCBtZXNzYWdlcyA9IFtdO1xuICAgIHRoaXMuaGFuZGxlQXVkaW9EYXRhKG1lc3NhZ2VzLCB0cnVlKTtcbiAgICByZXR1cm4gbWVzc2FnZXM7XG4gIH1cbiAgYXN5bmMgcHJvY2Vzc0ZyYW1lKGF1ZGlvRnJhbWUsIG1lc3NhZ2VzKSB7XG4gICAgY29uc3QgaXNTcGVlY2ggPSBhd2FpdCB0aGlzLnZhZC5wcm9jZXNzKGF1ZGlvRnJhbWUsIHRoaXMud2FzU3BlZWNoKTtcbiAgICB0aGlzLmJ1ZmZlci53cml0ZShhdWRpb0ZyYW1lKTtcbiAgICBpZiAoaXNTcGVlY2ggJiYgIXRoaXMud2FzU3BlZWNoKSB7XG4gICAgICB0aGlzLndhc1NwZWVjaCA9IHRydWU7XG4gICAgICBtZXNzYWdlcy5wdXNoKHsgdHlwZTogXCJzdGFydFwiIH0pO1xuICAgIH0gZWxzZSBpZiAoIWlzU3BlZWNoICYmIHRoaXMud2FzU3BlZWNoKSB7XG4gICAgICB0aGlzLndhc1NwZWVjaCA9IGZhbHNlO1xuICAgICAgbWVzc2FnZXMucHVzaCh7IHR5cGU6IFwiZW5kXCIgfSk7XG4gICAgfVxuICAgIGlmICh0aGlzLndhc1NwZWVjaCkge1xuICAgICAgdGhpcy5zcGVlY2hTYW1wbGVzICs9IHRoaXMucG9zdFNwZWVjaFNhbXBsZXMgKyBhdWRpb0ZyYW1lLmxlbmd0aDtcbiAgICAgIHRoaXMucG9zdFNwZWVjaFNhbXBsZXMgPSAwO1xuICAgIH0gZWxzZSBpZiAodGhpcy5zcGVlY2hTYW1wbGVzKSB7XG4gICAgICB0aGlzLnBvc3RTcGVlY2hTYW1wbGVzICs9IGF1ZGlvRnJhbWUubGVuZ3RoO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLnByZVNwZWVjaFNhbXBsZXMgKz0gYXVkaW9GcmFtZS5sZW5ndGg7XG4gICAgfVxuICAgIHRoaXMuaGFuZGxlQXVkaW9EYXRhKG1lc3NhZ2VzLCBmYWxzZSk7XG4gICAgcmV0dXJuO1xuICB9XG4gIGhhbmRsZUF1ZGlvRGF0YShtZXNzYWdlcywgZm9yY2UpIHtcbiAgICBpZiAodGhpcy5zcGVlY2hTYW1wbGVzIDwgTUlOX1NQRUVDSF9TQU1QTEVTKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGNvbnN0IG5vdyA9IHBlcmZvcm1hbmNlLm5vdygpO1xuICAgIGxldCBzcGVlY2hEYXRhO1xuICAgIGlmIChub3cgLSB0aGlzLmxhc3RTcGVlY2hBY3RpdmVNZXNzYWdlVGltZSA+IFNQRUVDSF9BQ1RJVkVfSU5URVJWQUxfTVMpIHtcbiAgICAgIHRoaXMubGFzdFNwZWVjaEFjdGl2ZU1lc3NhZ2VUaW1lID0gbm93O1xuICAgICAgc3BlZWNoRGF0YSA9IHRoaXMuZ2V0QXVkaW9EYXRhKG5vdyk7XG4gICAgICBtZXNzYWdlcy5wdXNoKHsgdHlwZTogXCJvbmdvaW5nXCIsIGRhdGE6IHNwZWVjaERhdGEgfSk7XG4gICAgfVxuICAgIGlmIChmb3JjZSB8fCB0aGlzLnBvc3RTcGVlY2hTYW1wbGVzID4gU1BFRUNIX1BBRF9TQU1QTEVTIHx8IHRoaXMuc3BlZWNoU2FtcGxlcyArIFNQRUVDSF9QQURfU0FNUExFUyAqIDIgPj0gTUFYX0FVRElPX0RVUkFUSU9OX1NBTVBMRVMpIHtcbiAgICAgIHNwZWVjaERhdGEgPSBzcGVlY2hEYXRhIHx8IHRoaXMuZ2V0QXVkaW9EYXRhKG5vdyk7XG4gICAgICBtZXNzYWdlcy5wdXNoKHsgdHlwZTogXCJhdmFpbGFibGVcIiwgZGF0YTogc3BlZWNoRGF0YSB9KTtcbiAgICAgIHRoaXMucmVzZXQoKTtcbiAgICB9XG4gIH1cbiAgZ2V0QXVkaW9EYXRhKG5vdykge1xuICAgIGNvbnN0IHBpY2tlZFNhbXBsZXMgPSBNYXRoLm1pbihcbiAgICAgIHRoaXMuYnVmZmVyLmxlbmd0aCxcbiAgICAgIE1hdGgubWluKHRoaXMucHJlU3BlZWNoU2FtcGxlcywgU1BFRUNIX1BBRF9TQU1QTEVTKSArIHRoaXMuc3BlZWNoU2FtcGxlcyArIHRoaXMucG9zdFNwZWVjaFNhbXBsZXNcbiAgICApO1xuICAgIGNvbnN0IGVuZEluZGV4ID0gdGhpcy5idWZmZXIubGVuZ3RoO1xuICAgIGNvbnN0IHN0YXJ0SW5kZXggPSBlbmRJbmRleCAtIHBpY2tlZFNhbXBsZXM7XG4gICAgY29uc3QgZW5kVGltZSA9IG5vdztcbiAgICBjb25zdCBzdGFydFRpbWUgPSBlbmRUaW1lIC0gcGlja2VkU2FtcGxlcyAvIFNBTVBMRV9SQVRFX01TO1xuICAgIHJldHVybiB7XG4gICAgICBzdGFydFRpbWUsXG4gICAgICBlbmRUaW1lLFxuICAgICAgYXVkaW9EYXRhOiB0aGlzLmJ1ZmZlci5yZWFkKHN0YXJ0SW5kZXgsIGVuZEluZGV4KSxcbiAgICAgIHNhbXBsZVJhdGU6IFNBTVBMRV9SQVRFXG4gICAgfTtcbiAgfVxuICByZXNldCgpIHtcbiAgICB0aGlzLnByZVNwZWVjaFNhbXBsZXMgPSAwO1xuICAgIHRoaXMuc3BlZWNoU2FtcGxlcyA9IDA7XG4gICAgdGhpcy5wb3N0U3BlZWNoU2FtcGxlcyA9IDA7XG4gICAgdGhpcy53YXNTcGVlY2ggPSBmYWxzZTtcbiAgfVxufTtcblxuLy8gc3JjL3Byb2Nlc3Nvci13b3JrZXIudHNcbmZ1bmN0aW9uIGV4cG9zZVByb2Nlc3NvcigpIHtcbiAgY29uc3QgcHJvY2Vzc29yID0gbmV3IFZBRFByb2Nlc3NvcigpO1xuICBleHBvc2UocHJvY2Vzc29yKTtcbn1cblxuLy8gc3JjL3ZhZC13ZWItd29ya2VyLmpzXG5leHBvc2VQcm9jZXNzb3IoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vad-web/dist/vad-web-worker.js\n");

/***/ })

};
;
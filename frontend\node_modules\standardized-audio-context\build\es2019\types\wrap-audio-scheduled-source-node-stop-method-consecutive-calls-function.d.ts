import { TNativeAudioBufferSourceNode } from './native-audio-buffer-source-node';
import { TNativeConstantSourceNode } from './native-constant-source-node';
import { TNativeContext } from './native-context';
import { TNativeOscillatorNode } from './native-oscillator-node';
export type TWrapAudioScheduledSourceNodeStopMethodConsecutiveCallsFunction = (nativeAudioScheduledSourceNode: TNativeAudioBufferSourceNode | TNativeConstantSourceNode | TNativeOscillatorNode, nativeContext: TNativeContext) => void;
//# sourceMappingURL=wrap-audio-scheduled-source-node-stop-method-consecutive-calls-function.d.ts.map
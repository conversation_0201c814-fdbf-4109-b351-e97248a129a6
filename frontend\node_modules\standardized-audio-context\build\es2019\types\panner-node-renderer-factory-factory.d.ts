import { TConnectAudioParamFunction } from './connect-audio-param-function';
import { TGetNativeAudioNodeFunction } from './get-native-audio-node-function';
import { TNativeChannelMergerNodeFactory } from './native-channel-merger-node-factory';
import { TNativeConstantSourceNodeFactory } from './native-constant-source-node-factory';
import { TNativeGainNodeFactory } from './native-gain-node-factory';
import { TNativeOfflineAudioContextConstructor } from './native-offline-audio-context-constructor';
import { TNativePannerNodeFactory } from './native-panner-node-factory';
import { TPannerNodeRendererFactory } from './panner-node-renderer-factory';
import { TRenderAutomationFunction } from './render-automation-function';
import { TRenderInputsOfAudioNodeFunction } from './render-inputs-of-audio-node-function';
import { TRenderNativeOfflineAudioContextFunction } from './render-native-offline-audio-context-function';
export type TPannerNodeRendererFactoryFactory = (connectAudioParam: TConnectAudioParamFunction, createNativeChannelMergerNode: TNativeChannelMergerNodeFactory, createNativeConstantSourceNode: TNativeConstantSourceNodeFactory, createNativeGainNode: TNativeGainNodeFactory, createNativePannerNode: TNativePannerNodeFactory, getNativeAudioNode: TGetNativeAudioNodeFunction, nativeOfflineAudioContextConstructor: null | TNativeOfflineAudioContextConstructor, renderAutomation: TRenderAutomationFunction, renderInputsOfAudioNode: TRenderInputsOfAudioNodeFunction, renderNativeOfflineAudioContext: TRenderNativeOfflineAudioContextFunction) => TPannerNodeRendererFactory;
//# sourceMappingURL=panner-node-renderer-factory-factory.d.ts.map
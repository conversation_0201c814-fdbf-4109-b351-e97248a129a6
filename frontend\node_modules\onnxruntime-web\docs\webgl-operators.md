## Operators Support Table

The following table shows [ai.onnx](https://github.com/onnx/onnx/blob/main/docs/Operators.md)  operators from which onnx opset version are currently supported by ONNX Runtime Web. For example, `4-6, 8+` means  ONNX Runtime Web currently support opset version 4 to 6, 8 and above.

See [Compatibility](../README.md#Compatibility) for a list of the supported platforms.

*This file is automatically generated from the  def files via [this script](../script/generate-operator-md.ts).  Do not modify directly.*

| Operator | WebGl Backend |
|:--------:|:-------------:|
| [Abs](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Abs) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Abs-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Abs-13) |
| [Acos](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Acos) | [7-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Acos-7), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Acos-22) |
| [Acosh](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Acosh) |  |
| [Add](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Add) | [7-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Add-7), [13](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Add-13), [14+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Add-14) |
| [AffineGrid](https://github.com/onnx/onnx/blob/main/docs/Operators.md#AffineGrid) |  |
| [And](https://github.com/onnx/onnx/blob/main/docs/Operators.md#And) | [7+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#And-7) |
| [ArgMax](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ArgMax) |  |
| [ArgMin](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ArgMin) |  |
| [Asin](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Asin) | [7-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Asin-7), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Asin-22) |
| [Asinh](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Asinh) |  |
| [Atan](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Atan) | [7-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Atan-7), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Atan-22) |
| [Atanh](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Atanh) |  |
| [AveragePool](https://github.com/onnx/onnx/blob/main/docs/Operators.md#AveragePool) | [7-9](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#AveragePool-7), [10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#AveragePool-10), [11-18](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#AveragePool-11), [19-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#AveragePool-19), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#AveragePool-22) |
| [BatchNormalization](https://github.com/onnx/onnx/blob/main/docs/Operators.md#BatchNormalization) | [7-8](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#BatchNormalization-7), [9-13](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#BatchNormalization-9), [14](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#BatchNormalization-14), [15+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#BatchNormalization-15) |
| [Bernoulli](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Bernoulli) |  |
| [BitShift](https://github.com/onnx/onnx/blob/main/docs/Operators.md#BitShift) |  |
| [BitwiseAnd](https://github.com/onnx/onnx/blob/main/docs/Operators.md#BitwiseAnd) |  |
| [BitwiseNot](https://github.com/onnx/onnx/blob/main/docs/Operators.md#BitwiseNot) |  |
| [BitwiseOr](https://github.com/onnx/onnx/blob/main/docs/Operators.md#BitwiseOr) |  |
| [BitwiseXor](https://github.com/onnx/onnx/blob/main/docs/Operators.md#BitwiseXor) |  |
| [BlackmanWindow](https://github.com/onnx/onnx/blob/main/docs/Operators.md#BlackmanWindow) |  |
| [Cast](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Cast) | [6-8](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Cast-6), [9-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Cast-9), [13-18](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Cast-13), [19-20](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Cast-19), [21+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Cast-21) |
| [CastLike](https://github.com/onnx/onnx/blob/main/docs/Operators.md#CastLike) |  |
| [Ceil](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Ceil) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Ceil-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Ceil-13) |
| [Celu](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Celu) |  |
| [CenterCropPad](https://github.com/onnx/onnx/blob/main/docs/Operators.md#CenterCropPad) |  |
| [Clip](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Clip) | [6-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Clip-6), [11](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Clip-11), [12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Clip-12), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Clip-13) |
| [Col2Im](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Col2Im) |  |
| [Compress](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Compress) |  |
| [Concat](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Concat) | [4-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Concat-4), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Concat-11), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Concat-13) |
| [ConcatFromSequence](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ConcatFromSequence) |  |
| [Constant](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Constant) |  |
| [ConstantOfShape](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ConstantOfShape) |  |
| [Conv](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Conv) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Conv-1), [11-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Conv-11), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Conv-22) |
| [ConvInteger](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ConvInteger) |  |
| [ConvTranspose](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ConvTranspose) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ConvTranspose-1), [11-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ConvTranspose-11), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ConvTranspose-22) |
| [Cos](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Cos) | [7-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Cos-7), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Cos-22) |
| [Cosh](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Cosh) |  |
| [CumSum](https://github.com/onnx/onnx/blob/main/docs/Operators.md#CumSum) |  |
| [DFT](https://github.com/onnx/onnx/blob/main/docs/Operators.md#DFT) |  |
| [DeformConv](https://github.com/onnx/onnx/blob/main/docs/Operators.md#DeformConv) |  |
| [DepthToSpace](https://github.com/onnx/onnx/blob/main/docs/Operators.md#DepthToSpace) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#DepthToSpace-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#DepthToSpace-11), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#DepthToSpace-13) |
| [DequantizeLinear](https://github.com/onnx/onnx/blob/main/docs/Operators.md#DequantizeLinear) |  |
| [Det](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Det) |  |
| [Div](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Div) | [7-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Div-7), [13](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Div-13), [14+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Div-14) |
| [Dropout](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Dropout) | [7-9](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Dropout-7), [10-11](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Dropout-10), [12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Dropout-12), [13-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Dropout-13), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Dropout-22) |
| [DynamicQuantizeLinear](https://github.com/onnx/onnx/blob/main/docs/Operators.md#DynamicQuantizeLinear) |  |
| [Einsum](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Einsum) |  |
| [Elu](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Elu) | [6-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Elu-6), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Elu-22) |
| [Equal](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Equal) | [7-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Equal-7), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Equal-11), [13-18](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Equal-13), [19+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Equal-19) |
| [Erf](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Erf) |  |
| [Exp](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Exp) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Exp-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Exp-13) |
| [Expand](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Expand) |  |
| [EyeLike](https://github.com/onnx/onnx/blob/main/docs/Operators.md#EyeLike) |  |
| [Flatten](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Flatten) | [1-8](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Flatten-1), [9-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Flatten-9), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Flatten-11), [13-20](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Flatten-13), [21+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Flatten-21) |
| [Floor](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Floor) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Floor-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Floor-13) |
| [GRU](https://github.com/onnx/onnx/blob/main/docs/Operators.md#GRU) |  |
| [Gather](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Gather) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Gather-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Gather-11), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Gather-13) |
| [GatherElements](https://github.com/onnx/onnx/blob/main/docs/Operators.md#GatherElements) |  |
| [GatherND](https://github.com/onnx/onnx/blob/main/docs/Operators.md#GatherND) |  |
| [Gelu](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Gelu) |  |
| [Gemm](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Gemm) | [7-8](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Gemm-7), [9-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Gemm-9), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Gemm-11), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Gemm-13) |
| [GlobalAveragePool](https://github.com/onnx/onnx/blob/main/docs/Operators.md#GlobalAveragePool) | [1-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#GlobalAveragePool-1), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#GlobalAveragePool-22) |
| [GlobalLpPool](https://github.com/onnx/onnx/blob/main/docs/Operators.md#GlobalLpPool) |  |
| [GlobalMaxPool](https://github.com/onnx/onnx/blob/main/docs/Operators.md#GlobalMaxPool) | [1-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#GlobalMaxPool-1), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#GlobalMaxPool-22) |
| [Greater](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Greater) | [7-8](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Greater-7), [9-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Greater-9), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Greater-13) |
| [GreaterOrEqual](https://github.com/onnx/onnx/blob/main/docs/Operators.md#GreaterOrEqual) |  |
| [GridSample](https://github.com/onnx/onnx/blob/main/docs/Operators.md#GridSample) |  |
| [GroupNormalization](https://github.com/onnx/onnx/blob/main/docs/Operators.md#GroupNormalization) |  |
| [HammingWindow](https://github.com/onnx/onnx/blob/main/docs/Operators.md#HammingWindow) |  |
| [HannWindow](https://github.com/onnx/onnx/blob/main/docs/Operators.md#HannWindow) |  |
| [HardSigmoid](https://github.com/onnx/onnx/blob/main/docs/Operators.md#HardSigmoid) |  |
| [HardSwish](https://github.com/onnx/onnx/blob/main/docs/Operators.md#HardSwish) |  |
| [Hardmax](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Hardmax) |  |
| [Identity](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Identity) | [1-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Identity-1), [13](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Identity-13), [14-15](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Identity-14), [16-18](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Identity-16), [19-20](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Identity-19), [21+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Identity-21) |
| [If](https://github.com/onnx/onnx/blob/main/docs/Operators.md#If) |  |
| [ImageDecoder](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ImageDecoder) |  |
| [InstanceNormalization](https://github.com/onnx/onnx/blob/main/docs/Operators.md#InstanceNormalization) | [6-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#InstanceNormalization-6), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#InstanceNormalization-22) |
| [IsInf](https://github.com/onnx/onnx/blob/main/docs/Operators.md#IsInf) |  |
| [IsNaN](https://github.com/onnx/onnx/blob/main/docs/Operators.md#IsNaN) |  |
| [LRN](https://github.com/onnx/onnx/blob/main/docs/Operators.md#LRN) | [1-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#LRN-1), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#LRN-13) |
| [LSTM](https://github.com/onnx/onnx/blob/main/docs/Operators.md#LSTM) |  |
| [LayerNormalization](https://github.com/onnx/onnx/blob/main/docs/Operators.md#LayerNormalization) |  |
| [LeakyRelu](https://github.com/onnx/onnx/blob/main/docs/Operators.md#LeakyRelu) | [6-15](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#LeakyRelu-6), [16+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#LeakyRelu-16) |
| [Less](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Less) | [7-8](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Less-7), [9-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Less-9), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Less-13) |
| [LessOrEqual](https://github.com/onnx/onnx/blob/main/docs/Operators.md#LessOrEqual) |  |
| [Log](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Log) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Log-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Log-13) |
| [LogSoftmax](https://github.com/onnx/onnx/blob/main/docs/Operators.md#LogSoftmax) |  |
| [Loop](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Loop) |  |
| [LpNormalization](https://github.com/onnx/onnx/blob/main/docs/Operators.md#LpNormalization) |  |
| [LpPool](https://github.com/onnx/onnx/blob/main/docs/Operators.md#LpPool) |  |
| [MatMul](https://github.com/onnx/onnx/blob/main/docs/Operators.md#MatMul) | [1-8](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#MatMul-1), [9-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#MatMul-9), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#MatMul-13) |
| [MatMulInteger](https://github.com/onnx/onnx/blob/main/docs/Operators.md#MatMulInteger) |  |
| [Max](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Max) |  |
| [MaxPool](https://github.com/onnx/onnx/blob/main/docs/Operators.md#MaxPool) | [1-7](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#MaxPool-1), [8-9](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#MaxPool-8), [10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#MaxPool-10), [11](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#MaxPool-11), [12-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#MaxPool-12), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#MaxPool-22) |
| [MaxRoiPool](https://github.com/onnx/onnx/blob/main/docs/Operators.md#MaxRoiPool) |  |
| [MaxUnpool](https://github.com/onnx/onnx/blob/main/docs/Operators.md#MaxUnpool) |  |
| [Mean](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Mean) |  |
| [MeanVarianceNormalization](https://github.com/onnx/onnx/blob/main/docs/Operators.md#MeanVarianceNormalization) |  |
| [MelWeightMatrix](https://github.com/onnx/onnx/blob/main/docs/Operators.md#MelWeightMatrix) |  |
| [Min](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Min) |  |
| [Mish](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Mish) |  |
| [Mod](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Mod) |  |
| [Mul](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Mul) | [7-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Mul-7), [13](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Mul-13), [14+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Mul-14) |
| [Multinomial](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Multinomial) |  |
| [Neg](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Neg) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Neg-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Neg-13) |
| [NegativeLogLikelihoodLoss](https://github.com/onnx/onnx/blob/main/docs/Operators.md#NegativeLogLikelihoodLoss) |  |
| [NonMaxSuppression](https://github.com/onnx/onnx/blob/main/docs/Operators.md#NonMaxSuppression) |  |
| [NonZero](https://github.com/onnx/onnx/blob/main/docs/Operators.md#NonZero) |  |
| [Not](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Not) | [1+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Not-1) |
| [OneHot](https://github.com/onnx/onnx/blob/main/docs/Operators.md#OneHot) |  |
| [Optional](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Optional) |  |
| [OptionalGetElement](https://github.com/onnx/onnx/blob/main/docs/Operators.md#OptionalGetElement) |  |
| [OptionalHasElement](https://github.com/onnx/onnx/blob/main/docs/Operators.md#OptionalHasElement) |  |
| [Or](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Or) | [7+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Or-7) |
| [PRelu](https://github.com/onnx/onnx/blob/main/docs/Operators.md#PRelu) | [7-8](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#PRelu-7), [9-15](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#PRelu-9), [16+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#PRelu-16) |
| [Pad](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Pad) | [2-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pad-2), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pad-11), [13-17](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pad-13), [18](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pad-18), [19-20](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pad-19), [21+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pad-21) |
| [Pow](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Pow) | [7-11](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pow-7), [12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pow-12), [13-14](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pow-13), [15+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Pow-15) |
| [QLinearConv](https://github.com/onnx/onnx/blob/main/docs/Operators.md#QLinearConv) |  |
| [QLinearMatMul](https://github.com/onnx/onnx/blob/main/docs/Operators.md#QLinearMatMul) |  |
| [QuantizeLinear](https://github.com/onnx/onnx/blob/main/docs/Operators.md#QuantizeLinear) |  |
| [RNN](https://github.com/onnx/onnx/blob/main/docs/Operators.md#RNN) |  |
| [RandomNormal](https://github.com/onnx/onnx/blob/main/docs/Operators.md#RandomNormal) |  |
| [RandomNormalLike](https://github.com/onnx/onnx/blob/main/docs/Operators.md#RandomNormalLike) |  |
| [RandomUniform](https://github.com/onnx/onnx/blob/main/docs/Operators.md#RandomUniform) |  |
| [RandomUniformLike](https://github.com/onnx/onnx/blob/main/docs/Operators.md#RandomUniformLike) |  |
| [Range](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Range) |  |
| [Reciprocal](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Reciprocal) |  |
| [ReduceL1](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceL1) |  |
| [ReduceL2](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceL2) |  |
| [ReduceLogSum](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceLogSum) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceLogSum-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceLogSum-11), [13-17](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceLogSum-13), [18+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceLogSum-18) |
| [ReduceLogSumExp](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceLogSumExp) |  |
| [ReduceMax](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceMax) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMax-1), [11](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMax-11), [12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMax-12), [13-17](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMax-13), [18-19](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMax-18), [20+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMax-20) |
| [ReduceMean](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceMean) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMean-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMean-11), [13-17](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMean-13), [18+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMean-18) |
| [ReduceMin](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceMin) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMin-1), [11](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMin-11), [12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMin-12), [13-17](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMin-13), [18-19](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMin-18), [20+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceMin-20) |
| [ReduceProd](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceProd) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceProd-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceProd-11), [13-17](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceProd-13), [18+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceProd-18) |
| [ReduceSum](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceSum) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceSum-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceSum-11) |
| [ReduceSumSquare](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReduceSumSquare) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceSumSquare-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceSumSquare-11), [13-17](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceSumSquare-13), [18+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#ReduceSumSquare-18) |
| [RegexFullMatch](https://github.com/onnx/onnx/blob/main/docs/Operators.md#RegexFullMatch) |  |
| [Relu](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Relu) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Relu-6), [13](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Relu-13), [14+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Relu-14) |
| [Reshape](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Reshape) | [5-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Reshape-5), [13](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Reshape-13), [14-18](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Reshape-14), [19-20](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Reshape-19), [21+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Reshape-21) |
| [Resize](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Resize) | [10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Resize-10), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Resize-11), [13-17](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Resize-13), [18](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Resize-18), [19+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Resize-19) |
| [ReverseSequence](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ReverseSequence) |  |
| [RoiAlign](https://github.com/onnx/onnx/blob/main/docs/Operators.md#RoiAlign) |  |
| [Round](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Round) |  |
| [STFT](https://github.com/onnx/onnx/blob/main/docs/Operators.md#STFT) |  |
| [Scan](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Scan) |  |
| [Scatter](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Scatter) |  |
| [ScatterElements](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ScatterElements) |  |
| [ScatterND](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ScatterND) |  |
| [Selu](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Selu) |  |
| [SequenceAt](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SequenceAt) |  |
| [SequenceConstruct](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SequenceConstruct) |  |
| [SequenceEmpty](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SequenceEmpty) |  |
| [SequenceErase](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SequenceErase) |  |
| [SequenceInsert](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SequenceInsert) |  |
| [SequenceLength](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SequenceLength) |  |
| [SequenceMap](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SequenceMap) |  |
| [Shape](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Shape) | [1-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Shape-1), [13-14](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Shape-13), [15-18](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Shape-15), [19-20](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Shape-19), [21+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Shape-21) |
| [Shrink](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Shrink) |  |
| [Sigmoid](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Sigmoid) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sigmoid-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sigmoid-13) |
| [Sign](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Sign) |  |
| [Sin](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Sin) | [7-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sin-7), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sin-22) |
| [Sinh](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Sinh) |  |
| [Size](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Size) |  |
| [Slice](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Slice) | [1-9](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Slice-1), [10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Slice-10), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Slice-11), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Slice-13) |
| [Softmax](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Softmax) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Softmax-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Softmax-11), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Softmax-13) |
| [SoftmaxCrossEntropyLoss](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SoftmaxCrossEntropyLoss) |  |
| [Softplus](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Softplus) |  |
| [Softsign](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Softsign) |  |
| [SpaceToDepth](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SpaceToDepth) |  |
| [Split](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Split) | [2-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Split-2), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Split-11) |
| [SplitToSequence](https://github.com/onnx/onnx/blob/main/docs/Operators.md#SplitToSequence) |  |
| [Sqrt](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Sqrt) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sqrt-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sqrt-13) |
| [Squeeze](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Squeeze) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Squeeze-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Squeeze-11), [13-20](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Squeeze-13), [21+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Squeeze-21) |
| [StringConcat](https://github.com/onnx/onnx/blob/main/docs/Operators.md#StringConcat) |  |
| [StringNormalizer](https://github.com/onnx/onnx/blob/main/docs/Operators.md#StringNormalizer) |  |
| [StringSplit](https://github.com/onnx/onnx/blob/main/docs/Operators.md#StringSplit) |  |
| [Sub](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Sub) | [7-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sub-7), [13](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sub-13), [14+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sub-14) |
| [Sum](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Sum) | [6-7](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sum-6), [8-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sum-8), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Sum-13) |
| [Tan](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Tan) | [7-21](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Tan-7), [22+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Tan-22) |
| [Tanh](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Tanh) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Tanh-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Tanh-13) |
| [TfIdfVectorizer](https://github.com/onnx/onnx/blob/main/docs/Operators.md#TfIdfVectorizer) |  |
| [ThresholdedRelu](https://github.com/onnx/onnx/blob/main/docs/Operators.md#ThresholdedRelu) |  |
| [Tile](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Tile) | [6-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Tile-6), [13+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Tile-13) |
| [TopK](https://github.com/onnx/onnx/blob/main/docs/Operators.md#TopK) |  |
| [Transpose](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Transpose) | [1-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Transpose-1), [13-20](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Transpose-13), [21+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Transpose-21) |
| [Trilu](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Trilu) |  |
| [Unique](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Unique) |  |
| [Unsqueeze](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Unsqueeze) | [1-10](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Unsqueeze-1), [11-12](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Unsqueeze-11), [13-20](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Unsqueeze-13), [21+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Unsqueeze-21) |
| [Upsample](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Upsample) | [7-8](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Upsample-7), [9](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Upsample-9) |
| [Where](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Where) |  |
| [Xor](https://github.com/onnx/onnx/blob/main/docs/Operators.md#Xor) | [7+](https://github.com/onnx/onnx/blob/main/docs/Changelog.md#Xor-7) |

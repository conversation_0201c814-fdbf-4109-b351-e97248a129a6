{"version": 3, "file": "tokenizers.d.ts", "sourceRoot": "", "sources": ["../src/tokenizers.js"], "names": [], "mappings": "AAmNA;;;;;;;;;;;;;GAaG;AACH,oCAHW,MAAM,GAAC,MAAM,GACX,OAAO,CAanB;;KAxO0E,GAC1E;UAA0B,GAAE;;AA0T7B;;;;GAIG;AACH;IA0BI;;;;;;OAMG;IACH,wCAJc,GAAC,EAAA,GACF,cAAc,CAkC1B;IA/DD;;;OAGG;IACH,yBAmBC;IAjBG,YAAoB;IAEpB,uBAAuB;IACvB,OADW,MAAM,EAAE,CACJ;IAEf;;;OAGG;IACH,eAFU,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAEC;IAE9B,kBAA6B;IAC7B,eAA0B;IAC1B,wBAAmC;IAEnC,uFAAuF;IACvF,UADW,OAAO,CAC2B;IA2CjD;;;;OAIG;IACH,cAHW,MAAM,EAAE,GACN,MAAM,EAAE,CASpB;IAED;;;;;OAKG;IACH,eAJW,MAAM,EAAE,GACN,MAAM,EAAE,CAKpB;IAED;;;;OAIG;IACH,8BAHW,MAAM,EAAE,GACN,MAAM,EAAE,CAIpB;IAED;;;;OAIG;IACH,2BAHW,MAAM,EAAE,GAAC,MAAM,EAAE,GACf,MAAM,EAAE,CAIpB;CACJ;;KA3a0E,GAC1E;UAA0B,GAAE;;AA4/E7B;;;;GAIG;AAEH;IA0II;;;;;;;;OAQG;IACH,sDANW,MAAM,kFACN,0BAA0B,GAGxB,OAAO,CAAC,mBAAmB,CAAC,CAsBxC;IAnKD;;;;OAIG;IACH,sDAqGC;IA7GD,+BAA8B;IAE9B,qBAAuB;IASnB,YAA6B;IAG7B,uBAAiE;IACjE,4BAAyE;IACzE,sBAA4E;IAC5E,8BAA4E;IAC5E,iBAAwD;IAGxD,sBAAwB;IACxB,0BAAyB;IAEzB,2BAA2B;IAC3B,cADW,UAAU,EAAE,CACD;IAetB,+BAAgF;IAehF,0CAEC;IAED,sCAAsC;IACtC,kBADW,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CACyC;IAG3E,mBAA6C;IAC7C,sBAAkE;IAElE,kBAAwD;IACxD,qBAAgE;IAEhE,kBAA2C;IAC3C,qBAAgE;IAEhE,kBAA2C;IAC3C,qBAAgE;IAEhE,kBAA2C;IAC3C,qBAAgE;IAEhE,kBAA2C;IAC3C,qBAAgE;IAEhE,sBAAwD;IAExD,6HAA6H;IAC7H,cADW,OAAO,CAC8B;IAEhD,kCAAwF;IACxF,oCAA6F;IAM7F,gBAAmB;IAEnB,mBAA0D;IAa1D,wCAAyC;IAG7C;;;;;;OAMG;IACH,iBAiBC;IAiCD;;;;;;;OAOG;IAEH;;;;;;;;;;;;OAYG;IACH,YAXW,MAAM,GAAC,MAAM,EAAE,8GAEvB;QAAkC,SAAS,GAAnC,MAAM,GAAC,MAAM,EAAE;QACgB,OAAO,GAAtC,OAAO,GAAC,YAAY;QACF,kBAAkB,GAApC,OAAO;QACW,UAAU,GAA5B,OAAO;QACU,UAAU,GAA3B,MAAM;QACY,aAAa,GAA/B,OAAO;QACW,qBAAqB,GAAvC,OAAO;KACf;;;;;;;;;;;;;MAgKF;IAED;;;;;OAKG;IACH,mBAHW,MAAM,GAAC,IAAI,GACT,MAAM,EAAE,GAAC,IAAI,CAsDzB;IAED;;;;;;;;;;OAUG;IACH,qBAkBC;IAED;;;;;;;OAOG;IACH,uBANW,MAAM,kCAEd;QAAyB,IAAI,GAArB,MAAM;QACY,kBAAkB,GAApC,OAAO;KACf,GAAU;QAAC,MAAM,EAAE,MAAM,EAAE,CAAC;QAAC,cAAc,CAAC,EAAE,MAAM,EAAE,CAAA;KAAC,CAYzD;IAED;;;;;;;OAOG;IACH,eANW,MAAM,kCAEd;QAAyB,IAAI,GAArB,MAAM;QACY,kBAAkB,GAApC,OAAO;KACf,GAAU,MAAM,EAAE,CAOpB;IAED;;;;;;;;;OASG;IACH,aAPW,MAAM,8DAEd;QAAyB,SAAS,GAA1B,MAAM;QACY,kBAAkB,GAApC,OAAO;QACW,qBAAqB,GAAvC,OAAO;KACf,GAAU,MAAM,EAAE,CAYpB;IAED;;;;;OAKG;IACH,oBAJW,MAAM,EAAE,EAAE,GAAC,MAAM,sBAEf,MAAM,EAAE,CAOpB;IAED;;;;;;;;;;OAUG;IACH,kBARW,MAAM,EAAE,GAAC,MAAM,EAAE,GAAC,MAAM,gBAEhC;QAA8B,mBAAmB,GAAzC,OAAO;QACe,4BAA4B,GAAlD,OAAO;KAEf,GAAU,MAAM,CAgBlB;IAED;;;;;;;;OAQG;IACH,yBAPW,MAAM,EAAE,GAAC,MAAM,EAAE,0DAEzB;QAA8B,mBAAmB,GAAzC,OAAO;QACe,4BAA4B,GAAlD,OAAO;KAEf,GAAU,MAAM,CAiClB;IAED;;;;;;;;;;;;;;;;;OAiBG;IACH,8CAZG;QAAyB,aAAa,GAA9B,MAAM;QAIa,KAAK,GAAxB,KAAQ;KAMhB,GAAU,MAAM,CA0ClB;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAyDG;IACH,kCA/BW,OAAO,EAAE,mKAGjB;QAAyB,aAAa,GAA9B,MAAM;QAEa,KAAK,GAAxB,KAAQ;QAM2B,SAAS,GAA5C,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;QAMN,qBAAqB,GAAvC,OAAO;QAIW,QAAQ,GAA1B,OAAO;QACW,OAAO,GAAzB,OAAO;QACW,UAAU,GAA5B,OAAO;QACU,UAAU,GAA3B,MAAM;QAEY,aAAa,GAA/B,OAAO;QACW,WAAW,GAA7B,OAAO;QACU,gBAAgB;KACzC,GAAU,MAAM,GAAG,MAAM,GAAG,MAAM,EAAE,GAAE,MAAM,EAAE,EAAE;;;;;;;;;;;;;KAAc,CA4DhE;CACJ;AAED;;;GAGG;AACH;CAEC;AACD;;;GAGG;AACH;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAEC;AACD;CAAgE;AAChE;CAA+D;AAC/D;IAGI,sDAGC;CACJ;AACD;CAEC;AAED;CAAwD;AACxD;CAA0D;AAC1D;CAA0D;AAC1D;IACI,sDAMC;IAHG,sBAA0C;IAC1C,sBAAiF;IACjF,+BAA2B;IAG/B;;;;;;OAMG;IACH,sCALW,MAAM,GAAC,MAAM,EAAE,qDAOzB;CACJ;AACD;CAAwD;AAExD;CAA6D;AAE7D;CAA2D;AAI3D;IAII,sDAaC;IAVG,YAA4C;CA+BnD;AACD;CAA+D;AAE/D;CAAgE;AAChE;CAA2D;AAE3D;CAA4D;AAE5D;CAA6D;AAE7D;CAAyD;AAEzD;CAA2D;AAE3D;CAA2D;AAE3D;CAA2D;AAqD3D;;;;;;;;;;;;GAYG;AACH;IAEI,sDAMC;IAHG,sBAA+C;IAC/C,sBAAiF;IACjF,+BAA2B;IAG/B;;;;;;OAMG;IACH,sCALW,MAAM,GAAC,MAAM,EAAE,qDAOzB;CACJ;AAED;;;;;;;;;GASG;AACH;IACI,sDAQC;IALG,sBAAuC;IACvC,sBAE6B;IAC7B,kCAAoC;IAGxC;;;;;;OAMG;IACH,sCALW,MAAM,GAAC,MAAM,EAAE,qDAOzB;CACJ;AAED;;;GAGG;AACH;IAEI,8BAEC;IAED;;;;;OAKG;IACH,uBAJW,KAAK,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAC;QAAC,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAC,CAAC,uFAEtE,KAAK,CAAC,MAAM,GAAC;QAAC,MAAM,CAAC,EAAE,SAAS,GAAC,KAAK,CAAC;YAAC,QAAQ,EAAE,MAAM,GAAC,IAAI,CAAC;YAAC,SAAS,EAAE,KAAK,CAAC,MAAM,GAAC,IAAI,CAAC,CAAC;YAAC,IAAI,EAAE,MAAM,CAAA;SAAC,CAAC,CAAA;KAAC,CAAC,CAyS1H;IAED;;;;;;OAMG;IACH,kCAsGC;IAED,eAAe;IACf,8BAgBC;IAED;;;;;;;;;OASG;IACH,+BAaC;IAwBD;;;;OAIG;IACH,6BAqBC;IAED;;;;;OAKG;IACH,6BAqCC;IAED;;;;OAIG;IACH,4BAoCC;IAED;;;;;;;;OAQG;IACH,0BA+CC;CACJ;AACD;CAA6D;AAC7D;CAA0D;AAC1D;CAA4D;AAE5D;;;GAGG;AACH;IASQ,sBAAqC;IAErC,mCAEC;IAKL;;;;;;;OAOG;IACH,mBAHW,MAAM,GAAC,IAAI,SAsBrB;CAEJ;AAED;CAAiE;AAEjE;CAAgE;AAChE;CAAqE;AAErE;CAA8D;AAE9D;CAA4D;AAE5D;IAEI,sDAKC;CACJ;AAED;CAA4D;AAE5D;CAA4D;AAE5D;CAA+D;AAE/D;;;;;;GAMG;AACH;IACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAkDC;IAGD;;;;;;;;;;;;;;OAcG;IACH,sDATW,MAAM,kFAKN,0BAA0B,GAExB,OAAO,CAAC,mBAAmB,CAAC,CA6BxC;CACJ;;;;;;;;aAvxIa,OAAO;;yCACR,OAAO,gBAAgB,EAAE,iBAAiB,GAAG,mBAAmB;;;;;WAymB/D,MAAM;;;;UACN,MAAM;;;;YACN,MAAM;;;;WACN,OAAO;;;;WACP,OAAO;;qCA00BR,SAAS,GAAC,UAAU,GAAC,oBAAoB,GAAC,gBAAgB,GAAC,YAAY;;;;;YAuGtE,MAAM,EAAE;;;;qBACR,MAAM,EAAE;;;;;;eAMR,MAAM,EAAE;;;;oBACR,MAAM,EAAE;;;;qBACR,MAAM,EAAE;;;;;;UAm6BR,MAAM;;;;aACN,MAAM;;;KAhgFuD,GAC1E;UAA0B,GAAE;;AAu9B7B;;;GAGG;AACH;IASI;;;;;;OAMG;IACH,gCAHa,UAAU,CAiCtB;IA7CD;;OAEG;IACH,yBAGC;IADG,YAAoB;IA0CxB;;;;;;OAMG;IACH,gBAJW,MAAM,GACJ,MAAM,CAKlB;IAED;;;;OAIG;IACH,YAHW,MAAM,GACJ,MAAM,CAIlB;CAEJ;;KAhiC0E,GAC1E;UAA0B,GAAE;;AAi0C7B;;;;GAIG;AACH;IACI;;;;;;;KAOC;IACD,gCAHW,YAAY,CA+BtB;IAED;;;;;;;;OAQG;IACH,wBALW,MAAM,kBAEJ,MAAM,EAAE,CAKpB;IAED;;;;;OAKG;IACH,mBAJW,MAAM,GAAC,MAAM,EAAE,kBAEb,MAAM,EAAE,CAOpB;IAED;;;;;OAKG;IACH,YAJW,MAAM,GAAC,MAAM,EAAE,kBAEb,MAAM,EAAE,CAIpB;CACJ;;KAj5C0E,GAC1E;UAA0B,GAAE;;AAglD7B;;;;GAIG;AAGH;;;;;GAKG;AAGH;;GAEG;AACH;IAUI;;;;;;OAMG;IACH,gCAHa,aAAa,CAsBzB;IAlCD;;OAEG;IACH,yBAGC;IADG,YAAoB;IA+BxB;;;;;;;OAOG;IACH,qCAJc,GAAC,EAAA,GACF,mBAAmB,CAK/B;IAED;;;;;OAKG;IACH,8BAHc,GAAC,EAAA,GACF,mBAAmB,CAI/B;CACJ;;KA9pD0E,GAC1E;UAA0B,GAAE;;AAi0D7B;;;GAGG;AACH;IAiBI;;;;;;KAMC;IACD,gCAHW,OAAO,CAgCjB;IAnDD;;;;MAIE;IACF,yBAQC;IANG,YAAoB;IAEpB,2BAA2B;IAC3B,cADW,UAAU,EAAE,CACD;IACtB,wBAA8B;IAC9B,kBAAuC;IAyC3C;;;;;MAKE;IACF,cAHU,MAAM,EAAE,GACN,MAAM,CAIjB;IAED;;;;MAIE;IACF,eAHU,MAAM,EAAE,GACN,MAAM,CAIjB;IAED;;;;;;OAMG;IACH,qBAJW,MAAM,EAAE,GACN,MAAM,EAAE,CAKpB;CAEJ;AA7nDD;;;;;GAKG;AACH;IACI;;;;;;;;;;OAUG;IACH,oBARG;QAAuB,OAAO,EAAtB,MAAM;QACS,EAAE,EAAjB,MAAM;QACW,WAAW,GAA5B,OAAO;QACU,MAAM,GAAvB,OAAO;QACU,MAAM,GAAvB,OAAO;QACU,UAAU,GAA3B,OAAO;QACU,OAAO,GAAxB,OAAO;KACjB,EASA;IAPG,gBAA6B;IAC7B,WAAmB;IACnB,qBAA8C;IAC9C,gBAAoC;IACpC,gBAAoC;IACpC,iBAAsC;IACtC,oBAA2C;CAElD;mCA/QM,4BAA4B;uBARZ,mBAAmB"}
(()=>{var e,t,s={"node:fs":e=>{"use strict";e.exports=require("node:fs")},"node:path":e=>{"use strict";e.exports=require("node:path")},"node:url":e=>{"use strict";e.exports=require("node:url")},"onnxruntime-common":e=>{"use strict";e.exports=require("onnxruntime-common")},"onnxruntime-node":e=>{"use strict";e.exports=require("onnxruntime-node")},sharp:e=>{"use strict";e.exports=require("sharp")},"?8b6b":()=>{},"./node_modules/@huggingface/jinja/dist/index.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Environment:()=>te,Interpreter:()=>se,Template:()=>_e,parse:()=>N,tokenize:()=>d});var r=Object.freeze({Text:"Text",NumericLiteral:"NumericLiteral",BooleanLiteral:"BooleanLiteral",NullLiteral:"NullLiteral",StringLiteral:"StringLiteral",Identifier:"Identifier",Equals:"Equals",OpenParen:"OpenParen",CloseParen:"CloseParen",OpenStatement:"OpenStatement",CloseStatement:"CloseStatement",OpenExpression:"OpenExpression",CloseExpression:"CloseExpression",OpenSquareBracket:"OpenSquareBracket",CloseSquareBracket:"CloseSquareBracket",OpenCurlyBracket:"OpenCurlyBracket",CloseCurlyBracket:"CloseCurlyBracket",Comma:"Comma",Dot:"Dot",Colon:"Colon",Pipe:"Pipe",CallOperator:"CallOperator",AdditiveBinaryOperator:"AdditiveBinaryOperator",MultiplicativeBinaryOperator:"MultiplicativeBinaryOperator",ComparisonBinaryOperator:"ComparisonBinaryOperator",UnaryOperator:"UnaryOperator",Set:"Set",If:"If",For:"For",In:"In",Is:"Is",NotIn:"NotIn",Else:"Else",EndSet:"EndSet",EndIf:"EndIf",ElseIf:"ElseIf",EndFor:"EndFor",And:"And",Or:"Or",Not:"UnaryOperator",Macro:"Macro",EndMacro:"EndMacro",Break:"Break",Continue:"Continue"}),o=Object.freeze({set:r.Set,for:r.For,in:r.In,is:r.Is,if:r.If,else:r.Else,endset:r.EndSet,endif:r.EndIf,elif:r.ElseIf,endfor:r.EndFor,and:r.And,or:r.Or,not:r.Not,"not in":r.NotIn,macro:r.Macro,endmacro:r.EndMacro,break:r.Break,continue:r.Continue,true:r.BooleanLiteral,false:r.BooleanLiteral,none:r.NullLiteral,True:r.BooleanLiteral,False:r.BooleanLiteral,None:r.NullLiteral}),n=class{constructor(e,t){this.value=e,this.type=t}};function i(e){return/\w/.test(e)}function a(e){return/[0-9]/.test(e)}var l=[["{%",r.OpenStatement],["%}",r.CloseStatement],["{{",r.OpenExpression],["}}",r.CloseExpression],["(",r.OpenParen],[")",r.CloseParen],["{",r.OpenCurlyBracket],["}",r.CloseCurlyBracket],["[",r.OpenSquareBracket],["]",r.CloseSquareBracket],[",",r.Comma],[".",r.Dot],[":",r.Colon],["|",r.Pipe],["<=",r.ComparisonBinaryOperator],[">=",r.ComparisonBinaryOperator],["==",r.ComparisonBinaryOperator],["!=",r.ComparisonBinaryOperator],["<",r.ComparisonBinaryOperator],[">",r.ComparisonBinaryOperator],["+",r.AdditiveBinaryOperator],["-",r.AdditiveBinaryOperator],["*",r.MultiplicativeBinaryOperator],["/",r.MultiplicativeBinaryOperator],["%",r.MultiplicativeBinaryOperator],["=",r.Equals]],c=new Map([["n","\n"],["t","\t"],["r","\r"],["b","\b"],["f","\f"],["v","\v"],["'","'"],['"','"'],["\\","\\"]]);function d(e,t={}){const s=[],d=function(e,t={}){return e.endsWith("\n")&&(e=e.slice(0,-1)),e=e.replace(/{#.*?#}/gs,"{##}"),t.lstrip_blocks&&(e=e.replace(/^[ \t]*({[#%])/gm,"$1")),t.trim_blocks&&(e=e.replace(/([#%]})\n/g,"$1")),e.replace(/{##}/g,"").replace(/-%}\s*/g,"%}").replace(/\s*{%-/g,"{%").replace(/-}}\s*/g,"}}").replace(/\s*{{-/g,"{{")}(e,t);let u=0;const _=e=>{let t="";for(;e(d[u]);)if("\\"!==d[u]){if(t+=d[u++],u>=d.length)throw new SyntaxError("Unexpected end of input")}else{if(++u,u>=d.length)throw new SyntaxError("Unexpected end of input");const e=d[u++],s=c.get(e);if(void 0===s)throw new SyntaxError(`Unexpected escaped character: ${e}`);t+=s}return t};e:for(;u<d.length;){const e=s.at(-1)?.type;if(void 0===e||e===r.CloseStatement||e===r.CloseExpression){let e="";for(;u<d.length&&("{"!==d[u]||"%"!==d[u+1]&&"{"!==d[u+1]);)e+=d[u++];if(e.length>0){s.push(new n(e,r.Text));continue}}_((e=>/\s/.test(e)));const t=d[u];if("-"===t||"+"===t){const e=s.at(-1)?.type;if(e===r.Text||void 0===e)throw new SyntaxError(`Unexpected character: ${t}`);switch(e){case r.Identifier:case r.NumericLiteral:case r.BooleanLiteral:case r.NullLiteral:case r.StringLiteral:case r.CloseParen:case r.CloseSquareBracket:break;default:{++u;const e=_(a);s.push(new n(`${t}${e}`,e.length>0?r.NumericLiteral:r.UnaryOperator));continue}}}for(const[e,t]of l){if(d.slice(u,u+e.length)===e){s.push(new n(e,t)),u+=e.length;continue e}}if("'"!==t&&'"'!==t)if(a(t)){const e=_(a);s.push(new n(e,r.NumericLiteral))}else{if(!i(t))throw new SyntaxError(`Unexpected character: ${t}`);{const e=_(i),t=Object.hasOwn(o,e)?o[e]:r.Identifier;t===r.In&&s.at(-1)?.type===r.Not?(s.pop(),s.push(new n("not in",r.NotIn))):s.push(new n(e,t))}}else{++u;const e=_((e=>e!==t));s.push(new n(e,r.StringLiteral)),++u}}return s}var u=class{type="Statement"},_=class extends u{constructor(e){super(),this.body=e}type="Program"},p=class extends u{constructor(e,t,s){super(),this.test=e,this.body=t,this.alternate=s}type="If"},m=class extends u{constructor(e,t,s,r){super(),this.loopvar=e,this.iterable=t,this.body=s,this.defaultBlock=r}type="For"},h=class extends u{type="Break"},g=class extends u{type="Continue"},f=class extends u{constructor(e,t,s){super(),this.assignee=e,this.value=t,this.body=s}type="Set"},w=class extends u{constructor(e,t,s){super(),this.name=e,this.args=t,this.body=s}type="Macro"},M=class extends u{type="Expression"},x=class extends M{constructor(e,t,s){super(),this.object=e,this.property=t,this.computed=s}type="MemberExpression"},b=class extends M{constructor(e,t){super(),this.callee=e,this.args=t}type="CallExpression"},k=class extends M{constructor(e){super(),this.value=e}type="Identifier"},y=class extends M{constructor(e){super(),this.value=e}type="Literal"},v=class extends y{type="NumericLiteral"},T=class extends y{type="StringLiteral"},P=class extends y{type="BooleanLiteral"},F=class extends y{type="NullLiteral"},C=class extends y{type="ArrayLiteral"},S=class extends y{type="TupleLiteral"},E=class extends y{type="ObjectLiteral"},A=class extends M{constructor(e,t,s){super(),this.operator=e,this.left=t,this.right=s}type="BinaryExpression"},L=class extends M{constructor(e,t){super(),this.operand=e,this.filter=t}type="FilterExpression"},I=class extends M{constructor(e,t){super(),this.iterable=e,this.test=t}type="SelectExpression"},z=class extends M{constructor(e,t,s){super(),this.operand=e,this.negate=t,this.test=s}type="TestExpression"},j=class extends M{constructor(e,t){super(),this.operator=e,this.argument=t}type="UnaryExpression"},D=class extends M{constructor(e=void 0,t=void 0,s=void 0){super(),this.start=e,this.stop=t,this.step=s}type="SliceExpression"},O=class extends M{constructor(e,t){super(),this.key=e,this.value=t}type="KeywordArgumentExpression"};function N(e){const t=new _([]);let s=0;function o(t,r){const o=e[s++];if(!o||o.type!==t)throw new Error(`Parser Error: ${r}. ${o.type} !== ${t}.`);return o}function n(){switch(e[s].type){case r.Text:return new T(o(r.Text,"Expected text token").value);case r.OpenStatement:return function(){let t;switch(o(r.OpenStatement,"Expected opening statement token"),e[s].type){case r.Set:++s,t=function(){const t=d();if(a(r.Equals)){++s;const e=d();return new f(t,e,[])}{const i=[];for(o(r.CloseStatement,"Expected %} token");e[s]?.type!==r.OpenStatement||e[s+1]?.type!==r.EndSet;){const e=n();i.push(e)}return o(r.OpenStatement,"Expected {% token"),o(r.EndSet,"Expected endset token"),new f(t,null,i)}}(),o(r.CloseStatement,"Expected closing statement token");break;case r.If:++s,t=l(),o(r.OpenStatement,"Expected {% token"),o(r.EndIf,"Expected endif token"),o(r.CloseStatement,"Expected %} token");break;case r.Macro:++s,t=function(){const e=W();if("Identifier"!==e.type)throw new SyntaxError("Expected identifier following macro statement");const t=B();o(r.CloseStatement,"Expected closing statement token");const s=[];for(;i(r.OpenStatement,r.EndMacro);)s.push(n());return new w(e,t,s)}(),o(r.OpenStatement,"Expected {% token"),o(r.EndMacro,"Expected endmacro token"),o(r.CloseStatement,"Expected %} token");break;case r.For:++s,t=function(){const e=c(!0);if(!(e instanceof k||e instanceof S))throw new SyntaxError(`Expected identifier/tuple for the loop variable, got ${e.type} instead`);o(r.In,"Expected `in` keyword following loop variable");const t=d();o(r.CloseStatement,"Expected closing statement token");const l=[];for(;i(r.OpenStatement,r.EndFor)&&i(r.OpenStatement,r.Else);)l.push(n());const u=[];if(a(r.OpenStatement,r.Else))for(++s,++s,o(r.CloseStatement,"Expected closing statement token");i(r.OpenStatement,r.EndFor);)u.push(n());return new m(e,t,l,u)}(),o(r.OpenStatement,"Expected {% token"),o(r.EndFor,"Expected endfor token"),o(r.CloseStatement,"Expected %} token");break;case r.Break:++s,o(r.CloseStatement,"Expected closing statement token"),t=new h;break;case r.Continue:++s,o(r.CloseStatement,"Expected closing statement token"),t=new g;break;default:throw new SyntaxError(`Unknown statement type: ${e[s].type}`)}return t}();case r.OpenExpression:return function(){o(r.OpenExpression,"Expected opening expression token");const e=d();return o(r.CloseExpression,"Expected closing expression token"),e}();default:throw new SyntaxError(`Unexpected token type: ${e[s].type}`)}}function i(...t){return s+t.length<=e.length&&t.some(((t,r)=>t!==e[s+r].type))}function a(...t){return s+t.length<=e.length&&t.every(((t,r)=>t===e[s+r].type))}function l(){const t=d();o(r.CloseStatement,"Expected closing statement token");const i=[],c=[];for(;e[s]?.type!==r.OpenStatement||e[s+1]?.type!==r.ElseIf&&e[s+1]?.type!==r.Else&&e[s+1]?.type!==r.EndIf;)i.push(n());if(e[s]?.type===r.OpenStatement&&e[s+1]?.type!==r.EndIf)if(++s,a(r.ElseIf))o(r.ElseIf,"Expected elseif token"),c.push(l());else for(o(r.Else,"Expected else token"),o(r.CloseStatement,"Expected closing statement token");e[s]?.type!==r.OpenStatement||e[s+1]?.type!==r.EndIf;)c.push(n());return new p(t,i,c)}function c(e=!1){const t=e?W:d,o=[t()],n=a(r.Comma);for(;n&&(++s,o.push(t()),a(r.Comma)););return n?new S(o):o[0]}function d(){return function(){const e=u();if(a(r.If)){++s;const t=u();if(a(r.Else)){++s;const r=u();return new p(t,[e],[r])}return new I(e,t)}return e}()}function u(){let t=M();for(;a(r.Or);){const r=e[s];++s;const o=M();t=new A(r,t,o)}return t}function M(){let t=y();for(;a(r.And);){const r=e[s];++s;const o=y();t=new A(r,t,o)}return t}function y(){let t;for(;a(r.Not);){const r=e[s];++s;const o=y();t=new j(r,o)}return t??function(){let t=N();for(;a(r.ComparisonBinaryOperator)||a(r.In)||a(r.NotIn);){const r=e[s];++s;const o=N();t=new A(r,t,o)}return t}()}function N(){let t=$();for(;a(r.AdditiveBinaryOperator);){const r=e[s];++s;const o=$();t=new A(r,t,o)}return t}function V(e){let t=new b(e,B());return t=R(t),a(r.OpenParen)&&(t=V(t)),t}function B(){o(r.OpenParen,"Expected opening parenthesis for arguments list");const e=function(){const e=[];for(;!a(r.CloseParen);){let t=d();if(a(r.Equals)){if(++s,!(t instanceof k))throw new SyntaxError("Expected identifier for keyword argument");const e=d();t=new O(t,e)}e.push(t),a(r.Comma)&&++s}return e}();return o(r.CloseParen,"Expected closing parenthesis for arguments list"),e}function G(){const e=[];let t=!1;for(;!a(r.CloseSquareBracket);)a(r.Colon)?(e.push(void 0),++s,t=!0):(e.push(d()),a(r.Colon)&&(++s,t=!0));if(0===e.length)throw new SyntaxError("Expected at least one argument for member/slice expression");if(t){if(e.length>3)throw new SyntaxError("Expected 0-3 arguments for slice expression");return new D(...e)}return e[0]}function R(t){for(;a(r.Dot)||a(r.OpenSquareBracket);){const n=e[s];let i;++s;const a=n.type!==r.Dot;if(a)i=G(),o(r.CloseSquareBracket,"Expected closing square bracket");else if(i=W(),"Identifier"!==i.type)throw new SyntaxError("Expected identifier following dot operator");t=new x(t,i,a)}return t}function $(){let t=q();for(;a(r.MultiplicativeBinaryOperator);){const r=e[s];++s;const o=q();t=new A(r,t,o)}return t}function q(){let e=function(){let e=function(){const e=R(W());return a(r.OpenParen)?V(e):e}();for(;a(r.Pipe);){++s;let t=W();if(!(t instanceof k))throw new SyntaxError("Expected identifier for the filter");a(r.OpenParen)&&(t=V(t)),e=new L(e,t)}return e}();for(;a(r.Is);){++s;const t=a(r.Not);t&&++s;let o=W();if(o instanceof P?o=new k(o.value.toString()):o instanceof F&&(o=new k("none")),!(o instanceof k))throw new SyntaxError("Expected identifier for the test");e=new z(e,t,o)}return e}function W(){const t=e[s];switch(t.type){case r.NumericLiteral:return++s,new v(Number(t.value));case r.StringLiteral:return++s,new T(t.value);case r.BooleanLiteral:return++s,new P("true"===t.value.toLowerCase());case r.NullLiteral:return++s,new F(null);case r.Identifier:return++s,new k(t.value);case r.OpenParen:{++s;const t=c();if(e[s].type!==r.CloseParen)throw new SyntaxError(`Expected closing parenthesis, got ${e[s].type} instead`);return++s,t}case r.OpenSquareBracket:{++s;const e=[];for(;!a(r.CloseSquareBracket);)e.push(d()),a(r.Comma)&&++s;return++s,new C(e)}case r.OpenCurlyBracket:{++s;const e=new Map;for(;!a(r.CloseCurlyBracket);){const t=d();o(r.Colon,"Expected colon between key and value in object literal");const n=d();e.set(t,n),a(r.Comma)&&++s}return++s,new E(e)}default:throw new SyntaxError(`Unexpected token: ${t.type}`)}}for(;s<e.length;)t.body.push(n());return t}function V(e,t,s=1){void 0===t&&(t=e,e=0);const r=[];for(let o=e;o<t;o+=s)r.push(o);return r}function B(e,t,s,r=1){const o=Math.sign(r);o>=0?(t=(t??=0)<0?Math.max(e.length+t,0):Math.min(t,e.length),s=(s??=e.length)<0?Math.max(e.length+s,0):Math.min(s,e.length)):(t=(t??=e.length-1)<0?Math.max(e.length+t,-1):Math.min(t,e.length-1),s=(s??=-1)<-1?Math.max(e.length+s,-1):Math.min(s,e.length-1));const n=[];for(let i=t;o*i<o*s;i+=r)n.push(e[i]);return n}function G(e){return e.replace(/\b\w/g,(e=>e.toUpperCase()))}var R=class extends Error{},$=class extends Error{},q=class{type="RuntimeValue";value;builtins=new Map;constructor(e=void 0){this.value=e}__bool__(){return new Q(!!this.value)}},W=class extends q{type="NumericValue"},U=class extends q{type="StringValue";builtins=new Map([["upper",new K((()=>new U(this.value.toUpperCase())))],["lower",new K((()=>new U(this.value.toLowerCase())))],["strip",new K((()=>new U(this.value.trim())))],["title",new K((()=>new U(G(this.value))))],["length",new W(this.value.length)],["rstrip",new K((()=>new U(this.value.trimEnd())))],["lstrip",new K((()=>new U(this.value.trimStart())))],["startswith",new K((e=>{if(0===e.length)throw new Error("startswith() requires at least one argument");const t=e[0];if(!(t instanceof U))throw new Error("startswith() argument must be a string");return new Q(this.value.startsWith(t.value))}))],["endswith",new K((e=>{if(0===e.length)throw new Error("endswith() requires at least one argument");const t=e[0];if(!(t instanceof U))throw new Error("endswith() argument must be a string");return new Q(this.value.endsWith(t.value))}))],["split",new K((e=>{const t=e[0]??new Z;if(!(t instanceof U||t instanceof Z))throw new Error("sep argument must be a string or null");const s=e[1]??new W(-1);if(!(s instanceof W))throw new Error("maxsplit argument must be a number");let r=[];if(t instanceof Z){const e=this.value.trimStart();for(const{0:t,index:o}of e.matchAll(/\S+/g)){if(-1!==s.value&&r.length>=s.value&&void 0!==o){r.push(t+e.slice(o+t.length));break}r.push(t)}}else{if(""===t.value)throw new Error("empty separator");r=this.value.split(t.value),-1!==s.value&&r.length>s.value&&r.push(r.splice(s.value).join(t.value))}return new J(r.map((e=>new U(e))))}))]])},Q=class extends q{type="BooleanValue"},X=class extends q{type="ObjectValue";__bool__(){return new Q(this.value.size>0)}builtins=new Map([["get",new K((([e,t])=>{if(!(e instanceof U))throw new Error(`Object key must be a string: got ${e.type}`);return this.value.get(e.value)??t??new Z}))],["items",new K((()=>new J(Array.from(this.value.entries()).map((([e,t])=>new J([new U(e),t]))))))]])},H=class extends X{type="KeywordArgumentsValue"},J=class extends q{type="ArrayValue";builtins=new Map([["length",new W(this.value.length)]]);__bool__(){return new Q(this.value.length>0)}},Y=class extends J{type="TupleValue"},K=class extends q{type="FunctionValue"},Z=class extends q{type="NullValue"},ee=class extends q{type="UndefinedValue"},te=class{constructor(e){this.parent=e}variables=new Map([["namespace",new K((e=>{if(0===e.length)return new X(new Map);if(1!==e.length||!(e[0]instanceof X))throw new Error("`namespace` expects either zero arguments or a single object argument");return e[0]}))]]);tests=new Map([["boolean",e=>"BooleanValue"===e.type],["callable",e=>e instanceof K],["odd",e=>{if("NumericValue"!==e.type)throw new Error(`Cannot apply test "odd" to type: ${e.type}`);return e.value%2!=0}],["even",e=>{if("NumericValue"!==e.type)throw new Error(`Cannot apply test "even" to type: ${e.type}`);return e.value%2==0}],["false",e=>"BooleanValue"===e.type&&!e.value],["true",e=>"BooleanValue"===e.type&&e.value],["none",e=>"NullValue"===e.type],["string",e=>"StringValue"===e.type],["number",e=>"NumericValue"===e.type],["integer",e=>"NumericValue"===e.type&&Number.isInteger(e.value)],["iterable",e=>"ArrayValue"===e.type||"StringValue"===e.type],["mapping",e=>"ObjectValue"===e.type],["lower",e=>{const t=e.value;return"StringValue"===e.type&&t===t.toLowerCase()}],["upper",e=>{const t=e.value;return"StringValue"===e.type&&t===t.toUpperCase()}],["none",e=>"NullValue"===e.type],["defined",e=>"UndefinedValue"!==e.type],["undefined",e=>"UndefinedValue"===e.type],["equalto",(e,t)=>e.value===t.value],["eq",(e,t)=>e.value===t.value]]);set(e,t){return this.declareVariable(e,re(t))}declareVariable(e,t){if(this.variables.has(e))throw new SyntaxError(`Variable already declared: ${e}`);return this.variables.set(e,t),t}setVariable(e,t){return this.variables.set(e,t),t}resolve(e){if(this.variables.has(e))return this;if(this.parent)return this.parent.resolve(e);throw new Error(`Unknown variable: ${e}`)}lookupVariable(e){try{return this.resolve(e).variables.get(e)??new ee}catch{return new ee}}},se=class{global;constructor(e){this.global=e??new te}run(e){return this.evaluate(e,this.global)}evaluateBinaryExpression(e,t){const s=this.evaluate(e.left,t);switch(e.operator.value){case"and":return s.__bool__().value?this.evaluate(e.right,t):s;case"or":return s.__bool__().value?s:this.evaluate(e.right,t)}const r=this.evaluate(e.right,t);switch(e.operator.value){case"==":return new Q(s.value==r.value);case"!=":return new Q(s.value!=r.value)}if(s instanceof ee||r instanceof ee)throw new Error("Cannot perform operation on undefined values");if(s instanceof Z||r instanceof Z)throw new Error("Cannot perform operation on null values");if(s instanceof W&&r instanceof W)switch(e.operator.value){case"+":return new W(s.value+r.value);case"-":return new W(s.value-r.value);case"*":return new W(s.value*r.value);case"/":return new W(s.value/r.value);case"%":return new W(s.value%r.value);case"<":return new Q(s.value<r.value);case">":return new Q(s.value>r.value);case">=":return new Q(s.value>=r.value);case"<=":return new Q(s.value<=r.value)}else if(s instanceof J&&r instanceof J){if("+"===e.operator.value)return new J(s.value.concat(r.value))}else if(r instanceof J){const t=void 0!==r.value.find((e=>e.value===s.value));switch(e.operator.value){case"in":return new Q(t);case"not in":return new Q(!t)}}if((s instanceof U||r instanceof U)&&"+"===e.operator.value)return new U(s.value.toString()+r.value.toString());if(s instanceof U&&r instanceof U)switch(e.operator.value){case"in":return new Q(r.value.includes(s.value));case"not in":return new Q(!r.value.includes(s.value))}if(s instanceof U&&r instanceof X)switch(e.operator.value){case"in":return new Q(r.value.has(s.value));case"not in":return new Q(!r.value.has(s.value))}throw new SyntaxError(`Unknown operator "${e.operator.value}" between ${s.type} and ${r.type}`)}evaluateArguments(e,t){const s=[],r=new Map;for(const o of e)if("KeywordArgumentExpression"===o.type){const e=o;r.set(e.key.value,this.evaluate(e.value,t))}else{if(r.size>0)throw new Error("Positional arguments must come before keyword arguments");s.push(this.evaluate(o,t))}return[s,r]}evaluateFilterExpression(e,t){const s=this.evaluate(e.operand,t);if("Identifier"===e.filter.type){const t=e.filter;if("tojson"===t.value)return new U(oe(s));if(s instanceof J)switch(t.value){case"list":return s;case"first":return s.value[0];case"last":return s.value[s.value.length-1];case"length":return new W(s.value.length);case"reverse":return new J(s.value.reverse());case"sort":return new J(s.value.sort(((e,t)=>{if(e.type!==t.type)throw new Error(`Cannot compare different types: ${e.type} and ${t.type}`);switch(e.type){case"NumericValue":return e.value-t.value;case"StringValue":return e.value.localeCompare(t.value);default:throw new Error(`Cannot compare type: ${e.type}`)}})));case"join":return new U(s.value.map((e=>e.value)).join(""));case"string":return new U(oe(s));default:throw new Error(`Unknown ArrayValue filter: ${t.value}`)}else if(s instanceof U)switch(t.value){case"length":return new W(s.value.length);case"upper":return new U(s.value.toUpperCase());case"lower":return new U(s.value.toLowerCase());case"title":return new U(G(s.value));case"capitalize":return new U(s.value.charAt(0).toUpperCase()+s.value.slice(1));case"trim":return new U(s.value.trim());case"indent":return new U(s.value.split("\n").map(((e,t)=>0===t||0===e.length?e:"    "+e)).join("\n"));case"join":case"string":return s;default:throw new Error(`Unknown StringValue filter: ${t.value}`)}else{if(s instanceof W){if("abs"===t.value)return new W(Math.abs(s.value));throw new Error(`Unknown NumericValue filter: ${t.value}`)}if(s instanceof X)switch(t.value){case"items":return new J(Array.from(s.value.entries()).map((([e,t])=>new J([new U(e),t]))));case"length":return new W(s.value.size);default:throw new Error(`Unknown ObjectValue filter: ${t.value}`)}}throw new Error(`Cannot apply filter "${t.value}" to type: ${s.type}`)}if("CallExpression"===e.filter.type){const r=e.filter;if("Identifier"!==r.callee.type)throw new Error(`Unknown filter: ${r.callee.type}`);const o=r.callee.value;if("tojson"===o){const[,e]=this.evaluateArguments(r.args,t),o=e.get("indent")??new Z;if(!(o instanceof W||o instanceof Z))throw new Error("If set, indent must be a number");return new U(oe(s,o.value))}if("join"===o){let e;if(s instanceof U)e=Array.from(s.value);else{if(!(s instanceof J))throw new Error(`Cannot apply filter "${o}" to type: ${s.type}`);e=s.value.map((e=>e.value))}const[n,i]=this.evaluateArguments(r.args,t),a=n.at(0)??i.get("separator")??new U("");if(!(a instanceof U))throw new Error("separator must be a string");return new U(e.join(a.value))}if(s instanceof J){switch(o){case"selectattr":case"rejectattr":{const e="selectattr"===o;if(s.value.some((e=>!(e instanceof X))))throw new Error(`\`${o}\` can only be applied to array of objects`);if(r.args.some((e=>"StringLiteral"!==e.type)))throw new Error(`arguments of \`${o}\` must be strings`);const[n,i,a]=r.args.map((e=>this.evaluate(e,t)));let l;if(i){const e=t.tests.get(i.value);if(!e)throw new Error(`Unknown test: ${i.value}`);l=e}else l=(...e)=>e[0].__bool__().value;const c=s.value.filter((t=>{const s=t.value.get(n.value),r=!!s&&l(s,a);return e?r:!r}));return new J(c)}case"map":{const[,e]=this.evaluateArguments(r.args,t);if(e.has("attribute")){const t=e.get("attribute");if(!(t instanceof U))throw new Error("attribute must be a string");const r=e.get("default"),o=s.value.map((e=>{if(!(e instanceof X))throw new Error("items in map must be an object");return e.value.get(t.value)??r??new ee}));return new J(o)}throw new Error("`map` expressions without `attribute` set are not currently supported.")}}throw new Error(`Unknown ArrayValue filter: ${o}`)}if(s instanceof U){if("indent"===o){const[e,o]=this.evaluateArguments(r.args,t),n=e.at(0)??o.get("width")??new W(4);if(!(n instanceof W))throw new Error("width must be a number");const i=e.at(1)??o.get("first")??new Q(!1),a=e.at(2)??o.get("blank")??new Q(!1),l=s.value.split("\n"),c=" ".repeat(n.value),d=l.map(((e,t)=>!i.value&&0===t||!a.value&&0===e.length?e:c+e));return new U(d.join("\n"))}throw new Error(`Unknown StringValue filter: ${o}`)}throw new Error(`Cannot apply filter "${o}" to type: ${s.type}`)}throw new Error(`Unknown filter: ${e.filter.type}`)}evaluateTestExpression(e,t){const s=this.evaluate(e.operand,t),r=t.tests.get(e.test.value);if(!r)throw new Error(`Unknown test: ${e.test.value}`);const o=r(s);return new Q(e.negate?!o:o)}evaluateUnaryExpression(e,t){const s=this.evaluate(e.argument,t);if("not"===e.operator.value)return new Q(!s.value);throw new SyntaxError(`Unknown operator: ${e.operator.value}`)}evalProgram(e,t){return this.evaluateBlock(e.body,t)}evaluateBlock(e,t){let s="";for(const r of e){const e=this.evaluate(r,t);"NullValue"!==e.type&&"UndefinedValue"!==e.type&&(s+=e.value)}return new U(s)}evaluateIdentifier(e,t){return t.lookupVariable(e.value)}evaluateCallExpression(e,t){const[s,r]=this.evaluateArguments(e.args,t);r.size>0&&s.push(new H(r));const o=this.evaluate(e.callee,t);if("FunctionValue"!==o.type)throw new Error(`Cannot call something that is not a function: got ${o.type}`);return o.value(s,t)}evaluateSliceExpression(e,t,s){if(!(e instanceof J||e instanceof U))throw new Error("Slice object must be an array or string");const r=this.evaluate(t.start,s),o=this.evaluate(t.stop,s),n=this.evaluate(t.step,s);if(!(r instanceof W||r instanceof ee))throw new Error("Slice start must be numeric or undefined");if(!(o instanceof W||o instanceof ee))throw new Error("Slice stop must be numeric or undefined");if(!(n instanceof W||n instanceof ee))throw new Error("Slice step must be numeric or undefined");return e instanceof J?new J(B(e.value,r.value,o.value,n.value)):new U(B(Array.from(e.value),r.value,o.value,n.value).join(""))}evaluateMemberExpression(e,t){const s=this.evaluate(e.object,t);let r,o;if(e.computed){if("SliceExpression"===e.property.type)return this.evaluateSliceExpression(s,e.property,t);r=this.evaluate(e.property,t)}else r=new U(e.property.value);if(s instanceof X){if(!(r instanceof U))throw new Error(`Cannot access property with non-string: got ${r.type}`);o=s.value.get(r.value)??s.builtins.get(r.value)}else if(s instanceof J||s instanceof U)if(r instanceof W)o=s.value.at(r.value),s instanceof U&&(o=new U(s.value.at(r.value)));else{if(!(r instanceof U))throw new Error(`Cannot access property with non-string/non-number: got ${r.type}`);o=s.builtins.get(r.value)}else{if(!(r instanceof U))throw new Error(`Cannot access property with non-string: got ${r.type}`);o=s.builtins.get(r.value)}return o instanceof q?o:new ee}evaluateSet(e,t){const s=e.value?this.evaluate(e.value,t):this.evaluateBlock(e.body,t);if("Identifier"===e.assignee.type){const r=e.assignee.value;t.setVariable(r,s)}else{if("MemberExpression"!==e.assignee.type)throw new Error(`Invalid LHS inside assignment expression: ${JSON.stringify(e.assignee)}`);{const r=e.assignee,o=this.evaluate(r.object,t);if(!(o instanceof X))throw new Error("Cannot assign to member of non-object");if("Identifier"!==r.property.type)throw new Error("Cannot assign to member with non-identifier property");o.value.set(r.property.value,s)}}return new Z}evaluateIf(e,t){const s=this.evaluate(e.test,t);return this.evaluateBlock(s.__bool__().value?e.body:e.alternate,t)}evaluateFor(e,t){const s=new te(t);let r,o;if("SelectExpression"===e.iterable.type){const t=e.iterable;o=this.evaluate(t.iterable,s),r=t.test}else o=this.evaluate(e.iterable,s);if(!(o instanceof J))throw new Error(`Expected iterable type in for loop: got ${o.type}`);const n=[],i=[];for(let t=0;t<o.value.length;++t){const a=new te(s),l=o.value[t];let c;if("Identifier"===e.loopvar.type)c=t=>t.setVariable(e.loopvar.value,l);else{if("TupleLiteral"!==e.loopvar.type)throw new Error(`Invalid loop variable(s): ${e.loopvar.type}`);{const t=e.loopvar;if("ArrayValue"!==l.type)throw new Error(`Cannot unpack non-iterable type: ${l.type}`);const s=l;if(t.value.length!==s.value.length)throw new Error(`Too ${t.value.length>s.value.length?"few":"many"} items to unpack`);c=e=>{for(let r=0;r<t.value.length;++r){if("Identifier"!==t.value[r].type)throw new Error(`Cannot unpack non-identifier type: ${t.value[r].type}`);e.setVariable(t.value[r].value,s.value[r])}}}}if(r){c(a);if(!this.evaluate(r,a).__bool__().value)continue}n.push(l),i.push(c)}let a="",l=!0;for(let t=0;t<n.length;++t){const r=new Map([["index",new W(t+1)],["index0",new W(t)],["revindex",new W(n.length-t)],["revindex0",new W(n.length-t-1)],["first",new Q(0===t)],["last",new Q(t===n.length-1)],["length",new W(n.length)],["previtem",t>0?n[t-1]:new ee],["nextitem",t<n.length-1?n[t+1]:new ee]]);s.setVariable("loop",new X(r)),i[t](s);try{a+=this.evaluateBlock(e.body,s).value}catch(e){if(e instanceof $)continue;if(e instanceof R)break;throw e}l=!1}if(l){a+=this.evaluateBlock(e.defaultBlock,s).value}return new U(a)}evaluateMacro(e,t){return t.setVariable(e.name.value,new K(((t,s)=>{const r=new te(s);let o;t=t.slice(),"KeywordArgumentsValue"===t.at(-1)?.type&&(o=t.pop());for(let s=0;s<e.args.length;++s){const n=e.args[s],i=t[s];if("Identifier"===n.type){const e=n;if(!i)throw new Error(`Missing positional argument: ${e.value}`);r.setVariable(e.value,i)}else{if("KeywordArgumentExpression"!==n.type)throw new Error(`Unknown argument type: ${n.type}`);{const e=n,t=i??o?.value.get(e.key.value)??this.evaluate(e.value,r);r.setVariable(e.key.value,t)}}}return this.evaluateBlock(e.body,r)}))),new Z}evaluate(e,t){if(void 0===e)return new ee;switch(e.type){case"Program":return this.evalProgram(e,t);case"Set":return this.evaluateSet(e,t);case"If":return this.evaluateIf(e,t);case"For":return this.evaluateFor(e,t);case"Macro":return this.evaluateMacro(e,t);case"Break":throw new R;case"Continue":throw new $;case"NumericLiteral":return new W(Number(e.value));case"StringLiteral":return new U(e.value);case"BooleanLiteral":return new Q(e.value);case"NullLiteral":return new Z(e.value);case"ArrayLiteral":return new J(e.value.map((e=>this.evaluate(e,t))));case"TupleLiteral":return new Y(e.value.map((e=>this.evaluate(e,t))));case"ObjectLiteral":{const s=new Map;for(const[r,o]of e.value){const e=this.evaluate(r,t);if(!(e instanceof U))throw new Error(`Object keys must be strings: got ${e.type}`);s.set(e.value,this.evaluate(o,t))}return new X(s)}case"Identifier":return this.evaluateIdentifier(e,t);case"CallExpression":return this.evaluateCallExpression(e,t);case"MemberExpression":return this.evaluateMemberExpression(e,t);case"UnaryExpression":return this.evaluateUnaryExpression(e,t);case"BinaryExpression":return this.evaluateBinaryExpression(e,t);case"FilterExpression":return this.evaluateFilterExpression(e,t);case"TestExpression":return this.evaluateTestExpression(e,t);default:throw new SyntaxError(`Unknown node type: ${e.type}`)}}};function re(e){switch(typeof e){case"number":return new W(e);case"string":return new U(e);case"boolean":return new Q(e);case"undefined":return new ee;case"object":return null===e?new Z:Array.isArray(e)?new J(e.map(re)):new X(new Map(Object.entries(e).map((([e,t])=>[e,re(t)]))));case"function":return new K(((t,s)=>re(e(...t.map((e=>e.value)))??null)));default:throw new Error(`Cannot convert to runtime value: ${e}`)}}function oe(e,t,s){const r=s??0;switch(e.type){case"NullValue":case"UndefinedValue":return"null";case"NumericValue":case"StringValue":case"BooleanValue":return JSON.stringify(e.value);case"ArrayValue":case"ObjectValue":{const s=t?" ".repeat(t):"",o="\n"+s.repeat(r),n=o+s;if("ArrayValue"===e.type){const s=e.value.map((e=>oe(e,t,r+1)));return t?`[${n}${s.join(`,${n}`)}${o}]`:`[${s.join(", ")}]`}{const s=Array.from(e.value.entries()).map((([e,s])=>{const o=`"${e}": ${oe(s,t,r+1)}`;return t?`${n}${o}`:o}));return t?`{${s.join(",")}${o}}`:`{${s.join(", ")}}`}}default:throw new Error(`Cannot convert to JSON: ${e.type}`)}}var ne="\n",ie="{%- ",ae=" -%}",le={MultiplicativeBinaryOperator:2,AdditiveBinaryOperator:1,ComparisonBinaryOperator:0};function ce(...e){return ie+e.join(" ")+ae}function de(e,t,s){return e.map((e=>function(e,t,s){const r=s.repeat(t);switch(e.type){case"Program":return de(e.body,t,s);case"If":return function(e,t,s){const r=s.repeat(t),o=[];let n=e;for(;n&&(o.push({test:n.test,body:n.body}),1===n.alternate.length&&"If"===n.alternate[0].type);)n=n.alternate[0];let i=r+ce("if",ue(o[0].test))+ne+de(o[0].body,t+1,s);for(let e=1;e<o.length;e++)i+=ne+r+ce("elif",ue(o[e].test))+ne+de(o[e].body,t+1,s);n&&n.alternate.length>0&&(i+=ne+r+ce("else")+ne+de(n.alternate,t+1,s));return i+=ne+r+ce("endif"),i}(e,t,s);case"For":return function(e,t,s){const r=s.repeat(t);let o="";if("SelectExpression"===e.iterable.type){const t=e.iterable;o=`${ue(t.iterable)} if ${ue(t.test)}`}else o=ue(e.iterable);let n=r+ce("for",ue(e.loopvar),"in",o)+ne+de(e.body,t+1,s);e.defaultBlock.length>0&&(n+=ne+r+ce("else")+ne+de(e.defaultBlock,t+1,s));return n+=ne+r+ce("endfor"),n}(e,t,s);case"Set":return function(e,t,s){const r=s.repeat(t),o=ue(e.assignee),n=e.value?ue(e.value):"",i=r+ce("set",`${o}${e.value?" = "+n:""}`);if(0===e.body.length)return i;return i+ne+de(e.body,t+1,s)+ne+r+ce("endset")}(e,t,s);case"Macro":return function(e,t,s){const r=s.repeat(t),o=e.args.map(ue).join(", ");return r+ce("macro",`${e.name.value}(${o})`)+ne+de(e.body,t+1,s)+ne+r+ce("endmacro")}(e,t,s);case"Break":return r+ce("break");case"Continue":return r+ce("continue");default:return r+"{{- "+ue(e)+" -}}"}}(e,t,s))).join(ne)}function ue(e,t=-1){switch(e.type){case"Identifier":return e.value;case"NullLiteral":return"none";case"NumericLiteral":case"BooleanLiteral":return`${e.value}`;case"StringLiteral":return JSON.stringify(e.value);case"BinaryExpression":{const s=e,r=le[s.operator.type]??0,o=ue(s.left,r),n=ue(s.right,r+1),i=`${o} ${s.operator.value} ${n}`;return r<t?`(${i})`:i}case"UnaryExpression":{const t=e;return t.operator.value+("not"===t.operator.value?" ":"")+ue(t.argument,1/0)}case"LogicalNegationExpression":return`not ${ue(e.argument,1/0)}`;case"CallExpression":{const t=e,s=t.args.map((e=>ue(e,-1))).join(", ");return`${ue(t.callee,-1)}(${s})`}case"MemberExpression":{const t=e;let s=ue(t.object,-1);"Identifier"!==t.object.type&&(s=`(${s})`);let r=ue(t.property,-1);return t.computed||"Identifier"===t.property.type||(r=`(${r})`),t.computed?`${s}[${r}]`:`${s}.${r}`}case"FilterExpression":{const t=e,s=ue(t.operand,1/0);return"CallExpression"===t.filter.type?`${s} | ${ue(t.filter,-1)}`:`${s} | ${t.filter.value}`}case"SelectExpression":{const t=e;return`${ue(t.iterable,-1)} | select(${ue(t.test,-1)})`}case"TestExpression":{const t=e;return`${ue(t.operand,-1)} is${t.negate?" not":""} ${t.test.value}`}case"ArrayLiteral":case"TupleLiteral":{const t=e.value.map((e=>ue(e,-1))),s="ArrayLiteral"===e.type?"[]":"()";return`${s[0]}${t.join(", ")}${s[1]}`}case"ObjectLiteral":return`{ ${Array.from(e.value.entries()).map((([e,t])=>`${ue(e,-1)}: ${ue(t,-1)}`)).join(", ")} }`;case"SliceExpression":{const t=e;return`${t.start?ue(t.start,-1):""}:${t.stop?ue(t.stop,-1):""}${t.step?`:${ue(t.step,-1)}`:""}`}case"KeywordArgumentExpression":{const t=e;return`${t.key.value}=${ue(t.value,-1)}`}case"If":{const t=e,s=ue(t.test,-1);return`${ue(t.body[0],0)} if ${s} else ${ue(t.alternate[0],-1)}`}default:throw new Error(`Unknown expression type: ${e.type}`)}}var _e=class{parsed;constructor(e){const t=d(e,{lstrip_blocks:!0,trim_blocks:!0});this.parsed=N(t)}render(e){const t=new te;if(t.set("false",!1),t.set("true",!0),t.set("raise_exception",(e=>{throw new Error(e)})),t.set("range",V),e)for(const[s,r]of Object.entries(e))t.set(s,r);return new se(t).run(this.parsed).value}format(e){return function(e,t="\t"){const s="number"==typeof t?" ".repeat(t):t;return de(e.body,0,s).replace(/\n$/,"")}(this.parsed,e?.indent||"\t")}}},"./src/backends/onnx.js":(e,t,s)=>{"use strict";var r,o;s.r(t),s.d(t,{Tensor:()=>l.Tensor,createInferenceSession:()=>f,deviceToExecutionProviders:()=>h,isONNXProxy:()=>x,isONNXTensor:()=>w});var n=s("./src/env.js"),i=s("onnxruntime-node"),a=s("?8b6b"),l=s("onnxruntime-common");const c=Object.freeze({auto:null,gpu:null,cpu:"cpu",wasm:"wasm",webgpu:"webgpu",cuda:"cuda",dml:"dml",webnn:{name:"webnn",deviceType:"cpu"},"webnn-npu":{name:"webnn",deviceType:"npu"},"webnn-gpu":{name:"webnn",deviceType:"gpu"},"webnn-cpu":{name:"webnn",deviceType:"cpu"}}),d=[];let u,_;const p=Symbol.for("onnxruntime");if(p in globalThis)_=globalThis[p];else if(n.apis.IS_NODE_ENV){switch(_=i??(r||(r=s.t(i,2))),process.platform){case"win32":d.push("dml");break;case"linux":"x64"===process.arch&&d.push("cuda")}d.push("cpu"),u=["cpu"]}else _=o||(o=s.t(a,2)),n.apis.IS_WEBNN_AVAILABLE&&d.push("webnn-npu","webnn-gpu","webnn-cpu","webnn"),n.apis.IS_WEBGPU_AVAILABLE&&d.push("webgpu"),d.push("wasm"),u=["wasm"];const m=_.InferenceSession;function h(e=null){if(!e)return u;switch(e){case"auto":return d;case"gpu":return d.filter((e=>["webgpu","cuda","dml","webnn-gpu"].includes(e)))}if(d.includes(e))return[c[e]??e];throw new Error(`Unsupported device: "${e}". Should be one of: ${d.join(", ")}.`)}let g=null;async function f(e,t,s){g&&await g;const r=m.create(e,t);g??=r;const o=await r;return o.config=s,o}function w(e){return e instanceof _.Tensor}const M=_?.env;function x(){return M?.wasm?.proxy}M?.wasm&&("undefined"!=typeof ServiceWorkerGlobalScope&&self instanceof ServiceWorkerGlobalScope||M.wasm.wasmPaths||(M.wasm.wasmPaths=`https://cdn.jsdelivr.net/npm/@huggingface/transformers@${n.env.version}/dist/`),M.wasm.proxy=!1),M?.webgpu&&(M.webgpu.powerPreference="high-performance"),n.env.backends.onnx=M},"./src/base/feature_extraction_utils.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{FeatureExtractor:()=>i,validate_audio_inputs:()=>a});var r=s("./src/utils/constants.js"),o=s("./src/utils/generic.js"),n=s("./src/utils/hub.js");class i extends o.Callable{constructor(e){super(),this.config=e}static async from_pretrained(e,t={}){return new this(await(0,n.getModelJSON)(e,r.FEATURE_EXTRACTOR_NAME,!0,t))}}function a(e,t){if(!(e instanceof Float32Array||e instanceof Float64Array))throw new Error(`${t} expects input to be a Float32Array or a Float64Array, but got ${e?.constructor?.name??typeof e} instead. If using the feature extractor directly, remember to use \`read_audio(url, sampling_rate)\` to obtain the raw audio data of the file/url.`)}},"./src/base/image_processors_utils.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{ImageProcessor:()=>M,center_to_corners_format:()=>u,post_process_instance_segmentation:()=>w,post_process_object_detection:()=>_,post_process_panoptic_segmentation:()=>f,post_process_semantic_segmentation:()=>p});var r=s("./src/utils/generic.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/maths.js"),i=(s("./src/utils/image.js"),s("./src/utils/core.js")),a=s("./src/utils/hub.js"),l=s("./src/utils/constants.js");function c(e,t,s=0,r=null){const o=e/t;let i=(0,n.bankers_round)(o)*t;return null!==r&&i>r&&(i=Math.floor(o)*t),i<s&&(i=Math.ceil(o)*t),i}function d([e,t],s){return[Math.max(Math.floor(e/s),1)*s,Math.max(Math.floor(t/s),1)*s]}function u([e,t,s,r]){return[e-s/2,t-r/2,e+s/2,t+r/2]}function _(e,t=.5,s=null,r=!1){const o=e.logits,i=e.pred_boxes,[a,l,c]=o.dims;if(null!==s&&s.length!==a)throw Error("Make sure that you pass in as many target sizes as the batch dimension of the logits");let d=[];for(let e=0;e<a;++e){let a=null!==s?s[e]:null,_={boxes:[],classes:[],scores:[]},p=o[e],m=i[e];for(let e=0;e<l;++e){let s,o=p[e],i=[];if(r){s=o.sigmoid().data;for(let e=0;e<s.length;++e)s[e]>t&&i.push(e)}else{let e=(0,n.max)(o.data)[1];if(e===c-1)continue;if(s=(0,n.softmax)(o.data),s[e]<t)continue;i.push(e)}for(const t of i){let r=m[e].data;r=u(r),null!==a&&(r=r.map(((e,t)=>e*a[(t+1)%2]))),_.boxes.push(r),_.classes.push(t),_.scores.push(s[t])}}d.push(_)}return d}function p(e,t=null){const s=e.logits,r=s.dims[0];if(null!==t&&t.length!==r)throw Error("Make sure that you pass in as many target sizes as the batch dimension of the logits");const n=[];for(let e=0;e<r;++e){const r=null!==t?t[e]:null;let i=s[e];null!==r&&(i=(0,o.interpolate)(i,r,"bilinear",!1));const[a,l]=r??i.dims.slice(-2),c=new o.Tensor("int32",new Int32Array(a*l),[a,l]),d=i[0].data,u=c.data;for(let e=1;e<i.dims[0];++e){const t=i[e].data;for(let s=0;s<t.length;++s)t[s]>d[s]&&(d[s]=t[s],u[s]=e)}const _=new Array(i.dims[0]);for(let e=0;e<u.length;++e){const t=u[e];_[t]=t}const p=_.filter((e=>void 0!==e));n.push({segmentation:c,labels:p})}return n}function m(e,t,s,r){const o=[],i=[],a=[];for(let l=0;l<e.dims[0];++l){const c=e[l],d=t[l],u=(0,n.max)(c.data)[1];if(u===r)continue;const _=(0,n.softmax)(c.data)[u];_>s&&(o.push(d),i.push(_),a.push(u))}return[o,i,a]}function h(e,t,s,r=.5,o=.8){const n=[];let i=0,a=0;const l=t[s].data;for(let t=0;t<e.length;++t)e[t]===s&&(n.push(t),++i),l[t]>=r&&++a;let c=i>0&&a>0;if(c){c=i/a>o}return[c,n]}function g(e,t,s,r,n,i=null,a=null){const[l,c]=a??e[0].dims,d=new o.Tensor("int32",new Int32Array(l*c),[l,c]),u=[];if(null!==a)for(let t=0;t<e.length;++t)e[t]=(0,o.interpolate)(e[t],a,"bilinear",!1);const _=new Int32Array(e[0].data.length),p=new Float32Array(e[0].data.length);for(let s=0;s<e.length;++s){let r=t[s];const o=e[s].data;for(let e=0;e<o.length;++e)o[e]*=r,o[e]>p[e]&&(_[e]=s,p[e]=o[e])}let m=0;const g=d.data;for(let o=0;o<s.length;++o){const i=s[o],[a,l]=h(_,e,o,r,n);if(a){++m;for(const e of l)g[e]=m;u.push({id:m,label_id:i,score:t[o]})}}return[d,u]}function f(e,t=.5,s=.5,r=.8,n=null,i=null){null===n&&(console.warn("`label_ids_to_fuse` unset. No instance will be fused."),n=new Set);const a=e.class_queries_logits??e.logits,l=(e.masks_queries_logits??e.pred_masks).sigmoid();let[c,d,u]=a.dims;if(u-=1,null!==i&&i.length!==c)throw Error("Make sure that you pass in as many target sizes as the batch dimension of the logits");let _=[];for(let e=0;e<c;++e){let c=null!==i?i[e]:null,d=a[e],p=l[e],[h,f,w]=m(d,p,t,u);if(0===w.length){let[e,t]=c??p.dims.slice(-2),s=new o.Tensor("int32",new Int32Array(e*t).fill(-1),[e,t]);_.push({segmentation:s,segments_info:[]});continue}let[M,x]=g(h,f,w,s,r,n,c);_.push({segmentation:M,segments_info:x})}return _}function w(e,t=.5,s=null){throw new Error("`post_process_instance_segmentation` is not yet implemented.")}class M extends r.Callable{constructor(e){super(),this.image_mean=e.image_mean??e.mean,this.image_std=e.image_std??e.std,this.resample=e.resample??2,this.do_rescale=e.do_rescale??!0,this.rescale_factor=e.rescale_factor??1/255,this.do_normalize=e.do_normalize,this.do_thumbnail=e.do_thumbnail,this.size=e.size??e.image_size,this.do_resize=e.do_resize??void 0!==this.size,this.size_divisibility=e.size_divisibility??e.size_divisor,this.do_center_crop=e.do_center_crop,this.crop_size=e.crop_size,this.do_convert_rgb=e.do_convert_rgb??!0,this.do_crop_margin=e.do_crop_margin,this.pad_size=e.pad_size,this.do_pad=e.do_pad,this.min_pixels=e.min_pixels,this.max_pixels=e.max_pixels,this.do_pad&&!this.pad_size&&this.size&&void 0!==this.size.width&&void 0!==this.size.height&&(this.pad_size=this.size),this.do_flip_channel_order=e.do_flip_channel_order??!1,this.config=e}async thumbnail(e,t,s=2){const r=e.height,o=e.width,n=t.height,i=t.width;let a=Math.min(r,n),l=Math.min(o,i);return a===r&&l===o?e:(r>o?l=Math.floor(o*a/r):o>r&&(a=Math.floor(r*l/o)),await e.resize(l,a,{resample:s}))}async crop_margin(e,t=200){const s=e.clone().grayscale(),r=(0,n.min)(s.data)[0],o=(0,n.max)(s.data)[0]-r;if(0===o)return e;const i=t/255;let a=s.width,l=s.height,c=0,d=0;const u=s.data;for(let e=0;e<s.height;++e){const t=e*s.width;for(let n=0;n<s.width;++n)(u[t+n]-r)/o<i&&(a=Math.min(a,n),l=Math.min(l,e),c=Math.max(c,n),d=Math.max(d,e))}return e=await e.crop([a,l,c,d])}pad_image(e,t,s,{mode:r="constant",center:o=!1,constant_values:n=0}={}){const[a,l,c]=t;let d,u;if("number"==typeof s?(d=s,u=s):"square"===s?d=u=Math.max(a,l):(d=s.width,u=s.height),d!==l||u!==a){const s=new Float32Array(d*u*c);if(Array.isArray(n))for(let e=0;e<s.length;++e)s[e]=n[e%c];else 0!==n&&s.fill(n);const[_,p]=o?[Math.floor((d-l)/2),Math.floor((u-a)/2)]:[0,0];for(let t=0;t<a;++t){const r=(t+p)*d,o=t*l;for(let t=0;t<l;++t){const n=(r+t+_)*c,i=(o+t)*c;for(let t=0;t<c;++t)s[n+t]=e[i+t]}}if("symmetric"===r){if(o)throw new Error("`center` padding is not supported when `mode` is set to `symmetric`.");const t=a-1,r=l-1;for(let o=0;o<u;++o){const n=o*d,u=(0,i.calculateReflectOffset)(o,t)*l;for(let t=0;t<d;++t){if(o<a&&t<l)continue;const d=(n+t)*c,_=(u+(0,i.calculateReflectOffset)(t,r))*c;for(let t=0;t<c;++t)s[d+t]=e[_+t]}}}e=s,t=[u,d,c]}return[e,t]}rescale(e){for(let t=0;t<e.length;++t)e[t]=this.rescale_factor*e[t]}get_resize_output_image_size(e,t){const[s,r]=e.size;let o,n;if(this.do_thumbnail){const{height:e,width:s}=t;o=Math.min(e,s)}else Number.isInteger(t)?(o=t,n=this.config.max_size??o):void 0!==t&&(o=t.shortest_edge,n=t.longest_edge);if(void 0!==o||void 0!==n){const e=void 0===o?1:Math.max(o/s,o/r),t=s*e,i=r*e,a=void 0===n?1:Math.min(n/t,n/i);let l=Math.floor(Number((t*a).toFixed(2))),c=Math.floor(Number((i*a).toFixed(2)));return void 0!==this.size_divisibility&&([l,c]=d([l,c],this.size_divisibility)),[l,c]}if(void 0!==t&&void 0!==t.width&&void 0!==t.height){let e=t.width,o=t.height;if(this.config.keep_aspect_ratio&&this.config.ensure_multiple_of){let t=o/r,n=e/s;Math.abs(1-n)<Math.abs(1-t)?t=n:n=t,o=c(t*r,this.config.ensure_multiple_of),e=c(n*s,this.config.ensure_multiple_of)}return[e,o]}if(void 0!==this.size_divisibility)return d([s,r],this.size_divisibility);if(void 0!==this.min_pixels&&void 0!==this.max_pixels){return function(e,t,s=28,r=3136,o=1003520){if(e<s||t<s)throw new Error(`height:${e} or width:${t} must be larger than factor:${s}`);if(Math.max(e,t)/Math.min(e,t)>200)throw new Error("absolute aspect ratio must be smaller than 200, got "+Math.max(e,t)/Math.min(e,t));let n=Math.round(e/s)*s,i=Math.round(t/s)*s;if(n*i>o){const r=Math.sqrt(e*t/o);n=Math.floor(e/r/s)*s,i=Math.floor(t/r/s)*s}else if(n*i<r){const o=Math.sqrt(r/(e*t));n=Math.ceil(e*o/s)*s,i=Math.ceil(t*o/s)*s}return[n,i]}(r,s,this.config.patch_size*this.config.merge_size,this.min_pixels,this.max_pixels)}throw new Error(`Could not resize image due to unsupported \`this.size\` option in config: ${JSON.stringify(t)}`)}async resize(e){const[t,s]=this.get_resize_output_image_size(e,this.size);return await e.resize(t,s,{resample:this.resample})}async preprocess(e,{do_normalize:t=null,do_pad:s=null,do_convert_rgb:r=null,do_convert_grayscale:n=null,do_flip_channel_order:i=null}={}){this.do_crop_margin&&(e=await this.crop_margin(e));const[a,l]=e.size;if(r??this.do_convert_rgb?e=e.rgb():n&&(e=e.grayscale()),this.do_resize&&(e=await this.resize(e)),this.do_thumbnail&&(e=await this.thumbnail(e,this.size,this.resample)),this.do_center_crop){let t,s;Number.isInteger(this.crop_size)?(t=this.crop_size,s=this.crop_size):(t=this.crop_size.width,s=this.crop_size.height),e=await e.center_crop(t,s)}const c=[e.height,e.width];let u=Float32Array.from(e.data),_=[e.height,e.width,e.channels];if(this.do_rescale&&this.rescale(u),t??this.do_normalize){let t=this.image_mean;Array.isArray(this.image_mean)||(t=new Array(e.channels).fill(t));let s=this.image_std;if(Array.isArray(this.image_std)||(s=new Array(e.channels).fill(t)),t.length!==e.channels||s.length!==e.channels)throw new Error(`When set to arrays, the length of \`image_mean\` (${t.length}) and \`image_std\` (${s.length}) must match the number of channels in the image (${e.channels}).`);for(let r=0;r<u.length;r+=e.channels)for(let o=0;o<e.channels;++o)u[r+o]=(u[r+o]-t[o])/s[o]}if(s??this.do_pad)if(this.pad_size){const t=this.pad_image(u,[e.height,e.width,e.channels],this.pad_size);[u,_]=t}else if(this.size_divisibility){const[e,t]=d([_[1],_[0]],this.size_divisibility);[u,_]=this.pad_image(u,_,{width:e,height:t})}if(i??this.do_flip_channel_order){if(3!==_[2])throw new Error("Flipping channel order is only supported for RGB images.");for(let e=0;e<u.length;e+=3){const t=u[e];u[e]=u[e+2],u[e+2]=t}}return{original_size:[l,a],reshaped_input_size:c,pixel_values:new o.Tensor("float32",u,_).permute(2,0,1)}}async _call(e,...t){Array.isArray(e)||(e=[e]);const s=await Promise.all(e.map((e=>this.preprocess(e))));return{pixel_values:(0,o.stack)(s.map((e=>e.pixel_values)),0),original_sizes:s.map((e=>e.original_size)),reshaped_input_sizes:s.map((e=>e.reshaped_input_size))}}static async from_pretrained(e,t={}){return new this(await(0,a.getModelJSON)(e,l.IMAGE_PROCESSOR_NAME,!0,t))}}},"./src/base/processing_utils.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Processor:()=>i});var r=s("./src/utils/constants.js"),o=s("./src/utils/generic.js"),n=s("./src/utils/hub.js");class i extends o.Callable{static classes=["image_processor_class","tokenizer_class","feature_extractor_class"];static uses_processor_config=!1;static uses_chat_template_file=!1;constructor(e,t,s){super(),this.config=e,this.components=t,this.chat_template=s}get image_processor(){return this.components.image_processor}get tokenizer(){return this.components.tokenizer}get feature_extractor(){return this.components.feature_extractor}apply_chat_template(e,t={}){if(!this.tokenizer)throw new Error("Unable to apply chat template without a tokenizer.");return this.tokenizer.apply_chat_template(e,{tokenize:!1,chat_template:this.chat_template??void 0,...t})}batch_decode(...e){if(!this.tokenizer)throw new Error("Unable to decode without a tokenizer.");return this.tokenizer.batch_decode(...e)}decode(...e){if(!this.tokenizer)throw new Error("Unable to decode without a tokenizer.");return this.tokenizer.decode(...e)}async _call(e,...t){for(const s of[this.image_processor,this.feature_extractor,this.tokenizer])if(s)return s(e,...t);throw new Error("No image processor, feature extractor, or tokenizer found.")}static async from_pretrained(e,t={}){const[s,o,i]=await Promise.all([this.uses_processor_config?(0,n.getModelJSON)(e,r.PROCESSOR_NAME,!0,t):{},Promise.all(this.classes.filter((e=>e in this)).map((async s=>{const r=await this[s].from_pretrained(e,t);return[s.replace(/_class$/,""),r]}))).then(Object.fromEntries),this.uses_chat_template_file?(0,n.getModelText)(e,r.CHAT_TEMPLATE_NAME,!0,t):null]);return new this(s,o,i)}}},"./src/configs.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{AutoConfig:()=>l,PretrainedConfig:()=>a,getKeyValueShapes:()=>i});var r=s("./src/utils/core.js"),o=s("./src/utils/hub.js");function n(e){const t={};let s={};switch(e.model_type){case"llava":case"paligemma":case"gemma3":case"florence2":case"llava_onevision":case"idefics3":case"ultravox":case"smolvlm":case"gemma3n":s=n(e.text_config);break;case"moondream1":s=n(e.phi_config);break;case"musicgen":s=n(e.decoder);break;case"multi_modality":s=n(e.language_config);break;case"gpt2":case"gptj":case"jais":case"codegen":case"gpt_bigcode":t.num_heads="n_head",t.num_layers="n_layer",t.hidden_size="n_embd";break;case"gpt_neox":case"stablelm":case"opt":case"falcon":t.num_heads="num_attention_heads",t.num_layers="num_hidden_layers",t.hidden_size="hidden_size";break;case"llama":case"smollm3":case"olmo":case"olmo2":case"mobilellm":case"granite":case"cohere":case"mistral":case"starcoder2":case"qwen2":case"qwen2_vl":case"phi":case"phi3":case"phi3_v":case"llava_qwen2":t.num_heads="num_key_value_heads",t.num_layers="num_hidden_layers",t.hidden_size="hidden_size",t.num_attention_heads="num_attention_heads";break;case"qwen3":case"gemma":case"gemma2":case"gemma3_text":case"gemma3n_text":case"glm":case"helium":case"ernie4_5":t.num_heads="num_key_value_heads",t.num_layers="num_hidden_layers",t.dim_kv="head_dim";break;case"openelm":t.num_heads="num_kv_heads",t.num_layers="num_transformer_layers",t.dim_kv="head_dim";break;case"gpt_neo":case"donut-swin":t.num_heads="num_heads",t.num_layers="num_layers",t.hidden_size="hidden_size";break;case"bloom":t.num_heads="n_head",t.num_layers="n_layer",t.hidden_size="hidden_size";break;case"mpt":t.num_heads="n_heads",t.num_layers="n_layers",t.hidden_size="d_model";break;case"exaone":t.num_heads="num_key_value_heads",t.num_layers="num_layers",t.dim_kv="head_dim",t.num_attention_heads="num_attention_heads";break;case"t5":case"mt5":case"longt5":t.num_decoder_layers="num_decoder_layers",t.num_decoder_heads="num_heads",t.decoder_dim_kv="d_kv",t.num_encoder_layers="num_layers",t.num_encoder_heads="num_heads",t.encoder_dim_kv="d_kv";break;case"bart":case"mbart":case"marian":case"whisper":case"lite-whisper":case"m2m_100":case"blenderbot":case"blenderbot-small":case"florence2_language":t.num_decoder_layers="decoder_layers",t.num_decoder_heads="decoder_attention_heads",t.decoder_hidden_size="d_model",t.num_encoder_layers="encoder_layers",t.num_encoder_heads="encoder_attention_heads",t.encoder_hidden_size="d_model";break;case"speecht5":t.num_decoder_layers="decoder_layers",t.num_decoder_heads="decoder_attention_heads",t.decoder_hidden_size="hidden_size",t.num_encoder_layers="encoder_layers",t.num_encoder_heads="encoder_attention_heads",t.encoder_hidden_size="hidden_size";break;case"trocr":t.num_encoder_layers=t.num_decoder_layers="decoder_layers",t.num_encoder_heads=t.num_decoder_heads="decoder_attention_heads",t.encoder_hidden_size=t.decoder_hidden_size="d_model";break;case"musicgen_decoder":t.num_encoder_layers=t.num_decoder_layers="num_hidden_layers",t.num_encoder_heads=t.num_decoder_heads="num_attention_heads",t.encoder_hidden_size=t.decoder_hidden_size="hidden_size";break;case"moonshine":t.num_decoder_layers="decoder_num_hidden_layers",t.num_decoder_heads="decoder_num_key_value_heads",t.num_encoder_layers="encoder_num_hidden_layers",t.num_encoder_heads="encoder_num_key_value_heads",t.encoder_hidden_size=t.decoder_hidden_size="hidden_size";break;case"vision-encoder-decoder":const o=n(e.decoder),i="num_decoder_layers"in o,a=(0,r.pick)(e,["model_type","is_encoder_decoder"]);return i?(a.num_decoder_layers=o.num_decoder_layers,a.num_decoder_heads=o.num_decoder_heads,a.decoder_hidden_size=o.decoder_hidden_size,a.num_encoder_layers=o.num_encoder_layers,a.num_encoder_heads=o.num_encoder_heads,a.encoder_hidden_size=o.encoder_hidden_size):(a.num_layers=o.num_layers,a.num_heads=o.num_heads,a.hidden_size=o.hidden_size),a}const o={...s,...(0,r.pick)(e,["model_type","multi_query","is_encoder_decoder"])};for(const s in t)o[s]=e[t[s]];return o}function i(e,{prefix:t="past_key_values",batch_size:s=1}={}){const r={},o=e.normalized_config;if(o.is_encoder_decoder&&"num_encoder_heads"in o&&"num_decoder_heads"in o){const e=o.encoder_dim_kv??o.encoder_hidden_size/o.num_encoder_heads,n=o.decoder_dim_kv??o.decoder_hidden_size/o.num_decoder_heads,i=[s,o.num_encoder_heads,0,e],a=[s,o.num_decoder_heads,0,n];for(let e=0;e<o.num_decoder_layers;++e)r[`${t}.${e}.encoder.key`]=i,r[`${t}.${e}.encoder.value`]=i,r[`${t}.${e}.decoder.key`]=a,r[`${t}.${e}.decoder.value`]=a}else{const e=o.num_heads,n=o.num_layers,i=o.dim_kv??o.hidden_size/(o.num_attention_heads??e);if("falcon"===o.model_type){const o=[s*e,0,i];for(let e=0;e<n;++e)r[`${t}.${e}.key`]=o,r[`${t}.${e}.value`]=o}else if(o.multi_query){const o=[s*e,0,2*i];for(let e=0;e<n;++e)r[`${t}.${e}.key_value`]=o}else if("bloom"===o.model_type){const o=[s*e,i,0],a=[s*e,0,i];for(let e=0;e<n;++e)r[`${t}.${e}.key`]=o,r[`${t}.${e}.value`]=a}else if("openelm"===o.model_type)for(let o=0;o<n;++o){const n=[s,e[o],0,i];r[`${t}.${o}.key`]=n,r[`${t}.${o}.value`]=n}else{const o=[s,e,0,i];for(let e=0;e<n;++e)r[`${t}.${e}.key`]=o,r[`${t}.${e}.value`]=o}}return r}class a{model_type=null;is_encoder_decoder=!1;max_position_embeddings;"transformers.js_config";constructor(e){Object.assign(this,e),this.normalized_config=n(this)}static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:r=null,local_files_only:n=!1,revision:i="main"}={}){!s||s instanceof a||(s=new a(s));const l=s??await async function(e,t){return await(0,o.getModelJSON)(e,"config.json",!0,t)}(e,{progress_callback:t,config:s,cache_dir:r,local_files_only:n,revision:i});return new this(l)}}class l{static async from_pretrained(...e){return a.from_pretrained(...e)}}},"./src/env.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{apis:()=>g,env:()=>b});var r=s("node:fs"),o=s("node:path"),n=s("node:url");const i="undefined"!=typeof window&&void 0!==window.document,a="undefined"!=typeof self&&["DedicatedWorkerGlobalScope","ServiceWorkerGlobalScope","SharedWorkerGlobalScope"].includes(self.constructor?.name),l="undefined"!=typeof self&&"caches"in self,c="undefined"!=typeof navigator&&"gpu"in navigator,d="undefined"!=typeof navigator&&"ml"in navigator,u="undefined"!=typeof process,_=u&&"node"===process?.release?.name,p=!k(r),m=!k(o),h=void 0!==globalThis.Deno,g=(globalThis.Bun,Object.freeze({IS_BROWSER_ENV:i,IS_WEBWORKER_ENV:a,IS_WEB_CACHE_AVAILABLE:l,IS_WEBGPU_AVAILABLE:c,IS_WEBNN_AVAILABLE:d,IS_PROCESS_AVAILABLE:u,IS_NODE_ENV:_,IS_FS_AVAILABLE:p,IS_PATH_AVAILABLE:m})),f=p&&m;let w="./";if(f){const e=Object({}).url;e?w=o.dirname(o.dirname(n.fileURLToPath(e))):"undefined"!=typeof __dirname&&(w=o.dirname(__dirname))}const M=f?o.join(w,"/.cache/"):null,x="/models/",b={version:"3.6.2",backends:{onnx:{}},allowRemoteModels:!0,remoteHost:"https://huggingface.co/",remotePathTemplate:"{model}/resolve/{revision}/",allowLocalModels:!(i||a),localModelPath:f?o.join(w,x):x,useFS:p,useBrowserCache:l&&!h,useFSCache:p,cacheDir:M,useCustomCache:!1,customCache:null};function k(e){return 0===Object.keys(e).length}},"./src/generation/configuration_utils.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{GenerationConfig:()=>o});var r=s("./src/utils/core.js");class o{max_length=20;max_new_tokens=null;min_length=0;min_new_tokens=null;early_stopping=!1;max_time=null;do_sample=!1;num_beams=1;num_beam_groups=1;penalty_alpha=null;use_cache=!0;temperature=1;top_k=50;top_p=1;typical_p=1;epsilon_cutoff=0;eta_cutoff=0;diversity_penalty=0;repetition_penalty=1;encoder_repetition_penalty=1;length_penalty=1;no_repeat_ngram_size=0;bad_words_ids=null;force_words_ids=null;renormalize_logits=!1;constraints=null;forced_bos_token_id=null;forced_eos_token_id=null;remove_invalid_values=!1;exponential_decay_length_penalty=null;suppress_tokens=null;streamer=null;begin_suppress_tokens=null;forced_decoder_ids=null;guidance_scale=null;num_return_sequences=1;output_attentions=!1;output_hidden_states=!1;output_scores=!1;return_dict_in_generate=!1;pad_token_id=null;bos_token_id=null;eos_token_id=null;encoder_no_repeat_ngram_size=0;decoder_start_token_id=null;generation_kwargs={};constructor(e){Object.assign(this,(0,r.pick)(e,Object.getOwnPropertyNames(this)))}}},"./src/generation/logits_process.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{ClassifierFreeGuidanceLogitsProcessor:()=>f,ForcedBOSTokenLogitsProcessor:()=>l,ForcedEOSTokenLogitsProcessor:()=>c,LogitsProcessor:()=>n,LogitsProcessorList:()=>a,LogitsWarper:()=>i,MinLengthLogitsProcessor:()=>m,MinNewTokensLengthLogitsProcessor:()=>h,NoBadWordsLogitsProcessor:()=>g,NoRepeatNGramLogitsProcessor:()=>_,RepetitionPenaltyLogitsProcessor:()=>p,SuppressTokensAtBeginLogitsProcessor:()=>d,TemperatureLogitsWarper:()=>w,TopKLogitsWarper:()=>x,TopPLogitsWarper:()=>M,WhisperTimeStampLogitsProcessor:()=>u});var r=s("./src/utils/generic.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/maths.js"));class n extends r.Callable{_call(e,t){throw Error("`_call` should be implemented in a subclass")}}class i extends r.Callable{_call(e,t){throw Error("`_call` should be implemented in a subclass")}}class a extends r.Callable{constructor(){super(),this.processors=[]}push(e){this.processors.push(e)}extend(e){this.processors.push(...e)}_call(e,t){let s=t;for(const t of this.processors)s=t(e,s);return s}[Symbol.iterator](){return this.processors.values()}}class l extends n{constructor(e){super(),this.bos_token_id=e}_call(e,t){for(let s=0;s<e.length;++s)if(1===e[s].length){const e=t[s].data;e.fill(-1/0),e[this.bos_token_id]=0}return t}}class c extends n{constructor(e,t){super(),this.max_length=e,this.eos_token_id=Array.isArray(t)?t:[t]}_call(e,t){for(let s=0;s<e.length;++s)if(e[s].length===this.max_length-1){const e=t[s].data;e.fill(-1/0);for(const t of this.eos_token_id)e[t]=0}return t}}class d extends n{constructor(e,t){super(),this.begin_suppress_tokens=e,this.begin_index=t}_call(e,t){for(let s=0;s<e.length;++s)if(e[s].length===this.begin_index){const e=t[s].data;for(const t of this.begin_suppress_tokens)e[t]=-1/0}return t}}class u extends n{constructor(e,t){super(),this.eos_token_id=Array.isArray(e.eos_token_id)?e.eos_token_id[0]:e.eos_token_id,this.no_timestamps_token_id=e.no_timestamps_token_id,this.timestamp_begin=this.no_timestamps_token_id+1,this.begin_index=t.length,t.at(-1)===this.no_timestamps_token_id&&(this.begin_index-=1),this.max_initial_timestamp_index=e.max_initial_timestamp_index}_call(e,t){for(let s=0;s<e.length;++s){const r=t[s].data;if(r[this.no_timestamps_token_id]=-1/0,e[s].length===this.begin_index-1){r.fill(-1/0),r[this.timestamp_begin]=0;continue}const n=e[s].slice(this.begin_index),i=n.length>=1&&n[n.length-1]>=this.timestamp_begin,a=n.length<2||n[n.length-2]>=this.timestamp_begin;if(i&&(a?r.subarray(this.timestamp_begin).fill(-1/0):r.subarray(0,this.eos_token_id).fill(-1/0)),e[s].length===this.begin_index&&null!==this.max_initial_timestamp_index){const e=this.timestamp_begin+this.max_initial_timestamp_index;r.subarray(e+1).fill(-1/0)}const l=(0,o.log_softmax)(r);Math.log(l.subarray(this.timestamp_begin).map(Math.exp).reduce(((e,t)=>e+t)))>(0,o.max)(l.subarray(0,this.timestamp_begin))[0]&&r.subarray(0,this.timestamp_begin).fill(-1/0)}return t}}class _ extends n{constructor(e){super(),this.no_repeat_ngram_size=e}getNgrams(e){const t=e.length,s=[];for(let r=0;r<t+1-this.no_repeat_ngram_size;++r){const t=[];for(let s=0;s<this.no_repeat_ngram_size;++s)t.push(e[r+s]);s.push(t.map(Number))}const r=new Map;for(const e of s){const t=e.slice(0,e.length-1),s=JSON.stringify(t),o=r.get(s)??[];o.push(e[e.length-1]),r.set(s,o)}return r}getGeneratedNgrams(e,t){const s=t.slice(t.length+1-this.no_repeat_ngram_size,t.length);return e.get(JSON.stringify(s.map(Number)))??[]}calcBannedNgramTokens(e){const t=[];if(e.length+1<this.no_repeat_ngram_size)return t;{const t=this.getNgrams(e);return this.getGeneratedNgrams(t,e)}}_call(e,t){for(let s=0;s<e.length;++s){const r=t[s].data,o=this.calcBannedNgramTokens(e[s]);for(const e of o)r[e]=-1/0}return t}}class p extends n{constructor(e){super(),this.penalty=e}_call(e,t){for(let s=0;s<e.length;++s){const r=t[s].data;for(const t of new Set(e[s])){const e=Number(t);r[e]<0?r[e]*=this.penalty:r[e]/=this.penalty}}return t}}class m extends n{constructor(e,t){super(),this.min_length=e,this.eos_token_id=Array.isArray(t)?t:[t]}_call(e,t){for(let s=0;s<e.length;++s)if(e[s].length<this.min_length){const e=t[s].data;for(const t of this.eos_token_id)e[t]=-1/0}return t}}class h extends n{constructor(e,t,s){super(),this.prompt_length_to_skip=e,this.min_new_tokens=t,this.eos_token_id=Array.isArray(s)?s:[s]}_call(e,t){for(let s=0;s<e.length;++s){if(e[s].length-this.prompt_length_to_skip<this.min_new_tokens){const e=t[s].data;for(const t of this.eos_token_id)e[t]=-1/0}}return t}}class g extends n{constructor(e,t){super(),this.bad_words_ids=e,this.eos_token_id=Array.isArray(t)?t:[t]}_call(e,t){for(let s=0;s<e.length;++s){const r=t[s].data,o=e[s];for(const e of this.bad_words_ids){if(o.length<e.length-1)continue;let t=!0;for(let s=1;s<=e.length-1;++s)if(e.at(-s-1)!=o.at(-s)){t=!1;break}t&&(r[e.at(-1)]=-1/0)}}return t}}class f extends n{constructor(e){if(super(),e<=1)throw new Error(`Require guidance scale >1 to use the classifier free guidance processor, got guidance scale ${e}.`);this.guidance_scale=e}_call(e,t){if(t.dims[0]!==2*e.length)throw new Error(`Logits should have twice the batch size of the input ids, the first half of batches corresponding to the conditional inputs, and the second half of batches corresponding to the unconditional inputs. Got batch size ${t.dims[0]} for the logits and ${e.length} for the input ids.`);const s=e.length,r=t.slice([0,s],null),o=t.slice([s,t.dims[0]],null);for(let e=0;e<o.data.length;++e)o.data[e]+=(r.data[e]-o.data[e])*this.guidance_scale;return o}}class w extends i{constructor(e){if(super(),"number"!=typeof e||e<=0){let t=`\`temperature\` (=${e}) must be a strictly positive float, otherwise your next token scores will be invalid.`;0===e&&(t+=" If you're looking for greedy decoding strategies, set `do_sample=false`.")}this.temperature=e}_call(e,t){const s=t.data;for(let e=0;e<s.length;++e)s[e]/=this.temperature;return t}}class M extends i{constructor(e,{filter_value:t=-1/0,min_tokens_to_keep:s=1}={}){if(super(),e<0||e>1)throw new Error(`\`top_p\` must be a float > 0 and < 1, but is ${e}`);if(!Number.isInteger(s)||s<1)throw new Error(`\`min_tokens_to_keep\` must be a positive integer, but is ${s}`);this.top_p=e,this.filter_value=t,this.min_tokens_to_keep=s}}class x extends i{constructor(e,{filter_value:t=-1/0,min_tokens_to_keep:s=1}={}){if(super(),!Number.isInteger(e)||e<0)throw new Error(`\`top_k\` must be a positive integer, but is ${e}`);this.top_k=Math.max(e,s),this.filter_value=t}}},"./src/generation/logits_sampler.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{LogitsSampler:()=>i});var r=s("./src/utils/generic.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/maths.js");s("./src/generation/configuration_utils.js");class i extends r.Callable{constructor(e){super(),this.generation_config=e}async _call(e){return this.sample(e)}async sample(e){throw Error("sample should be implemented in subclasses.")}getLogits(e,t){let s=e.dims.at(-1),r=e.data;if(-1===t)r=r.slice(-s);else{let e=t*s;r=r.slice(e,e+s)}return r}randomSelect(e){let t=0;for(let s=0;s<e.length;++s)t+=e[s];let s=Math.random()*t;for(let t=0;t<e.length;++t)if(s-=e[t],s<=0)return t;return 0}static getSampler(e){if(e.do_sample)return new l(e);if(e.num_beams>1)return new c(e);if(e.num_return_sequences>1)throw Error(`num_return_sequences has to be 1 when doing greedy search, but is ${e.num_return_sequences}.`);return new a(e)}}class a extends i{async sample(e){const t=(0,n.max)(e.data)[1];return[[BigInt(t),0]]}}class l extends i{async sample(e){let t=e.dims.at(-1);this.generation_config.top_k>0&&(t=Math.min(this.generation_config.top_k,t));const[s,r]=await(0,o.topk)(e,t),i=(0,n.softmax)(s.data);return Array.from({length:this.generation_config.num_beams},(()=>{const e=this.randomSelect(i);return[r.data[e],Math.log(i[e])]}))}}class c extends i{async sample(e){let t=e.dims.at(-1);this.generation_config.top_k>0&&(t=Math.min(this.generation_config.top_k,t));const[s,r]=await(0,o.topk)(e,t),i=(0,n.softmax)(s.data);return Array.from({length:this.generation_config.num_beams},((e,t)=>[r.data[t],Math.log(i[t])]))}}},"./src/generation/stopping_criteria.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{EosTokenCriteria:()=>a,InterruptableStoppingCriteria:()=>l,MaxLengthCriteria:()=>i,StoppingCriteria:()=>o,StoppingCriteriaList:()=>n});var r=s("./src/utils/generic.js");class o extends r.Callable{_call(e,t){throw Error("StoppingCriteria needs to be subclassed")}}class n extends r.Callable{constructor(){super(),this.criteria=[]}push(e){this.criteria.push(e)}extend(e){e instanceof n?e=e.criteria:e instanceof o&&(e=[e]),this.criteria.push(...e)}_call(e,t){const s=new Array(e.length).fill(!1);for(const r of this.criteria){const o=r(e,t);for(let e=0;e<s.length;++e)s[e]||=o[e]}return s}[Symbol.iterator](){return this.criteria.values()}}class i extends o{constructor(e,t=null){super(),this.max_length=e,this.max_position_embeddings=t}_call(e){return e.map((e=>e.length>=this.max_length))}}class a extends o{constructor(e){super(),Array.isArray(e)||(e=[e]),this.eos_token_id=e}_call(e,t){return e.map((e=>{const t=e.at(-1);return this.eos_token_id.some((e=>t==e))}))}}class l extends o{constructor(){super(),this.interrupted=!1}interrupt(){this.interrupted=!0}reset(){this.interrupted=!1}_call(e,t){return new Array(e.length).fill(this.interrupted)}}},"./src/generation/streamers.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{BaseStreamer:()=>i,TextStreamer:()=>l,WhisperTextStreamer:()=>c});var r=s("./src/utils/core.js"),o=s("./src/tokenizers.js"),n=s("./src/env.js");class i{put(e){throw Error("Not implemented")}end(){throw Error("Not implemented")}}const a=n.apis.IS_PROCESS_AVAILABLE?e=>process.stdout.write(e):e=>console.log(e);class l extends i{constructor(e,{skip_prompt:t=!1,callback_function:s=null,token_callback_function:r=null,skip_special_tokens:o=!0,decode_kwargs:n={},...i}={}){super(),this.tokenizer=e,this.skip_prompt=t,this.callback_function=s??a,this.token_callback_function=r,this.decode_kwargs={skip_special_tokens:o,...n,...i},this.token_cache=[],this.print_len=0,this.next_tokens_are_prompt=!0}put(e){if(e.length>1)throw Error("TextStreamer only supports batch size of 1");const t=this.next_tokens_are_prompt;if(t&&(this.next_tokens_are_prompt=!1,this.skip_prompt))return;const s=e[0];this.token_callback_function?.(s),this.token_cache=(0,r.mergeArrays)(this.token_cache,s);const n=this.tokenizer.decode(this.token_cache,this.decode_kwargs);let i;t||n.endsWith("\n")?(i=n.slice(this.print_len),this.token_cache=[],this.print_len=0):n.length>0&&(0,o.is_chinese_char)(n.charCodeAt(n.length-1))?(i=n.slice(this.print_len),this.print_len+=i.length):(i=n.slice(this.print_len,n.lastIndexOf(" ")+1),this.print_len+=i.length),this.on_finalized_text(i,!1)}end(){let e;if(this.token_cache.length>0){e=this.tokenizer.decode(this.token_cache,this.decode_kwargs).slice(this.print_len),this.token_cache=[],this.print_len=0}else e="";this.next_tokens_are_prompt=!0,this.on_finalized_text(e,!0)}on_finalized_text(e,t){e.length>0&&this.callback_function?.(e),t&&this.callback_function===a&&n.apis.IS_PROCESS_AVAILABLE&&this.callback_function?.("\n")}}class c extends l{constructor(e,{skip_prompt:t=!1,callback_function:s=null,token_callback_function:r=null,on_chunk_start:o=null,on_chunk_end:n=null,on_finalize:i=null,time_precision:a=.02,skip_special_tokens:l=!0,decode_kwargs:c={}}={}){super(e,{skip_prompt:t,skip_special_tokens:l,callback_function:s,token_callback_function:r,decode_kwargs:c}),this.timestamp_begin=e.timestamp_begin,this.on_chunk_start=o,this.on_chunk_end=n,this.on_finalize=i,this.time_precision=a,this.waiting_for_timestamp=!1}put(e){if(e.length>1)throw Error("WhisperTextStreamer only supports batch size of 1");const t=e[0];if(1===t.length){const e=Number(t[0])-this.timestamp_begin;if(e>=0){const s=e*this.time_precision;return this.waiting_for_timestamp?this.on_chunk_end?.(s):this.on_chunk_start?.(s),this.waiting_for_timestamp=!this.waiting_for_timestamp,void this.token_callback_function?.(t)}}return super.put(e)}end(){super.end(),this.on_finalize?.()}}},"./src/models.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{ASTForAudioClassification:()=>Es,ASTModel:()=>Ss,ASTPreTrainedModel:()=>Cs,AlbertForMaskedLM:()=>Vt,AlbertForQuestionAnswering:()=>Nt,AlbertForSequenceClassification:()=>Ot,AlbertModel:()=>Dt,AlbertPreTrainedModel:()=>jt,AutoModel:()=>xd,AutoModelForAudioClassification:()=>Vd,AutoModelForAudioFrameClassification:()=>Gd,AutoModelForAudioTextToText:()=>Jd,AutoModelForCTC:()=>Nd,AutoModelForCausalLM:()=>Fd,AutoModelForDepthEstimation:()=>Wd,AutoModelForDocumentQuestionAnswering:()=>Rd,AutoModelForImageClassification:()=>Ad,AutoModelForImageFeatureExtraction:()=>Xd,AutoModelForImageMatting:()=>$d,AutoModelForImageSegmentation:()=>Ld,AutoModelForImageTextToText:()=>Hd,AutoModelForImageToImage:()=>qd,AutoModelForMaskGeneration:()=>Od,AutoModelForMaskedLM:()=>Cd,AutoModelForNormalEstimation:()=>Ud,AutoModelForObjectDetection:()=>jd,AutoModelForPoseEstimation:()=>Qd,AutoModelForQuestionAnswering:()=>Sd,AutoModelForSemanticSegmentation:()=>Id,AutoModelForSeq2SeqLM:()=>yd,AutoModelForSequenceClassification:()=>bd,AutoModelForSpeechSeq2Seq:()=>vd,AutoModelForTextToSpectrogram:()=>Td,AutoModelForTextToWaveform:()=>Pd,AutoModelForTokenClassification:()=>kd,AutoModelForUniversalSegmentation:()=>zd,AutoModelForVision2Seq:()=>Ed,AutoModelForXVector:()=>Bd,AutoModelForZeroShotObjectDetection:()=>Dd,BartForConditionalGeneration:()=>Yt,BartForSequenceClassification:()=>Kt,BartModel:()=>Jt,BartPretrainedModel:()=>Ht,BaseModelOutput:()=>ie,BeitForImageClassification:()=>On,BeitModel:()=>Dn,BeitPreTrainedModel:()=>jn,BertForMaskedLM:()=>ce,BertForQuestionAnswering:()=>_e,BertForSequenceClassification:()=>de,BertForTokenClassification:()=>ue,BertModel:()=>le,BertPreTrainedModel:()=>ae,BlenderbotForConditionalGeneration:()=>is,BlenderbotModel:()=>ns,BlenderbotPreTrainedModel:()=>os,BlenderbotSmallForConditionalGeneration:()=>cs,BlenderbotSmallModel:()=>ls,BlenderbotSmallPreTrainedModel:()=>as,BloomForCausalLM:()=>Uo,BloomModel:()=>Wo,BloomPreTrainedModel:()=>qo,CLIPModel:()=>sr,CLIPPreTrainedModel:()=>tr,CLIPSegForImageSegmentation:()=>Mr,CLIPSegModel:()=>wr,CLIPSegPreTrainedModel:()=>fr,CLIPTextModel:()=>rr,CLIPTextModelWithProjection:()=>or,CLIPVisionModel:()=>nr,CLIPVisionModelWithProjection:()=>ir,CamembertForMaskedLM:()=>Ue,CamembertForQuestionAnswering:()=>He,CamembertForSequenceClassification:()=>Qe,CamembertForTokenClassification:()=>Xe,CamembertModel:()=>We,CamembertPreTrainedModel:()=>qe,CausalLMOutput:()=>ru,CausalLMOutputWithPast:()=>ou,ChineseCLIPModel:()=>_r,ChineseCLIPPreTrainedModel:()=>ur,ClapAudioModelWithProjection:()=>vl,ClapModel:()=>kl,ClapPreTrainedModel:()=>bl,ClapTextModelWithProjection:()=>yl,CodeGenForCausalLM:()=>Br,CodeGenModel:()=>Vr,CodeGenPreTrainedModel:()=>Nr,CohereForCausalLM:()=>fo,CohereModel:()=>go,CoherePreTrainedModel:()=>ho,ConvBertForMaskedLM:()=>ze,ConvBertForQuestionAnswering:()=>Oe,ConvBertForSequenceClassification:()=>je,ConvBertForTokenClassification:()=>De,ConvBertModel:()=>Ie,ConvBertPreTrainedModel:()=>Le,ConvNextForImageClassification:()=>Ji,ConvNextModel:()=>Hi,ConvNextPreTrainedModel:()=>Xi,ConvNextV2ForImageClassification:()=>Zi,ConvNextV2Model:()=>Ki,ConvNextV2PreTrainedModel:()=>Yi,DFineForObjectDetection:()=>oi,DFineModel:()=>ri,DFinePreTrainedModel:()=>si,DPTForDepthEstimation:()=>Fi,DPTModel:()=>Pi,DPTPreTrainedModel:()=>Ti,DacDecoderModel:()=>Sc,DacDecoderOutput:()=>Pc,DacEncoderModel:()=>Cc,DacEncoderOutput:()=>Tc,DacModel:()=>Fc,DacPreTrainedModel:()=>vc,DebertaForMaskedLM:()=>Ke,DebertaForQuestionAnswering:()=>tt,DebertaForSequenceClassification:()=>Ze,DebertaForTokenClassification:()=>et,DebertaModel:()=>Ye,DebertaPreTrainedModel:()=>Je,DebertaV2ForMaskedLM:()=>ot,DebertaV2ForQuestionAnswering:()=>at,DebertaV2ForSequenceClassification:()=>nt,DebertaV2ForTokenClassification:()=>it,DebertaV2Model:()=>rt,DebertaV2PreTrainedModel:()=>st,DecisionTransformerModel:()=>oc,DecisionTransformerPreTrainedModel:()=>rc,DeiTForImageClassification:()=>ui,DeiTModel:()=>di,DeiTPreTrainedModel:()=>ci,DepthAnythingForDepthEstimation:()=>Si,DepthAnythingPreTrainedModel:()=>Ci,DepthProForDepthEstimation:()=>ji,DepthProPreTrainedModel:()=>zi,DetrForObjectDetection:()=>Bn,DetrForSegmentation:()=>Gn,DetrModel:()=>Vn,DetrObjectDetectionOutput:()=>Rn,DetrPreTrainedModel:()=>Nn,DetrSegmentationOutput:()=>$n,Dinov2ForImageClassification:()=>sa,Dinov2Model:()=>ta,Dinov2PreTrainedModel:()=>ea,Dinov2WithRegistersForImageClassification:()=>na,Dinov2WithRegistersModel:()=>oa,Dinov2WithRegistersPreTrainedModel:()=>ra,DistilBertForMaskedLM:()=>pt,DistilBertForQuestionAnswering:()=>_t,DistilBertForSequenceClassification:()=>dt,DistilBertForTokenClassification:()=>ut,DistilBertModel:()=>ct,DistilBertPreTrainedModel:()=>lt,DonutSwinModel:()=>Qi,DonutSwinPreTrainedModel:()=>Ui,EfficientNetForImageClassification:()=>Dl,EfficientNetModel:()=>jl,EfficientNetPreTrainedModel:()=>zl,ElectraForMaskedLM:()=>Be,ElectraForQuestionAnswering:()=>$e,ElectraForSequenceClassification:()=>Ge,ElectraForTokenClassification:()=>Re,ElectraModel:()=>Ve,ElectraPreTrainedModel:()=>Ne,Ernie4_5_ForCausalLM:()=>ml,Ernie4_5_Model:()=>pl,Ernie4_5_PretrainedModel:()=>_l,EsmForMaskedLM:()=>gt,EsmForSequenceClassification:()=>ft,EsmForTokenClassification:()=>wt,EsmModel:()=>ht,EsmPreTrainedModel:()=>mt,ExaoneForCausalLM:()=>to,ExaoneModel:()=>eo,ExaonePreTrainedModel:()=>Zr,FalconForCausalLM:()=>xl,FalconModel:()=>Ml,FalconPreTrainedModel:()=>wl,FastViTForImageClassification:()=>xn,FastViTModel:()=>Mn,FastViTPreTrainedModel:()=>wn,Florence2ForConditionalGeneration:()=>qs,Florence2PreTrainedModel:()=>$s,GLPNForDepthEstimation:()=>Wi,GLPNModel:()=>qi,GLPNPreTrainedModel:()=>$i,GPT2LMHeadModel:()=>kr,GPT2Model:()=>br,GPT2PreTrainedModel:()=>xr,GPTBigCodeForCausalLM:()=>Or,GPTBigCodeModel:()=>Dr,GPTBigCodePreTrainedModel:()=>jr,GPTJForCausalLM:()=>zr,GPTJModel:()=>Ir,GPTJPreTrainedModel:()=>Lr,GPTNeoForCausalLM:()=>Cr,GPTNeoModel:()=>Fr,GPTNeoPreTrainedModel:()=>Pr,GPTNeoXForCausalLM:()=>Ar,GPTNeoXModel:()=>Er,GPTNeoXPreTrainedModel:()=>Sr,Gemma2ForCausalLM:()=>yo,Gemma2Model:()=>ko,Gemma2PreTrainedModel:()=>bo,Gemma3ForCausalLM:()=>Po,Gemma3Model:()=>To,Gemma3PreTrainedModel:()=>vo,Gemma3nForConditionalGeneration:()=>Hs,Gemma3nPreTrainedModel:()=>Xs,GemmaForCausalLM:()=>xo,GemmaModel:()=>Mo,GemmaPreTrainedModel:()=>wo,GlmForCausalLM:()=>Kr,GlmModel:()=>Yr,GlmPreTrainedModel:()=>Jr,GraniteForCausalLM:()=>mo,GraniteModel:()=>po,GranitePreTrainedModel:()=>_o,GroundingDinoForObjectDetection:()=>aa,GroundingDinoPreTrainedModel:()=>ia,GroupViTModel:()=>fn,GroupViTPreTrainedModel:()=>gn,HeliumForCausalLM:()=>Hr,HeliumModel:()=>Xr,HeliumPreTrainedModel:()=>Qr,HieraForImageClassification:()=>mi,HieraModel:()=>pi,HieraPreTrainedModel:()=>_i,HubertForCTC:()=>Ua,HubertForSequenceClassification:()=>Qa,HubertModel:()=>Wa,HubertPreTrainedModel:()=>qa,IJepaForImageClassification:()=>on,IJepaModel:()=>rn,IJepaPreTrainedModel:()=>sn,Idefics3ForConditionalGeneration:()=>Ys,Idefics3PreTrainedModel:()=>Js,ImageMattingOutput:()=>nu,JAISLMHeadModel:()=>Tr,JAISModel:()=>vr,JAISPreTrainedModel:()=>yr,JinaCLIPModel:()=>mr,JinaCLIPPreTrainedModel:()=>pr,JinaCLIPTextModel:()=>hr,JinaCLIPVisionModel:()=>gr,LiteWhisperForConditionalGeneration:()=>zs,LlamaForCausalLM:()=>$r,LlamaModel:()=>Rr,LlamaPreTrainedModel:()=>Gr,LlavaForConditionalGeneration:()=>Bs,LlavaOnevisionForConditionalGeneration:()=>Gs,LlavaPreTrainedModel:()=>Vs,LlavaQwen2ForCausalLM:()=>Qs,LongT5ForConditionalGeneration:()=>Wt,LongT5Model:()=>qt,LongT5PreTrainedModel:()=>$t,M2M100ForConditionalGeneration:()=>xa,M2M100Model:()=>Ma,M2M100PreTrainedModel:()=>wa,MBartForCausalLM:()=>rs,MBartForConditionalGeneration:()=>ts,MBartForSequenceClassification:()=>ss,MBartModel:()=>es,MBartPreTrainedModel:()=>Zt,MPNetForMaskedLM:()=>Pt,MPNetForQuestionAnswering:()=>St,MPNetForSequenceClassification:()=>Ft,MPNetForTokenClassification:()=>Ct,MPNetModel:()=>Tt,MPNetPreTrainedModel:()=>vt,MT5ForConditionalGeneration:()=>Xt,MT5Model:()=>Qt,MT5PreTrainedModel:()=>Ut,MarianMTModel:()=>fa,MarianModel:()=>ga,MarianPreTrainedModel:()=>ha,MaskFormerForInstanceSegmentation:()=>Ri,MaskFormerModel:()=>Gi,MaskFormerPreTrainedModel:()=>Bi,MaskedLMOutput:()=>tu,Metric3DForDepthEstimation:()=>Oi,Metric3DPreTrainedModel:()=>Di,Metric3Dv2ForDepthEstimation:()=>Vi,Metric3Dv2PreTrainedModel:()=>Ni,MgpstrForSceneTextRecognition:()=>cc,MgpstrModelOutput:()=>ac,MgpstrPreTrainedModel:()=>lc,MimiDecoderModel:()=>yc,MimiDecoderOutput:()=>xc,MimiEncoderModel:()=>kc,MimiEncoderOutput:()=>Mc,MimiModel:()=>bc,MimiPreTrainedModel:()=>wc,MistralForCausalLM:()=>ul,MistralModel:()=>dl,MistralPreTrainedModel:()=>cl,MobileBertForMaskedLM:()=>bt,MobileBertForQuestionAnswering:()=>yt,MobileBertForSequenceClassification:()=>kt,MobileBertModel:()=>xt,MobileBertPreTrainedModel:()=>Mt,MobileLLMForCausalLM:()=>oo,MobileLLMModel:()=>ro,MobileLLMPreTrainedModel:()=>so,MobileNetV1ForImageClassification:()=>$l,MobileNetV1ForSemanticSegmentation:()=>ql,MobileNetV1Model:()=>Rl,MobileNetV1PreTrainedModel:()=>Gl,MobileNetV2ForImageClassification:()=>Ql,MobileNetV2ForSemanticSegmentation:()=>Xl,MobileNetV2Model:()=>Ul,MobileNetV2PreTrainedModel:()=>Wl,MobileNetV3ForImageClassification:()=>Yl,MobileNetV3ForSemanticSegmentation:()=>Kl,MobileNetV3Model:()=>Jl,MobileNetV3PreTrainedModel:()=>Hl,MobileNetV4ForImageClassification:()=>tc,MobileNetV4ForSemanticSegmentation:()=>sc,MobileNetV4Model:()=>ec,MobileNetV4PreTrainedModel:()=>Zl,MobileViTForImageClassification:()=>Tn,MobileViTModel:()=>vn,MobileViTPreTrainedModel:()=>yn,MobileViTV2ForImageClassification:()=>Cn,MobileViTV2Model:()=>Fn,MobileViTV2PreTrainedModel:()=>Pn,ModelOutput:()=>ne,ModernBertForMaskedLM:()=>be,ModernBertForSequenceClassification:()=>ke,ModernBertForTokenClassification:()=>ye,ModernBertModel:()=>xe,ModernBertPreTrainedModel:()=>Me,Moondream1ForConditionalGeneration:()=>Rs,MoonshineForConditionalGeneration:()=>Os,MoonshineModel:()=>Ds,MoonshinePreTrainedModel:()=>js,MptForCausalLM:()=>Ho,MptModel:()=>Xo,MptPreTrainedModel:()=>Qo,MultiModalityCausalLM:()=>ic,MultiModalityPreTrainedModel:()=>nc,MusicgenForCausalLM:()=>Vl,MusicgenForConditionalGeneration:()=>Bl,MusicgenModel:()=>Nl,MusicgenPreTrainedModel:()=>Ol,NeoBertForMaskedLM:()=>he,NeoBertForQuestionAnswering:()=>we,NeoBertForSequenceClassification:()=>ge,NeoBertForTokenClassification:()=>fe,NeoBertModel:()=>me,NeoBertPreTrainedModel:()=>pe,NomicBertModel:()=>Te,NomicBertPreTrainedModel:()=>ve,OPTForCausalLM:()=>Ko,OPTModel:()=>Yo,OPTPreTrainedModel:()=>Jo,Olmo2ForCausalLM:()=>uo,Olmo2Model:()=>co,Olmo2PreTrainedModel:()=>lo,OlmoForCausalLM:()=>ao,OlmoModel:()=>io,OlmoPreTrainedModel:()=>no,OpenELMForCausalLM:()=>So,OpenELMModel:()=>Co,OpenELMPreTrainedModel:()=>Fo,OwlViTForObjectDetection:()=>An,OwlViTModel:()=>En,OwlViTPreTrainedModel:()=>Sn,Owlv2ForObjectDetection:()=>zn,Owlv2Model:()=>In,Owlv2PreTrainedModel:()=>Ln,PaliGemmaForConditionalGeneration:()=>Us,PaliGemmaPreTrainedModel:()=>Ws,PatchTSMixerForPrediction:()=>hc,PatchTSMixerModel:()=>mc,PatchTSMixerPreTrainedModel:()=>pc,PatchTSTForPrediction:()=>_c,PatchTSTModel:()=>uc,PatchTSTPreTrainedModel:()=>dc,Phi3ForCausalLM:()=>$o,Phi3Model:()=>Ro,Phi3PreTrainedModel:()=>Go,Phi3VForCausalLM:()=>er,Phi3VPreTrainedModel:()=>Zs,PhiForCausalLM:()=>Bo,PhiModel:()=>Vo,PhiPreTrainedModel:()=>No,PreTrainedModel:()=>oe,PretrainedMixin:()=>zc,PvtForImageClassification:()=>dn,PvtModel:()=>cn,PvtPreTrainedModel:()=>ln,PyAnnoteForAudioFrameClassification:()=>Ca,PyAnnoteModel:()=>Fa,PyAnnotePreTrainedModel:()=>Pa,QuestionAnsweringModelOutput:()=>su,Qwen2ForCausalLM:()=>Lo,Qwen2Model:()=>Ao,Qwen2PreTrainedModel:()=>Eo,Qwen2VLForConditionalGeneration:()=>Oo,Qwen2VLPreTrainedModel:()=>Do,Qwen3ForCausalLM:()=>jo,Qwen3Model:()=>zo,Qwen3PreTrainedModel:()=>Io,RFDetrForObjectDetection:()=>ei,RFDetrModel:()=>Zn,RFDetrObjectDetectionOutput:()=>ti,RFDetrPreTrainedModel:()=>Kn,RTDetrForObjectDetection:()=>Un,RTDetrModel:()=>Wn,RTDetrObjectDetectionOutput:()=>Qn,RTDetrPreTrainedModel:()=>qn,RTDetrV2ForObjectDetection:()=>Jn,RTDetrV2Model:()=>Hn,RTDetrV2ObjectDetectionOutput:()=>Yn,RTDetrV2PreTrainedModel:()=>Xn,ResNetForImageClassification:()=>fi,ResNetModel:()=>gi,ResNetPreTrainedModel:()=>hi,RoFormerForMaskedLM:()=>Ce,RoFormerForQuestionAnswering:()=>Ae,RoFormerForSequenceClassification:()=>Se,RoFormerForTokenClassification:()=>Ee,RoFormerModel:()=>Fe,RoFormerPreTrainedModel:()=>Pe,RobertaForMaskedLM:()=>_s,RobertaForQuestionAnswering:()=>hs,RobertaForSequenceClassification:()=>ps,RobertaForTokenClassification:()=>ms,RobertaModel:()=>us,RobertaPreTrainedModel:()=>ds,SamImageSegmentationOutput:()=>ma,SamModel:()=>pa,SamPreTrainedModel:()=>_a,SapiensForDepthEstimation:()=>Li,SapiensForNormalEstimation:()=>Ii,SapiensForSemanticSegmentation:()=>Ai,SapiensPreTrainedModel:()=>Ei,SegformerForImageClassification:()=>Sl,SegformerForSemanticSegmentation:()=>El,SegformerModel:()=>Cl,SegformerPreTrainedModel:()=>Fl,Seq2SeqLMOutput:()=>Yd,SequenceClassifierOutput:()=>Kd,SiglipModel:()=>lr,SiglipPreTrainedModel:()=>ar,SiglipTextModel:()=>cr,SiglipVisionModel:()=>dr,SmolLM3ForCausalLM:()=>Ur,SmolLM3Model:()=>Wr,SmolLM3PreTrainedModel:()=>qr,SmolVLMForConditionalGeneration:()=>Ks,SnacDecoderModel:()=>Ic,SnacEncoderModel:()=>Lc,SnacModel:()=>Ac,SnacPreTrainedModel:()=>Ec,SpeechT5ForSpeechToText:()=>ol,SpeechT5ForTextToSpeech:()=>nl,SpeechT5HifiGan:()=>il,SpeechT5Model:()=>rl,SpeechT5PreTrainedModel:()=>sl,SqueezeBertForMaskedLM:()=>Lt,SqueezeBertForQuestionAnswering:()=>zt,SqueezeBertForSequenceClassification:()=>It,SqueezeBertModel:()=>At,SqueezeBertPreTrainedModel:()=>Et,StableLmForCausalLM:()=>Il,StableLmModel:()=>Ll,StableLmPreTrainedModel:()=>Al,Starcoder2ForCausalLM:()=>fl,Starcoder2Model:()=>gl,Starcoder2PreTrainedModel:()=>hl,StyleTextToSpeech2Model:()=>tl,StyleTextToSpeech2PreTrainedModel:()=>el,Swin2SRForImageSuperResolution:()=>vi,Swin2SRModel:()=>yi,Swin2SRPreTrainedModel:()=>ki,SwinForImageClassification:()=>xi,SwinForSemanticSegmentation:()=>bi,SwinModel:()=>Mi,SwinPreTrainedModel:()=>wi,T5ForConditionalGeneration:()=>Rt,T5Model:()=>Gt,T5PreTrainedModel:()=>Bt,TableTransformerForObjectDetection:()=>ai,TableTransformerModel:()=>ii,TableTransformerObjectDetectionOutput:()=>li,TableTransformerPreTrainedModel:()=>ni,TokenClassifierOutput:()=>eu,TrOCRForCausalLM:()=>ll,TrOCRPreTrainedModel:()=>al,UltravoxModel:()=>fc,UltravoxPreTrainedModel:()=>gc,UniSpeechForCTC:()=>Ia,UniSpeechForSequenceClassification:()=>za,UniSpeechModel:()=>La,UniSpeechPreTrainedModel:()=>Aa,UniSpeechSatForAudioFrameClassification:()=>Va,UniSpeechSatForCTC:()=>Oa,UniSpeechSatForSequenceClassification:()=>Na,UniSpeechSatModel:()=>Da,UniSpeechSatPreTrainedModel:()=>ja,ViTForImageClassification:()=>tn,ViTMAEModel:()=>_n,ViTMAEPreTrainedModel:()=>un,ViTMSNForImageClassification:()=>hn,ViTMSNModel:()=>mn,ViTMSNPreTrainedModel:()=>pn,ViTModel:()=>en,ViTPreTrainedModel:()=>Zo,VisionEncoderDecoderModel:()=>Ns,VitMatteForImageMatting:()=>kn,VitMattePreTrainedModel:()=>bn,VitPoseForPoseEstimation:()=>an,VitPosePreTrainedModel:()=>nn,VitsModel:()=>Pl,VitsModelOutput:()=>iu,VitsPreTrainedModel:()=>Tl,Wav2Vec2BertForCTC:()=>Ra,Wav2Vec2BertForSequenceClassification:()=>$a,Wav2Vec2BertModel:()=>Ga,Wav2Vec2BertPreTrainedModel:()=>Ba,Wav2Vec2ForAudioFrameClassification:()=>Ta,Wav2Vec2ForCTC:()=>ya,Wav2Vec2ForSequenceClassification:()=>va,Wav2Vec2Model:()=>ka,Wav2Vec2PreTrainedModel:()=>ba,WavLMForAudioFrameClassification:()=>Za,WavLMForCTC:()=>Ja,WavLMForSequenceClassification:()=>Ya,WavLMForXVector:()=>Ka,WavLMModel:()=>Ha,WavLMPreTrainedModel:()=>Xa,WeSpeakerResNetModel:()=>Ea,WeSpeakerResNetPreTrainedModel:()=>Sa,WhisperForConditionalGeneration:()=>Is,WhisperModel:()=>Ls,WhisperPreTrainedModel:()=>As,XLMForQuestionAnswering:()=>bs,XLMForSequenceClassification:()=>Ms,XLMForTokenClassification:()=>xs,XLMModel:()=>fs,XLMPreTrainedModel:()=>gs,XLMRobertaForMaskedLM:()=>vs,XLMRobertaForQuestionAnswering:()=>Fs,XLMRobertaForSequenceClassification:()=>Ts,XLMRobertaForTokenClassification:()=>Ps,XLMRobertaModel:()=>ys,XLMRobertaPreTrainedModel:()=>ks,XLMWithLMHeadModel:()=>ws,XVectorOutput:()=>Zd,YolosForObjectDetection:()=>da,YolosModel:()=>ca,YolosObjectDetectionOutput:()=>ua,YolosPreTrainedModel:()=>la});var r=s("./src/configs.js"),o=s("./src/backends/onnx.js"),n=s("./src/utils/dtypes.js"),i=s("./src/utils/generic.js"),a=s("./src/utils/core.js"),l=s("./src/utils/hub.js"),c=s("./src/utils/constants.js"),d=s("./src/generation/logits_process.js"),u=s("./src/generation/configuration_utils.js"),_=s("./src/utils/tensor.js"),p=s("./src/utils/image.js"),m=s("./src/utils/maths.js"),h=s("./src/generation/stopping_criteria.js"),g=s("./src/generation/logits_sampler.js"),f=s("./src/env.js"),w=s("./src/models/whisper/generation_whisper.js"),M=s("./src/models/whisper/common_whisper.js");const x=0,b=1,k=2,y=3,v=4,T=5,P=6,F=7,C=8,S=9,E=10,A=11,L=12,I=new Map,z=new Map,j=new Map;async function D(e,t,s){return Object.fromEntries(await Promise.all(Object.keys(t).map((async i=>{const{buffer_or_path:a,session_options:c,session_config:d}=await async function(e,t,s){let i=s.config?.["transformers.js_config"]??{},a=s.device??i.device;a&&"string"!=typeof a&&(a.hasOwnProperty(t)?a=a[t]:(console.warn(`device not specified for "${t}". Using the default device.`),a=null));const c=a??(f.apis.IS_NODE_ENV?"cpu":"wasm"),d=(0,o.deviceToExecutionProviders)(c),u=i.device_config??{};u.hasOwnProperty(c)&&(i={...i,...u[c]});let _=s.dtype??i.dtype;if("string"!=typeof _&&(_&&_.hasOwnProperty(t)?_=_[t]:(_=n.DEFAULT_DEVICE_DTYPE_MAPPING[c]??n.DATA_TYPES.fp32,console.warn(`dtype not specified for "${t}". Using the default dtype (${_}) for this device (${c}).`))),_===n.DATA_TYPES.auto){let e=i.dtype;"string"!=typeof e&&(e=e?.[t]),_=e&&e!==n.DATA_TYPES.auto&&n.DATA_TYPES.hasOwnProperty(e)?e:n.DEFAULT_DEVICE_DTYPE_MAPPING[c]??n.DATA_TYPES.fp32}const p=_;if(!n.DEFAULT_DTYPE_SUFFIX_MAPPING.hasOwnProperty(p))throw new Error(`Invalid dtype: ${p}. Should be one of: ${Object.keys(n.DATA_TYPES).join(", ")}`);if(p===n.DATA_TYPES.fp16&&"webgpu"===c&&!await(0,n.isWebGpuFp16Supported)())throw new Error(`The device (${c}) does not support fp16.`);const m=i.kv_cache_dtype,h=m?"string"==typeof m?m:m[p]??"float32":void 0;if(h&&!["float32","float16"].includes(h))throw new Error(`Invalid kv_cache_dtype: ${h}. Should be one of: float32, float16`);const g={dtype:p,kv_cache_dtype:h,device:c},w=`${t}${n.DEFAULT_DTYPE_SUFFIX_MAPPING[p]}.onnx`,M=`${s.subfolder??""}/${w}`,x={...s.session_options};x.executionProviders??=d;const b=i.free_dimension_overrides;b?x.freeDimensionOverrides??=b:c.startsWith("webnn")&&!x.freeDimensionOverrides&&console.warn(`WebNN does not currently support dynamic shapes and requires 'free_dimension_overrides' to be set in config.json, preferably as a field within config["transformers.js_config"]["device_config"]["${c}"]. When 'free_dimension_overrides' is not set, you may experience significant performance degradation.`);const k=f.apis.IS_NODE_ENV&&f.env.useFSCache,y=(0,l.getModelFile)(e,M,!0,s,k),v=s.use_external_data_format??i.use_external_data_format;let T=[];if(v){let r;r="object"==typeof v?v.hasOwnProperty(w)?v[w]:!!v.hasOwnProperty(t)&&v[t]:v;const o=+r;if(o>l.MAX_EXTERNAL_DATA_CHUNKS)throw new Error(`The number of external data chunks (${o}) exceeds the maximum allowed value (${l.MAX_EXTERNAL_DATA_CHUNKS}).`);for(let t=0;t<o;++t){const r=`${w}_data${0===t?"":"_"+t}`,o=`${s.subfolder??""}/${r}`;T.push(new Promise((async(t,n)=>{const i=await(0,l.getModelFile)(e,o,!0,s,k);t(i instanceof Uint8Array?{path:r,data:i}:r)})))}}else void 0!==x.externalData&&(T=x.externalData.map((async t=>{if("string"==typeof t.data){const r=await(0,l.getModelFile)(e,t.data,!0,s);return{...t,data:r}}return t})));if(T.length>0){const e=await Promise.all(T);f.apis.IS_NODE_ENV||(x.externalData=e)}if("webgpu"===c){const e=(0,r.getKeyValueShapes)(s.config,{prefix:"present"});if(Object.keys(e).length>0&&!(0,o.isONNXProxy)()){const t={};for(const s in e)t[s]="gpu-buffer";x.preferredOutputLocation=t}}return{buffer_or_path:await y,session_options:x,session_config:g}}(e,t[i],s);return[i,await(0,o.createInferenceSession)(a,c,d)]}))))}async function O(e,t,s){return Object.fromEntries(await Promise.all(Object.keys(t).map((async r=>[r,await(0,l.getModelJSON)(e,t[r],!1,s)]))))}let N=Promise.resolve();async function V(e,t){const s=function(e,t){const s=Object.create(null),r=[];for(const n of e.inputNames){const e=t[n];e instanceof _.Tensor?s[n]=(0,o.isONNXProxy)()?e.clone():e:r.push(n)}if(r.length>0)throw new Error(`An error occurred during model execution: "Missing the following inputs: ${r.join(", ")}.`);const n=Object.keys(t).length,i=e.inputNames.length;if(n>i){let s=Object.keys(t).filter((t=>!e.inputNames.includes(t)));console.warn(`WARNING: Too many inputs were provided (${n} > ${i}). The following inputs will be ignored: "${s.join(", ")}".`)}return s}(e,t);try{const t=Object.fromEntries(Object.entries(s).map((([e,t])=>[e,t.ort_tensor]))),r=()=>e.run(t);return B(await(f.apis.IS_BROWSER_ENV||f.apis.IS_WEBWORKER_ENV?N=N.then(r):r()))}catch(e){const t=Object.fromEntries(Object.entries(s).map((([e,t])=>{const s={type:t.type,dims:t.dims,location:t.location};return"gpu-buffer"!==s.location&&(s.data=t.data),[e,s]})));throw console.error(`An error occurred during model execution: "${e}".`),console.error("Inputs given to model:",t),e}}function B(e){for(let t in e)(0,o.isONNXTensor)(e[t])?e[t]=new _.Tensor(e[t]):"object"==typeof e[t]&&B(e[t]);return e}function G(e){if(e instanceof _.Tensor)return e;if(0===e.length)throw Error("items must be non-empty");if(Array.isArray(e[0])){if(e.some((t=>t.length!==e[0].length)))throw Error("Unable to create tensor, you should probably activate truncation and/or padding with 'padding=True' and/or 'truncation=True' to have batched tensors with the same length.");return new _.Tensor("int64",BigInt64Array.from(e.flat().map((e=>BigInt(e)))),[e.length,e[0].length])}return new _.Tensor("int64",BigInt64Array.from(e.map((e=>BigInt(e)))),[1,e.length])}function R(e){return new _.Tensor("bool",[e],[1])}async function $(e,t){let{encoder_outputs:s,input_ids:r,decoder_input_ids:o,...n}=t;if(!s){const r=(0,a.pick)(t,e.sessions.model.inputNames);s=(await q(e,r)).last_hidden_state}n.input_ids=o,n.encoder_hidden_states=s,e.sessions.decoder_model_merged.inputNames.includes("encoder_attention_mask")&&(n.encoder_attention_mask=t.attention_mask);return await U(e,n,!0)}async function q(e,t){const s=e.sessions.model,r=(0,a.pick)(t,s.inputNames);if(s.inputNames.includes("inputs_embeds")&&!r.inputs_embeds){if(!t.input_ids)throw new Error("Both `input_ids` and `inputs_embeds` are missing in the model inputs.");r.inputs_embeds=await e.encode_text({input_ids:t.input_ids})}if(s.inputNames.includes("token_type_ids")&&!r.token_type_ids){if(!r.input_ids)throw new Error("Both `input_ids` and `token_type_ids` are missing in the model inputs.");r.token_type_ids=(0,_.zeros_like)(r.input_ids)}if(s.inputNames.includes("pixel_mask")&&!r.pixel_mask){if(!r.pixel_values)throw new Error("Both `pixel_values` and `pixel_mask` are missing in the model inputs.");const e=r.pixel_values.dims;r.pixel_mask=(0,_.ones)([e[0],e[2],e[3]])}return await V(s,r)}async function W(e,t){const s=await e.encode(t);return await e.decode(s)}async function U(e,t,s=!1){const r=e.sessions[s?"decoder_model_merged":"model"],{past_key_values:o,...n}=t;if(r.inputNames.includes("use_cache_branch")&&(n.use_cache_branch=R(!!o)),r.inputNames.includes("position_ids")&&n.attention_mask&&!n.position_ids){const t=["paligemma","gemma3_text","gemma3"].includes(e.config.model_type)?1:0;n.position_ids=function(e,t=null,s=0){const{input_ids:r,inputs_embeds:o,attention_mask:n}=e,{data:i,dims:a}=Z(n,s);let l=new _.Tensor("int64",i,a);if(t){const e=-(r??o).dims.at(1);l=l.slice(null,[e,null])}return l}(n,o,t)}e.addPastKeyValues(n,o);const i=(0,a.pick)(n,r.inputNames);return await V(r,i)}function Q({modality_token_id:e,inputs_embeds:t,modality_features:s,input_ids:r,attention_mask:o}){const n=r.tolist().map((t=>t.reduce(((t,s,r)=>(s==e&&t.push(r),t)),[]))),i=n.reduce(((e,t)=>e+t.length),0),a=s.dims[0];if(i!==a)throw new Error(`Number of tokens and features do not match: tokens: ${i}, features ${a}`);let l=0;for(let e=0;e<n.length;++e){const r=n[e],o=t[e];for(let e=0;e<r.length;++e)o[r[e]].data.set(s[l++].data)}return{inputs_embeds:t,attention_mask:o}}function X({image_token_id:e,inputs_embeds:t,image_features:s,input_ids:r,attention_mask:o}){return Q({modality_token_id:e,inputs_embeds:t,modality_features:s,input_ids:r,attention_mask:o})}function H({audio_token_id:e,inputs_embeds:t,audio_features:s,input_ids:r,attention_mask:o}){return Q({modality_token_id:e,inputs_embeds:t,modality_features:s,input_ids:r,attention_mask:o})}async function J(e,{encode_function:t,merge_function:s,modality_input_name:r,modality_output_name:o,input_ids:n=null,attention_mask:i=null,position_ids:a=null,inputs_embeds:l=null,past_key_values:c=null,generation_config:d=null,logits_processor:u=null,...p}){const m=p[r];if(!l)if(l=await e.encode_text({input_ids:n,...p}),m&&1!==n.dims[1]){const e=await t({[r]:m,...p});({inputs_embeds:l,attention_mask:i}=s({[o]:e,inputs_embeds:l,input_ids:n,attention_mask:i}))}else if(c&&m&&1===n.dims[1]){const e=n.dims[1],t=Object.values(c)[0].dims.at(-2);i=(0,_.cat)([(0,_.ones)([n.dims[0],t]),i.slice(null,[i.dims[1]-e,i.dims[1]])],1)}if(!a&&"qwen2_vl"===e.config.model_type){const{image_grid_thw:t,video_grid_thw:s}=p;[a]=e.get_rope_index(n,t,s,i)}return await U(e,{inputs_embeds:l,past_key_values:c,attention_mask:i,position_ids:a,generation_config:d,logits_processor:u},!0)}async function Y(e,t){return await J(e,{...t,modality_input_name:"audio_values",modality_output_name:"audio_features",encode_function:e.encode_audio.bind(e),merge_function:e._merge_input_ids_with_audio_features.bind(e)})}async function K(e,t){return await J(e,{...t,modality_input_name:"pixel_values",modality_output_name:"image_features",encode_function:e.encode_image.bind(e),merge_function:e._merge_input_ids_with_image_features.bind(e)})}function Z(e,t=0){const[s,r]=e.dims,o=e.data,n=new BigInt64Array(o.length);for(let e=0;e<s;++e){const s=e*r;let i=BigInt(t);for(let e=0;e<r;++e){const t=s+e;0n===o[t]?n[t]=BigInt(1):(n[t]=i,i+=o[t])}}return{data:n,dims:e.dims}}function ee(e,t,s,r){const o=s.past_key_values?Object.values(s.past_key_values)[0].dims.at(-2):0;if(!s.attention_mask){let e;for(const t of["input_ids","inputs_embeds","position_ids"])if(s[t]){e=s[t].dims;break}if(!e)throw new Error("attention_mask is not provided, and unable to infer its shape from model inputs.");s.attention_mask=(0,_.ones)([e[0],o+e[1]])}if(s.past_key_values){const{input_ids:e,attention_mask:t}=s;t&&t.dims[1]>e.dims[1]||o<e.dims[1]&&(s.input_ids=e.slice(null,[o,null]))}return s}function te(e,t,s,r){return s.past_key_values&&(t=t.map((e=>[e.at(-1)]))),{...s,decoder_input_ids:G(t)}}function se(e,...t){return e.config.is_encoder_decoder?te(e,...t):ee(e,...t)}function re(e,t,s,r){const o=!!s.past_key_values;if(null!==r.guidance_scale&&r.guidance_scale>1&&(o?s.input_ids=(0,_.cat)([s.input_ids,s.input_ids],0):(s.input_ids=(0,_.cat)([s.input_ids,(0,_.full_like)(s.input_ids,BigInt(r.pad_token_id))],0),s.attention_mask=(0,_.cat)([s.attention_mask,(0,_.full_like)(s.attention_mask,0n)],0))),!o&&s.pixel_values||(s.pixel_values=(0,_.full)([0,0,3,384,384],1)),o){const e=0,t=1,r=e>0?1:0,o=1;s.images_seq_mask=new _.Tensor("bool",new Array(e+t).fill(!0).fill(!1,0,t),[o,e+t]),s.images_emb_mask=new _.Tensor("bool",new Array(e).fill(!!r),[o,1,e])}return s}class oe extends i.Callable{main_input_name="input_ids";forward_params=["input_ids","attention_mask"];constructor(e,t,s){super(),this.config=e,this.sessions=t,this.configs=s;const r=j.get(this.constructor),o=I.get(r);switch(this.can_generate=!1,this._forward=null,this._prepare_inputs_for_generation=null,o){case v:this.can_generate=!0,this._forward=U,this._prepare_inputs_for_generation=ee;break;case k:case y:case F:this.can_generate=!0,this._forward=$,this._prepare_inputs_for_generation=te;break;case b:this._forward=$;break;case P:this.can_generate=!0,this._forward=K,this._prepare_inputs_for_generation=se;break;case E:this.can_generate=!0,this._forward=Y,this._prepare_inputs_for_generation=se;break;case S:case L:this.can_generate=!0,this._prepare_inputs_for_generation=se;break;case C:this.can_generate=!0,this._prepare_inputs_for_generation=re;break;case A:this._forward=W;break;default:this._forward=q}this.can_generate&&this.forward_params.push("past_key_values"),this.custom_config=this.config["transformers.js_config"]??{}}async dispose(){const e=[];for(const t of Object.values(this.sessions))t?.handler?.dispose&&e.push(t.handler.dispose());return await Promise.all(e)}static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:o=null,local_files_only:n=!1,revision:i="main",model_file_name:a=null,subfolder:l="onnx",device:d=null,dtype:u=null,use_external_data_format:_=null,session_options:p={}}={}){let m={progress_callback:t,config:s,cache_dir:o,local_files_only:n,revision:i,model_file_name:a,subfolder:l,device:d,dtype:u,use_external_data_format:_,session_options:p};const h=j.get(this),g=I.get(h);let f;if(s=m.config=await r.AutoConfig.from_pretrained(e,m),g===v)f=await Promise.all([D(e,{model:m.model_file_name??"model"},m),O(e,{generation_config:"generation_config.json"},m)]);else if(g===k||g===y)f=await Promise.all([D(e,{model:"encoder_model",decoder_model_merged:"decoder_model_merged"},m),O(e,{generation_config:"generation_config.json"},m)]);else if(g===T)f=await Promise.all([D(e,{model:"vision_encoder",prompt_encoder_mask_decoder:"prompt_encoder_mask_decoder"},m)]);else if(g===b)f=await Promise.all([D(e,{model:"encoder_model",decoder_model_merged:"decoder_model_merged"},m)]);else if(g===P){const t={embed_tokens:"embed_tokens",vision_encoder:"vision_encoder",decoder_model_merged:"decoder_model_merged"};s.is_encoder_decoder&&(t.model="encoder_model"),f=await Promise.all([D(e,t,m),O(e,{generation_config:"generation_config.json"},m)])}else if(g===E){const t={embed_tokens:"embed_tokens",audio_encoder:"audio_encoder",decoder_model_merged:"decoder_model_merged"};f=await Promise.all([D(e,t,m),O(e,{generation_config:"generation_config.json"},m)])}else if(g===L){const t={embed_tokens:"embed_tokens",audio_encoder:"audio_encoder",vision_encoder:"vision_encoder",decoder_model_merged:"decoder_model_merged"};f=await Promise.all([D(e,t,m),O(e,{generation_config:"generation_config.json"},m)])}else if(g===F)f=await Promise.all([D(e,{model:"text_encoder",decoder_model_merged:"decoder_model_merged",encodec_decode:"encodec_decode"},m),O(e,{generation_config:"generation_config.json"},m)]);else if(g===C)f=await Promise.all([D(e,{prepare_inputs_embeds:"prepare_inputs_embeds",model:"language_model",lm_head:"lm_head",gen_head:"gen_head",gen_img_embeds:"gen_img_embeds",image_decode:"image_decode"},m),O(e,{generation_config:"generation_config.json"},m)]);else if(g===S)f=await Promise.all([D(e,{prepare_inputs_embeds:"prepare_inputs_embeds",model:"model",vision_encoder:"vision_encoder"},m),O(e,{generation_config:"generation_config.json"},m)]);else if(g===A)f=await Promise.all([D(e,{encoder_model:"encoder_model",decoder_model:"decoder_model"},m)]);else{if(g!==x){const e=h??s?.model_type;"custom"!==e&&console.warn(`Model type for '${e}' not found, assuming encoder-only architecture. Please report this at ${c.GITHUB_ISSUE_URL}.`)}f=await Promise.all([D(e,{model:m.model_file_name??"model"},m)])}return new this(s,...f)}async _call(e){return await this.forward(e)}async forward(e){return await this._forward(this,e)}get generation_config(){return this.configs?.generation_config??null}_get_logits_warper(e){const t=new d.LogitsProcessorList;return null!==e.temperature&&1!==e.temperature&&t.push(new d.TemperatureLogitsWarper(e.temperature)),null!==e.top_k&&0!==e.top_k&&t.push(new d.TopKLogitsWarper(e.top_k)),null!==e.top_p&&e.top_p<1&&t.push(new d.TopPLogitsWarper(e.top_p)),t}_get_logits_processor(e,t,s=null){const r=new d.LogitsProcessorList;if(null!==e.repetition_penalty&&1!==e.repetition_penalty&&r.push(new d.RepetitionPenaltyLogitsProcessor(e.repetition_penalty)),null!==e.no_repeat_ngram_size&&e.no_repeat_ngram_size>0&&r.push(new d.NoRepeatNGramLogitsProcessor(e.no_repeat_ngram_size)),null!==e.bad_words_ids&&r.push(new d.NoBadWordsLogitsProcessor(e.bad_words_ids,e.eos_token_id)),null!==e.min_length&&null!==e.eos_token_id&&e.min_length>0&&r.push(new d.MinLengthLogitsProcessor(e.min_length,e.eos_token_id)),null!==e.min_new_tokens&&null!==e.eos_token_id&&e.min_new_tokens>0&&r.push(new d.MinNewTokensLengthLogitsProcessor(t,e.min_new_tokens,e.eos_token_id)),null!==e.forced_bos_token_id&&r.push(new d.ForcedBOSTokenLogitsProcessor(e.forced_bos_token_id)),null!==e.forced_eos_token_id&&r.push(new d.ForcedEOSTokenLogitsProcessor(e.max_length,e.forced_eos_token_id)),null!==e.begin_suppress_tokens){const s=t>1||null===e.forced_bos_token_id?t:t+1;r.push(new d.SuppressTokensAtBeginLogitsProcessor(e.begin_suppress_tokens,s))}return null!==e.guidance_scale&&e.guidance_scale>1&&r.push(new d.ClassifierFreeGuidanceLogitsProcessor(e.guidance_scale)),null!==s&&r.extend(s),r}_prepare_generation_config(e,t,s=u.GenerationConfig){const r={...this.config};for(const e of["decoder","generator","text_config"])e in r&&Object.assign(r,r[e]);const o=new s(r);return Object.assign(o,this.generation_config??{}),e&&Object.assign(o,e),t&&Object.assign(o,(0,a.pick)(t,Object.getOwnPropertyNames(o))),o}_get_stopping_criteria(e,t=null){const s=new h.StoppingCriteriaList;return null!==e.max_length&&s.push(new h.MaxLengthCriteria(e.max_length,this.config.max_position_embeddings??null)),null!==e.eos_token_id&&s.push(new h.EosTokenCriteria(e.eos_token_id)),t&&s.extend(t),s}_validate_model_class(){if(!this.can_generate){const e=[Wc,Hc,qc,Vc],t=j.get(this.constructor),s=new Set,r=this.config.model_type;for(const t of e){const e=t.get(r);e&&s.add(e[0])}let o=`The current model class (${t}) is not compatible with \`.generate()\`, as it doesn't have a language model head.`;throw s.size>0&&(o+=` Please use the following class instead: ${[...s].join(", ")}`),Error(o)}}prepare_inputs_for_generation(...e){return this._prepare_inputs_for_generation(this,...e)}_update_model_kwargs_for_generation({generated_input_ids:e,outputs:t,model_inputs:s,is_encoder_decoder:r}){return s.past_key_values=this.getPastKeyValues(t,s.past_key_values),s.input_ids=new _.Tensor("int64",e.flat(),[e.length,1]),r||(s.attention_mask=(0,_.cat)([s.attention_mask,(0,_.ones)([s.attention_mask.dims[0],1])],1)),s.position_ids=null,s}_prepare_model_inputs({inputs:e,bos_token_id:t,model_kwargs:s}){const r=(0,a.pick)(s,this.forward_params),o=this.main_input_name;if(o in r){if(e)throw new Error("`inputs`: {inputs}` were passed alongside {input_name} which is not allowed. Make sure to either pass {inputs} or {input_name}=...")}else r[o]=e;return{inputs_tensor:r[o],model_inputs:r,model_input_name:o}}async _prepare_encoder_decoder_kwargs_for_generation({inputs_tensor:e,model_inputs:t,model_input_name:s,generation_config:r}){if(this.sessions.model.inputNames.includes("inputs_embeds")&&!t.inputs_embeds&&"_prepare_inputs_embeds"in this){const{input_ids:e,pixel_values:s,attention_mask:r,...o}=t,n=await this._prepare_inputs_embeds(t);t={...o,...(0,a.pick)(n,["inputs_embeds","attention_mask"])}}let{last_hidden_state:o}=await q(this,t);if(null!==r.guidance_scale&&r.guidance_scale>1)o=(0,_.cat)([o,(0,_.full_like)(o,0)],0),"attention_mask"in t&&(t.attention_mask=(0,_.cat)([t.attention_mask,(0,_.zeros_like)(t.attention_mask)],0));else if(t.decoder_input_ids){const e=G(t.decoder_input_ids).dims[0];if(e!==o.dims[0]){if(1!==o.dims[0])throw new Error(`The encoder outputs have a different batch size (${o.dims[0]}) than the decoder inputs (${e}).`);o=(0,_.cat)(Array.from({length:e},(()=>o)),0)}}return t.encoder_outputs=o,t}_prepare_decoder_input_ids_for_generation({batch_size:e,model_input_name:t,model_kwargs:s,decoder_start_token_id:r,bos_token_id:o,generation_config:n}){let{decoder_input_ids:i,...a}=s;if(!(i instanceof _.Tensor)){if(i)Array.isArray(i[0])||(i=Array.from({length:e},(()=>i)));else if(r??=o,"musicgen"===this.config.model_type)i=Array.from({length:e*this.config.decoder.num_codebooks},(()=>[r]));else if(Array.isArray(r)){if(r.length!==e)throw new Error(`\`decoder_start_token_id\` expcted to have length ${e} but got ${r.length}`);i=r}else i=Array.from({length:e},(()=>[r]));i=G(i)}return s.decoder_attention_mask=(0,_.ones_like)(i),{input_ids:i,model_inputs:a}}async generate({inputs:e=null,generation_config:t=null,logits_processor:s=null,stopping_criteria:r=null,streamer:o=null,...n}){this._validate_model_class(),t=this._prepare_generation_config(t,n);let{inputs_tensor:i,model_inputs:a,model_input_name:l}=this._prepare_model_inputs({inputs:e,model_kwargs:n});const c=this.config.is_encoder_decoder;let d;c&&("encoder_outputs"in a||(a=await this._prepare_encoder_decoder_kwargs_for_generation({inputs_tensor:i,model_inputs:a,model_input_name:l,generation_config:t}))),c?({input_ids:d,model_inputs:a}=this._prepare_decoder_input_ids_for_generation({batch_size:a[l].dims.at(0),model_input_name:l,model_kwargs:a,decoder_start_token_id:t.decoder_start_token_id,bos_token_id:t.bos_token_id,generation_config:t})):d=a[l];let u=d.dims.at(-1);null!==t.max_new_tokens&&(t.max_length=u+t.max_new_tokens);const p=this._get_logits_processor(t,u,s),m=this._get_stopping_criteria(t,r),h=a[l].dims.at(0),f=g.LogitsSampler.getSampler(t),w=new Array(h).fill(0),M=d.tolist();let x;o&&o.put(M);let b={};for(;;){if(a=this.prepare_inputs_for_generation(M,a,t),x=await this.forward(a),t.output_attentions&&t.return_dict_in_generate){const e=this.getAttentions(x);for(const t in e)t in b||(b[t]=[]),b[t].push(e[t])}const e=p(M,x.logits.slice(null,-1,null)),s=[];for(let t=0;t<e.dims.at(0);++t){const r=e[t],o=await f(r);for(const[e,r]of o){const o=BigInt(e);w[t]+=r,M[t].push(o),s.push([o]);break}}o&&o.put(s);if(m(M).every((e=>e)))break;a=this._update_model_kwargs_for_generation({generated_input_ids:s,outputs:x,model_inputs:a,is_encoder_decoder:c})}o&&o.end();const k=this.getPastKeyValues(x,a.past_key_values,!0),y=new _.Tensor("int64",M.flat(),[M.length,M[0].length]);if(t.return_dict_in_generate)return{sequences:y,past_key_values:k,...b};for(const e of Object.values(x))"gpu-buffer"===e.location&&e.dispose();return y}getPastKeyValues(e,t,s=!1){const r=Object.create(null);for(const o in e)if(o.startsWith("present")){const n=o.replace("present","past_key_values"),i=o.includes("encoder");if(r[n]=i&&t?t[n]:e[o],t&&(!i||s)){const e=t[n];"gpu-buffer"===e.location&&e.dispose()}}return r}getAttentions(e){const t={};for(const s of["cross_attentions","encoder_attentions","decoder_attentions"])for(const r in e)r.startsWith(s)&&(s in t||(t[s]=[]),t[s].push(e[r]));return t}addPastKeyValues(e,t){if(t)Object.assign(e,t);else{const t=this.sessions.decoder_model_merged??this.sessions.model,s=t?.config?.kv_cache_dtype??"float32",o="float16"===s?new _.DataTypeMap.float16:[],n=(e[this.main_input_name]??e.attention_mask)?.dims?.[0]??1,i=(0,r.getKeyValueShapes)(this.config,{batch_size:n});for(const t in i)e[t]=new _.Tensor(s,o,i[t])}}async encode_image({pixel_values:e}){return(await V(this.sessions.vision_encoder,{pixel_values:e})).image_features}async encode_text({input_ids:e}){return(await V(this.sessions.embed_tokens,{input_ids:e})).inputs_embeds}async encode_audio({audio_values:e}){return(await V(this.sessions.audio_encoder,{audio_values:e})).audio_features}}class ne{}class ie extends ne{constructor({last_hidden_state:e,hidden_states:t=null,attentions:s=null}){super(),this.last_hidden_state=e,this.hidden_states=t,this.attentions=s}}class ae extends oe{}class le extends ae{}class ce extends ae{async _call(e){return new tu(await super._call(e))}}class de extends ae{async _call(e){return new Kd(await super._call(e))}}class ue extends ae{async _call(e){return new eu(await super._call(e))}}class _e extends ae{async _call(e){return new su(await super._call(e))}}class pe extends oe{}class me extends pe{}class he extends pe{async _call(e){return new tu(await super._call(e))}}class ge extends pe{async _call(e){return new Kd(await super._call(e))}}class fe extends pe{async _call(e){return new eu(await super._call(e))}}class we extends pe{async _call(e){return new su(await super._call(e))}}class Me extends oe{}class xe extends Me{}class be extends Me{async _call(e){return new tu(await super._call(e))}}class ke extends Me{async _call(e){return new Kd(await super._call(e))}}class ye extends Me{async _call(e){return new eu(await super._call(e))}}class ve extends oe{}class Te extends ve{}class Pe extends oe{}class Fe extends Pe{}class Ce extends Pe{async _call(e){return new tu(await super._call(e))}}class Se extends Pe{async _call(e){return new Kd(await super._call(e))}}class Ee extends Pe{async _call(e){return new eu(await super._call(e))}}class Ae extends Pe{async _call(e){return new su(await super._call(e))}}class Le extends oe{}class Ie extends Le{}class ze extends Le{async _call(e){return new tu(await super._call(e))}}class je extends Le{async _call(e){return new Kd(await super._call(e))}}class De extends Le{async _call(e){return new eu(await super._call(e))}}class Oe extends Le{async _call(e){return new su(await super._call(e))}}class Ne extends oe{}class Ve extends Ne{}class Be extends Ne{async _call(e){return new tu(await super._call(e))}}class Ge extends Ne{async _call(e){return new Kd(await super._call(e))}}class Re extends Ne{async _call(e){return new eu(await super._call(e))}}class $e extends Ne{async _call(e){return new su(await super._call(e))}}class qe extends oe{}class We extends qe{}class Ue extends qe{async _call(e){return new tu(await super._call(e))}}class Qe extends qe{async _call(e){return new Kd(await super._call(e))}}class Xe extends qe{async _call(e){return new eu(await super._call(e))}}class He extends qe{async _call(e){return new su(await super._call(e))}}class Je extends oe{}class Ye extends Je{}class Ke extends Je{async _call(e){return new tu(await super._call(e))}}class Ze extends Je{async _call(e){return new Kd(await super._call(e))}}class et extends Je{async _call(e){return new eu(await super._call(e))}}class tt extends Je{async _call(e){return new su(await super._call(e))}}class st extends oe{}class rt extends st{}class ot extends st{async _call(e){return new tu(await super._call(e))}}class nt extends st{async _call(e){return new Kd(await super._call(e))}}class it extends st{async _call(e){return new eu(await super._call(e))}}class at extends st{async _call(e){return new su(await super._call(e))}}class lt extends oe{}class ct extends lt{}class dt extends lt{async _call(e){return new Kd(await super._call(e))}}class ut extends lt{async _call(e){return new eu(await super._call(e))}}class _t extends lt{async _call(e){return new su(await super._call(e))}}class pt extends lt{async _call(e){return new tu(await super._call(e))}}class mt extends oe{}class ht extends mt{}class gt extends mt{async _call(e){return new tu(await super._call(e))}}class ft extends mt{async _call(e){return new Kd(await super._call(e))}}class wt extends mt{async _call(e){return new eu(await super._call(e))}}class Mt extends oe{}class xt extends Mt{}class bt extends Mt{async _call(e){return new tu(await super._call(e))}}class kt extends Mt{async _call(e){return new Kd(await super._call(e))}}class yt extends Mt{async _call(e){return new su(await super._call(e))}}class vt extends oe{}class Tt extends vt{}class Pt extends vt{async _call(e){return new tu(await super._call(e))}}class Ft extends vt{async _call(e){return new Kd(await super._call(e))}}class Ct extends vt{async _call(e){return new eu(await super._call(e))}}class St extends vt{async _call(e){return new su(await super._call(e))}}class Et extends oe{}class At extends Et{}class Lt extends Et{async _call(e){return new tu(await super._call(e))}}class It extends Et{async _call(e){return new Kd(await super._call(e))}}class zt extends Et{async _call(e){return new su(await super._call(e))}}class jt extends oe{}class Dt extends jt{}class Ot extends jt{async _call(e){return new Kd(await super._call(e))}}class Nt extends jt{async _call(e){return new su(await super._call(e))}}class Vt extends jt{async _call(e){return new tu(await super._call(e))}}class Bt extends oe{forward_params=["input_ids","attention_mask","encoder_outputs","decoder_input_ids","decoder_attention_mask","past_key_values"]}class Gt extends Bt{}class Rt extends Bt{}class $t extends oe{}class qt extends $t{}class Wt extends $t{}class Ut extends oe{}class Qt extends Ut{}class Xt extends Ut{}class Ht extends oe{}class Jt extends Ht{}class Yt extends Ht{}class Kt extends Ht{async _call(e){return new Kd(await super._call(e))}}class Zt extends oe{}class es extends Zt{}class ts extends Zt{}class ss extends Zt{async _call(e){return new Kd(await super._call(e))}}class rs extends Zt{}class os extends oe{}class ns extends os{}class is extends os{}class as extends oe{}class ls extends as{}class cs extends as{}class ds extends oe{}class us extends ds{}class _s extends ds{async _call(e){return new tu(await super._call(e))}}class ps extends ds{async _call(e){return new Kd(await super._call(e))}}class ms extends ds{async _call(e){return new eu(await super._call(e))}}class hs extends ds{async _call(e){return new su(await super._call(e))}}class gs extends oe{}class fs extends gs{}class ws extends gs{async _call(e){return new tu(await super._call(e))}}class Ms extends gs{async _call(e){return new Kd(await super._call(e))}}class xs extends gs{async _call(e){return new eu(await super._call(e))}}class bs extends gs{async _call(e){return new su(await super._call(e))}}class ks extends oe{}class ys extends ks{}class vs extends ks{async _call(e){return new tu(await super._call(e))}}class Ts extends ks{async _call(e){return new Kd(await super._call(e))}}class Ps extends ks{async _call(e){return new eu(await super._call(e))}}class Fs extends ks{async _call(e){return new su(await super._call(e))}}class Cs extends oe{}class Ss extends Cs{}class Es extends Cs{}class As extends oe{requires_attention_mask=!1;main_input_name="input_features";forward_params=["input_features","attention_mask","decoder_input_ids","decoder_attention_mask","past_key_values"]}class Ls extends As{}class Is extends As{_prepare_generation_config(e,t){return super._prepare_generation_config(e,t,w.WhisperGenerationConfig)}_retrieve_init_tokens(e){const t=[e.decoder_start_token_id];let s=e.language;const r=e.task;if(e.is_multilingual){s||(console.warn("No language specified - defaulting to English (en)."),s="en");const o=`<|${(0,M.whisper_language_to_code)(s)}|>`;t.push(e.lang_to_id[o]),t.push(e.task_to_id[r??"transcribe"])}else if(s||r)throw new Error("Cannot specify `task` or `language` for an English-only model. If the model is intended to be multilingual, pass `is_multilingual=true` to generate, or update the generation config.");return!e.return_timestamps&&e.no_timestamps_token_id&&t.at(-1)!==e.no_timestamps_token_id?t.push(e.no_timestamps_token_id):e.return_timestamps&&t.at(-1)===e.no_timestamps_token_id&&(console.warn("<|notimestamps|> prompt token is removed from generation_config since `return_timestamps` is set to `true`."),t.pop()),t.filter((e=>null!=e))}async generate({inputs:e=null,generation_config:t=null,logits_processor:s=null,stopping_criteria:r=null,...o}){t=this._prepare_generation_config(t,o);const n=o.decoder_input_ids??this._retrieve_init_tokens(t);if(t.return_timestamps&&(s??=new d.LogitsProcessorList,s.push(new d.WhisperTimeStampLogitsProcessor(t,n))),t.begin_suppress_tokens&&(s??=new d.LogitsProcessorList,s.push(new d.SuppressTokensAtBeginLogitsProcessor(t.begin_suppress_tokens,n.length))),t.return_token_timestamps){if(!t.alignment_heads)throw new Error("Model generation config has no `alignment_heads`, token-level timestamps not available. See https://gist.github.com/hollance/42e32852f24243b748ae6bc1f985b13a on how to add this property to the generation config.");"translate"===t.task&&console.warn("Token-level timestamps may not be reliable for task 'translate'."),t.output_attentions=!0,t.return_dict_in_generate=!0}const i=await super.generate({inputs:e,generation_config:t,logits_processor:s,decoder_input_ids:n,...o});return t.return_token_timestamps&&(i.token_timestamps=this._extract_token_timestamps(i,t.alignment_heads,t.num_frames)),i}_extract_token_timestamps(e,t,s=null,r=.02){if(!e.cross_attentions)throw new Error("Model outputs must contain cross attentions to extract timestamps. This is most likely because the model was not exported with `output_attentions=True`.");null==s&&console.warn("`num_frames` has not been set, meaning the entire audio will be analyzed. This may lead to inaccurate token-level timestamps for short audios (< 30 seconds).");let o=this.config.median_filter_width;void 0===o&&(console.warn("Model config has no `median_filter_width`, using default value of 7."),o=7);const n=e.cross_attentions,i=Array.from({length:this.config.decoder_layers},((e,t)=>(0,_.cat)(n.map((e=>e[t])),2))),l=(0,_.stack)(t.map((([e,t])=>{if(e>=i.length)throw new Error(`Layer index ${e} is out of bounds for cross attentions (length ${i.length}).`);return s?i[e].slice(null,t,null,[0,s]):i[e].slice(null,t)}))).transpose(1,0,2,3),[c,d]=(0,_.std_mean)(l,-2,0,!0),u=l.clone();for(let e=0;e<u.dims[0];++e){const t=u[e];for(let s=0;s<t.dims[0];++s){const r=t[s],n=c[e][s][0].data,i=d[e][s][0].data;for(let e=0;e<r.dims[0];++e){let t=r[e].data;for(let e=0;e<t.length;++e)t[e]=(t[e]-i[e])/n[e];t.set((0,m.medianFilter)(t,o))}}}const p=[(0,_.mean)(u,1)],h=e.sequences.dims,g=new _.Tensor("float32",new Float32Array(h[0]*h[1]),h);for(let e=0;e<h[0];++e){const t=p[e].neg().squeeze_(0),[s,o]=(0,m.dynamic_time_warping)(t.tolist()),n=Array.from({length:s.length-1},((e,t)=>s[t+1]-s[t])),i=(0,a.mergeArrays)([1],n).map((e=>!!e)),l=[];for(let e=0;e<i.length;++e)i[e]&&l.push(o[e]*r);g[e].data.set(l,1)}return g}}class zs extends Is{}class js extends oe{requires_attention_mask=!1;main_input_name="input_values";forward_params=["input_values","decoder_input_ids","past_key_values"]}class Ds extends js{}class Os extends js{}class Ns extends oe{main_input_name="pixel_values";forward_params=["pixel_values","decoder_input_ids","encoder_hidden_states","past_key_values"]}class Vs extends oe{forward_params=["input_ids","attention_mask","pixel_values","position_ids","past_key_values"]}class Bs extends Vs{_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_index,...e,image_features:s})}}class Gs extends Bs{}class Rs extends Bs{}class $s extends oe{forward_params=["input_ids","inputs_embeds","attention_mask","pixel_values","encoder_outputs","decoder_input_ids","decoder_inputs_embeds","decoder_attention_mask","past_key_values"];main_input_name="inputs_embeds"}class qs extends $s{_merge_input_ids_with_image_features({inputs_embeds:e,image_features:t,input_ids:s,attention_mask:r}){return{inputs_embeds:(0,_.cat)([t,e],1),attention_mask:(0,_.cat)([(0,_.ones)(t.dims.slice(0,2)),r],1)}}async _prepare_inputs_embeds({input_ids:e,pixel_values:t,inputs_embeds:s,attention_mask:r}){if(!e&&!t)throw new Error("Either `input_ids` or `pixel_values` should be provided.");let o,n;return e&&(o=await this.encode_text({input_ids:e})),t&&(n=await this.encode_image({pixel_values:t})),o&&n?({inputs_embeds:s,attention_mask:r}=this._merge_input_ids_with_image_features({inputs_embeds:o,image_features:n,input_ids:e,attention_mask:r})):s=o||n,{inputs_embeds:s,attention_mask:r}}async forward({input_ids:e,pixel_values:t,attention_mask:s,decoder_input_ids:r,decoder_attention_mask:o,encoder_outputs:n,past_key_values:i,inputs_embeds:a,decoder_inputs_embeds:l}){if(a||({inputs_embeds:a,attention_mask:s}=await this._prepare_inputs_embeds({input_ids:e,pixel_values:t,inputs_embeds:a,attention_mask:s})),!n){let{last_hidden_state:e}=await q(this,{inputs_embeds:a,attention_mask:s});n=e}if(!l){if(!r)throw new Error("Either `decoder_input_ids` or `decoder_inputs_embeds` should be provided.");l=await this.encode_text({input_ids:r})}const c={inputs_embeds:l,attention_mask:o,encoder_attention_mask:s,encoder_hidden_states:n,past_key_values:i};return await U(this,c,!0)}}class Ws extends oe{forward_params=["input_ids","attention_mask","pixel_values","position_ids","past_key_values"]}class Us extends Ws{_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_index,...e,image_features:s})}}class Qs extends Vs{_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_index,...e,image_features:s})}}class Xs extends oe{forward_params=["input_ids","attention_mask","inputs_embeds","per_layer_inputs","position_ids","pixel_values","input_features","input_features_mask","past_key_values"]}class Hs extends Xs{async forward({input_ids:e=null,attention_mask:t=null,pixel_values:s=null,input_features:r=null,input_features_mask:o=null,position_ids:n=null,inputs_embeds:i=null,per_layer_inputs:a=null,past_key_values:l=null,generation_config:c=null,logits_processor:d=null,...u}){if(!(i&&a||(({inputs_embeds:i,per_layer_inputs:a}=await V(this.sessions.embed_tokens,{input_ids:e})),1===e.dims[1]))){if(s){const{image_features:r}=await V(this.sessions.vision_encoder,{pixel_values:s});({inputs_embeds:i,attention_mask:t}=this._merge_input_ids_with_image_features({image_features:r,inputs_embeds:i,input_ids:e,attention_mask:t}))}if(r){const{audio_features:s}=await V(this.sessions.audio_encoder,{input_features:r,input_features_mask:o});({inputs_embeds:i,attention_mask:t}=this._merge_input_ids_with_audio_features({audio_features:s,inputs_embeds:i,input_ids:e,attention_mask:t}))}}return await U(this,{inputs_embeds:i,per_layer_inputs:a,past_key_values:l,attention_mask:t,position_ids:n,generation_config:c,logits_processor:d},!0)}_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_id,...e,image_features:s})}_merge_input_ids_with_audio_features(e){const t=e.audio_features.dims.at(-1),s=e.audio_features.view(-1,t);return H({audio_token_id:this.config.audio_token_id,...e,audio_features:s})}}class Js extends oe{forward_params=["input_ids","attention_mask","pixel_values","pixel_attention_mask","position_ids","past_key_values"]}class Ys extends Js{async encode_image({pixel_values:e,pixel_attention_mask:t}){return(await V(this.sessions.vision_encoder,{pixel_values:e,pixel_attention_mask:t})).image_features}_merge_input_ids_with_image_features(e){const t=e.image_features.dims.at(-1),s=e.image_features.view(-1,t);return X({image_token_id:this.config.image_token_id,...e,image_features:s})}}class Ks extends Ys{}class Zs extends oe{forward_params=["input_ids","inputs_embeds","attention_mask","position_ids","pixel_values","image_sizes","past_key_values"]}class er extends Zs{async forward({input_ids:e=null,attention_mask:t=null,pixel_values:s=null,image_sizes:r=null,position_ids:o=null,inputs_embeds:n=null,past_key_values:i=null,generation_config:a=null,logits_processor:l=null,...c}){if(!n){let t;if(s&&1!==e.dims[1]){if(!r)throw new Error("`image_sizes` must be provided when `pixel_values` is provided.");({image_features:t}=await V(this.sessions.vision_encoder,{pixel_values:s,image_sizes:r}))}else{const e=this.config.normalized_config.hidden_size;t=new _.Tensor("float32",[],[0,e])}({inputs_embeds:n}=await V(this.sessions.prepare_inputs_embeds,{input_ids:e,image_features:t}))}return await U(this,{inputs_embeds:n,past_key_values:i,attention_mask:t,position_ids:o,generation_config:a,logits_processor:l},!1)}}class tr extends oe{}class sr extends tr{}class rr extends tr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class or extends tr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class nr extends tr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"vision_model"})}}class ir extends tr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"vision_model"})}}class ar extends oe{}class lr extends ar{}class cr extends ar{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class dr extends tr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"vision_model"})}}class ur extends oe{}class _r extends ur{}class pr extends oe{}class mr extends pr{async forward(e){const t=!e.input_ids,s=!e.pixel_values;if(t&&s)throw new Error("Either `input_ids` or `pixel_values` should be provided.");if(t&&(e.input_ids=(0,_.ones)([e.pixel_values.dims[0],1])),s){const{image_size:t}=this.config.vision_config;e.pixel_values=(0,_.full)([0,3,t,t],0)}const{text_embeddings:r,image_embeddings:o,l2norm_text_embeddings:n,l2norm_image_embeddings:i}=await super.forward(e),a={};return t||(a.text_embeddings=r,a.l2norm_text_embeddings=n),s||(a.image_embeddings=o,a.l2norm_image_embeddings=i),a}}class hr extends pr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class gr extends pr{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"vision_model"})}}class fr extends oe{}class wr extends fr{}class Mr extends fr{}class xr extends oe{}class br extends xr{}class kr extends xr{}class yr extends oe{}class vr extends yr{}class Tr extends yr{}class Pr extends oe{}class Fr extends Pr{}class Cr extends Pr{}class Sr extends oe{}class Er extends Sr{}class Ar extends Sr{}class Lr extends oe{}class Ir extends Lr{}class zr extends Lr{}class jr extends oe{}class Dr extends jr{}class Or extends jr{}class Nr extends oe{}class Vr extends Nr{}class Br extends Nr{}class Gr extends oe{}class Rr extends Gr{}class $r extends Gr{}class qr extends oe{}class Wr extends qr{}class Ur extends qr{}class Qr extends oe{}class Xr extends Qr{}class Hr extends Qr{}class Jr extends oe{}class Yr extends Jr{}class Kr extends Jr{}class Zr extends oe{}class eo extends Zr{}class to extends Zr{}class so extends oe{}class ro extends so{}class oo extends so{}class no extends oe{}class io extends no{}class ao extends no{}class lo extends oe{}class co extends lo{}class uo extends lo{}class _o extends oe{}class po extends _o{}class mo extends _o{}class ho extends oe{}class go extends ho{}class fo extends ho{}class wo extends oe{}class Mo extends wo{}class xo extends wo{}class bo extends oe{}class ko extends bo{}class yo extends bo{}class vo extends oe{}class To extends vo{}class Po extends vo{}class Fo extends oe{}class Co extends Fo{}class So extends Fo{}class Eo extends oe{}class Ao extends Eo{}class Lo extends Eo{}class Io extends oe{}class zo extends Io{}class jo extends Io{}class Do extends oe{forward_params=["input_ids","attention_mask","position_ids","past_key_values","pixel_values","image_grid_thw"]}class Oo extends Do{get_rope_index(e,t,s,r){const{vision_config:o,image_token_id:n,video_token_id:i,vision_start_token_id:a}=this.config,l=o.spatial_merge_size??2,c=[];if(t||s){let o=e.tolist();r||(r=(0,_.ones_like)(e));const d=r.tolist(),u=Array.from({length:3},(t=>Array.from({length:e.dims[0]},(t=>Array.from({length:e.dims[1]},(e=>1)))))),p=t?t.tolist():[],h=s?s.tolist():[];let g=0,f=0;for(let e=0;e<o.length;++e){const t=o[e].filter(((t,s)=>1==d[e][s])),s=t.reduce(((e,t,s)=>(t==a&&e.push(s),e)),[]).map((e=>t[e+1])),r=s.filter((e=>e==n)).length,_=s.filter((e=>e==i)).length;let w=[],M=0,x=r,b=_;for(let e=0;e<s.length;++e){const e=t.findIndex(((e,t)=>t>M&&e==n)),s=t.findIndex(((e,t)=>t>M&&e==i)),r=x>0&&-1!==e?e:t.length+1,o=b>0&&-1!==s?s:t.length+1;let a,c,d,u;r<o?([c,d,u]=p[g],++g,--x,a=r):([c,d,u]=h[f],++f,--b,a=o);const[_,k,y]=[Number(c),Math.floor(Number(d)/l),Math.floor(Number(u)/l)],v=a-M,T=w.length>0?(0,m.max)(w.at(-1))[0]+1:0;w.push(Array.from({length:3*v},((e,t)=>T+t%v)));const P=v+T,F=_*k*y,C=Array.from({length:F},((e,t)=>P+Math.floor(t/(k*y)))),S=Array.from({length:F},((e,t)=>P+Math.floor(t/y)%k)),E=Array.from({length:F},((e,t)=>P+t%y));w.push([C,S,E].flat()),M=a+F}if(M<t.length){const e=w.length>0?(0,m.max)(w.at(-1))[0]+1:0,s=t.length-M;w.push(Array.from({length:3*s},((t,r)=>e+r%s)))}const k=w.reduce(((e,t)=>e+t.length),0),y=new Array(k);let v=0;for(let e=0;e<3;++e)for(let t=0;t<w.length;++t){const s=w[t],r=s.length/3;for(let t=e*r;t<(e+1)*r;++t)y[v++]=s[t]}let T=0;const P=d[e];for(let t=0;t<P.length;++t)if(1==P[t]){for(let s=0;s<3;++s)u[s][e][t]=y[s*k/3+T];++T}const F=(0,m.max)(y)[0];c.push(F+1-o[e].length)}return[new _.Tensor("int64",u.flat(1/0),[3,e.dims[0],e.dims[1]]),new _.Tensor("int64",c,[c.length,1])]}if(r){const{data:e,dims:t}=Z(r),s=BigInt64Array.from({length:3*e.length},((t,s)=>e[s%e.length])),o=Array.from({length:t[0]},((s,r)=>(0,m.max)(e.subarray(t[1]*r,t[1]*(r+1)))[0]+1n+BigInt(t[1])));return[new _.Tensor("int64",s,[3,...t]),new _.Tensor("int64",o,[o.length,1])]}{const[t,s]=e.dims,r=BigInt64Array.from({length:3*t*s},((e,r)=>BigInt(Math.floor(r%s/t))));return[new _.Tensor("int64",r,[3,...e.dims]),(0,_.zeros)([t,1])]}}async encode_image({pixel_values:e,image_grid_thw:t}){return(await V(this.sessions.vision_encoder,{pixel_values:e,grid_thw:t})).image_features}_merge_input_ids_with_image_features(e){return X({image_token_id:this.config.image_token_id,...e})}prepare_inputs_for_generation(e,t,s){if(t.attention_mask&&!t.position_ids)if(t.past_key_values){t.pixel_values=null;const e=BigInt(Object.values(t.past_key_values)[0].dims.at(-2)),s=t.rope_deltas.map((t=>e+t));t.position_ids=(0,_.stack)([s,s,s],0)}else[t.position_ids,t.rope_deltas]=this.get_rope_index(t.input_ids,t.image_grid_thw,t.video_grid_thw,t.attention_mask);return t}}class No extends oe{}class Vo extends No{}class Bo extends No{}class Go extends oe{}class Ro extends Go{}class $o extends Go{}class qo extends oe{}class Wo extends qo{}class Uo extends qo{}class Qo extends oe{}class Xo extends Qo{}class Ho extends Qo{}class Jo extends oe{}class Yo extends Jo{}class Ko extends Jo{}class Zo extends oe{}class en extends Zo{}class tn extends Zo{async _call(e){return new Kd(await super._call(e))}}class sn extends oe{}class rn extends sn{}class on extends sn{async _call(e){return new Kd(await super._call(e))}}class nn extends oe{}class an extends nn{}class ln extends oe{}class cn extends ln{}class dn extends ln{async _call(e){return new Kd(await super._call(e))}}class un extends oe{}class _n extends un{}class pn extends oe{}class mn extends pn{}class hn extends pn{async _call(e){return new Kd(await super._call(e))}}class gn extends oe{}class fn extends gn{}class wn extends oe{}class Mn extends wn{}class xn extends wn{async _call(e){return new Kd(await super._call(e))}}class bn extends oe{}class kn extends bn{async _call(e){return new nu(await super._call(e))}}class yn extends oe{}class vn extends yn{}class Tn extends yn{async _call(e){return new Kd(await super._call(e))}}class Pn extends oe{}class Fn extends Pn{}class Cn extends Pn{async _call(e){return new Kd(await super._call(e))}}class Sn extends oe{}class En extends Sn{}class An extends Sn{}class Ln extends oe{}class In extends Ln{}class zn extends Ln{}class jn extends oe{}class Dn extends jn{}class On extends jn{async _call(e){return new Kd(await super._call(e))}}class Nn extends oe{}class Vn extends Nn{}class Bn extends Nn{async _call(e){return new Rn(await super._call(e))}}class Gn extends Nn{async _call(e){return new $n(await super._call(e))}}class Rn extends ne{constructor({logits:e,pred_boxes:t}){super(),this.logits=e,this.pred_boxes=t}}class $n extends ne{constructor({logits:e,pred_boxes:t,pred_masks:s}){super(),this.logits=e,this.pred_boxes=t,this.pred_masks=s}}class qn extends oe{}class Wn extends qn{}class Un extends qn{async _call(e){return new Qn(await super._call(e))}}class Qn extends ne{constructor({logits:e,pred_boxes:t}){super(),this.logits=e,this.pred_boxes=t}}class Xn extends oe{}class Hn extends Xn{}class Jn extends Xn{async _call(e){return new Yn(await super._call(e))}}class Yn extends Qn{}class Kn extends oe{}class Zn extends Kn{}class ei extends Kn{async _call(e){return new ti(await super._call(e))}}class ti extends Qn{}class si extends oe{}class ri extends si{}class oi extends si{async _call(e){return new Qn(await super._call(e))}}class ni extends oe{}class ii extends ni{}class ai extends ni{async _call(e){return new li(await super._call(e))}}class li extends Rn{}class ci extends oe{}class di extends ci{}class ui extends ci{async _call(e){return new Kd(await super._call(e))}}class _i extends oe{}class pi extends _i{}class mi extends _i{async _call(e){return new Kd(await super._call(e))}}class hi extends oe{}class gi extends hi{}class fi extends hi{async _call(e){return new Kd(await super._call(e))}}class wi extends oe{}class Mi extends wi{}class xi extends wi{async _call(e){return new Kd(await super._call(e))}}class bi extends wi{}class ki extends oe{}class yi extends ki{}class vi extends ki{}class Ti extends oe{}class Pi extends Ti{}class Fi extends Ti{}class Ci extends oe{}class Si extends Ci{}class Ei extends oe{}class Ai extends Ei{}class Li extends Ei{}class Ii extends Ei{}class zi extends oe{}class ji extends zi{}class Di extends oe{}class Oi extends Di{}class Ni extends oe{}class Vi extends Ni{}class Bi extends oe{}class Gi extends Bi{}class Ri extends Bi{}class $i extends oe{}class qi extends $i{}class Wi extends $i{}class Ui extends oe{}class Qi extends Ui{}class Xi extends oe{}class Hi extends Xi{}class Ji extends Xi{async _call(e){return new Kd(await super._call(e))}}class Yi extends oe{}class Ki extends Yi{}class Zi extends Yi{async _call(e){return new Kd(await super._call(e))}}class ea extends oe{}class ta extends ea{}class sa extends ea{async _call(e){return new Kd(await super._call(e))}}class ra extends oe{}class oa extends ra{}class na extends ra{async _call(e){return new Kd(await super._call(e))}}class ia extends oe{}class aa extends ia{}class la extends oe{}class ca extends la{}class da extends la{async _call(e){return new ua(await super._call(e))}}class ua extends ne{constructor({logits:e,pred_boxes:t}){super(),this.logits=e,this.pred_boxes=t}}class _a extends oe{}class pa extends _a{async get_image_embeddings({pixel_values:e}){return await q(this,{pixel_values:e})}async forward(e){if(e.image_embeddings&&e.image_positional_embeddings||(e={...e,...await this.get_image_embeddings(e)}),!e.input_labels&&e.input_points){const t=e.input_points.dims.slice(0,-1),s=t.reduce(((e,t)=>e*t),1);e.input_labels=new _.Tensor("int64",new BigInt64Array(s).fill(1n),t)}const t={image_embeddings:e.image_embeddings,image_positional_embeddings:e.image_positional_embeddings};return e.input_points&&(t.input_points=e.input_points),e.input_labels&&(t.input_labels=e.input_labels),e.input_boxes&&(t.input_boxes=e.input_boxes),await V(this.sessions.prompt_encoder_mask_decoder,t)}async _call(e){return new ma(await super._call(e))}}class ma extends ne{constructor({iou_scores:e,pred_masks:t}){super(),this.iou_scores=e,this.pred_masks=t}}class ha extends oe{}class ga extends ha{}class fa extends ha{}class wa extends oe{}class Ma extends wa{}class xa extends wa{}class ba extends oe{}class ka extends ba{}class ya extends ba{async _call(e){return new ru(await super._call(e))}}class va extends ba{async _call(e){return new Kd(await super._call(e))}}class Ta extends ba{async _call(e){return new eu(await super._call(e))}}class Pa extends oe{}class Fa extends Pa{}class Ca extends Pa{async _call(e){return new eu(await super._call(e))}}class Sa extends oe{}class Ea extends Sa{}class Aa extends oe{}class La extends Aa{}class Ia extends Aa{async _call(e){return new ru(await super._call(e))}}class za extends Aa{async _call(e){return new Kd(await super._call(e))}}class ja extends oe{}class Da extends ja{}class Oa extends ja{async _call(e){return new ru(await super._call(e))}}class Na extends ja{async _call(e){return new Kd(await super._call(e))}}class Va extends ja{async _call(e){return new eu(await super._call(e))}}class Ba extends oe{}class Ga extends Ba{}class Ra extends Ba{async _call(e){return new ru(await super._call(e))}}class $a extends Ba{async _call(e){return new Kd(await super._call(e))}}class qa extends oe{}class Wa extends ba{}class Ua extends ba{async _call(e){return new ru(await super._call(e))}}class Qa extends ba{async _call(e){return new Kd(await super._call(e))}}class Xa extends oe{}class Ha extends Xa{}class Ja extends Xa{async _call(e){return new ru(await super._call(e))}}class Ya extends Xa{async _call(e){return new Kd(await super._call(e))}}class Ka extends Xa{async _call(e){return new Zd(await super._call(e))}}class Za extends Xa{async _call(e){return new eu(await super._call(e))}}class el extends oe{}class tl extends el{}class sl extends oe{}class rl extends sl{}class ol extends sl{}class nl extends sl{async generate_speech(e,t,{threshold:s=.5,minlenratio:r=0,maxlenratio:o=20,vocoder:n=null}={}){const i={input_ids:e},{encoder_outputs:a,encoder_attention_mask:l}=await q(this,i),c=a.dims[1]/this.config.reduction_factor,d=Math.floor(c*o),u=Math.floor(c*r),p=this.config.num_mel_bins;let m=[],h=null,g=null,f=0;for(;;){++f;const e=R(!!g);let r;r=g?g.output_sequence_out:new _.Tensor("float32",new Float32Array(p),[1,1,p]);let o={use_cache_branch:e,output_sequence:r,encoder_attention_mask:l,speaker_embeddings:t,encoder_hidden_states:a};this.addPastKeyValues(o,h),g=await V(this.sessions.decoder_model_merged,o),h=this.getPastKeyValues(g,h);const{prob:n,spectrum:i}=g;if(m.push(i),f>=u&&(Array.from(n.data).filter((e=>e>=s)).length>0||f>=d))break}const w=(0,_.cat)(m),{waveform:M}=await V(n.sessions.model,{spectrogram:w});return{spectrogram:w,waveform:M}}}class il extends oe{main_input_name="spectrogram"}class al extends oe{}class ll extends al{}class cl extends oe{}class dl extends cl{}class ul extends cl{}class _l extends oe{}class pl extends _l{}class ml extends _l{}class hl extends oe{}class gl extends hl{}class fl extends hl{}class wl extends oe{}class Ml extends wl{}class xl extends wl{}class bl extends oe{}class kl extends bl{}class yl extends bl{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"text_model"})}}class vl extends bl{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"audio_model"})}}class Tl extends oe{}class Pl extends Tl{async _call(e){return new iu(await super._call(e))}}class Fl extends oe{}class Cl extends Fl{}class Sl extends Fl{}class El extends Fl{}class Al extends oe{}class Ll extends Al{}class Il extends Al{}class zl extends oe{}class jl extends zl{}class Dl extends zl{async _call(e){return new Kd(await super._call(e))}}class Ol extends oe{}class Nl extends Ol{}class Vl extends Ol{}class Bl extends oe{forward_params=["input_ids","attention_mask","encoder_outputs","decoder_input_ids","decoder_attention_mask","past_key_values"];_apply_and_filter_by_delay_pattern_mask(e){const[t,s]=e.dims,r=this.config.decoder.num_codebooks,o=s-r;let n=0;for(let t=0;t<e.size;++t){if(e.data[t]===this.config.decoder.pad_token_id)continue;const i=t%s-Math.floor(t/s)%r;i>0&&i<=o&&(e.data[n++]=e.data[t])}const i=Math.floor(t/r),a=n/(i*r);return new _.Tensor(e.type,e.data.slice(0,n),[i,r,a])}prepare_inputs_for_generation(e,t,s){let r=structuredClone(e);for(let e=0;e<r.length;++e)for(let t=0;t<r[e].length;++t)e%this.config.decoder.num_codebooks>=t&&(r[e][t]=BigInt(this.config.decoder.pad_token_id));null!==s.guidance_scale&&s.guidance_scale>1&&(r=r.concat(r));return super.prepare_inputs_for_generation(r,t,s)}async generate(e){const t=await super.generate(e),s=this._apply_and_filter_by_delay_pattern_mask(t).unsqueeze_(0),{audio_values:r}=await V(this.sessions.encodec_decode,{audio_codes:s});return r}}class Gl extends oe{}class Rl extends Gl{}class $l extends Gl{async _call(e){return new Kd(await super._call(e))}}class ql extends Gl{}class Wl extends oe{}class Ul extends Wl{}class Ql extends Wl{async _call(e){return new Kd(await super._call(e))}}class Xl extends Wl{}class Hl extends oe{}class Jl extends Hl{}class Yl extends Hl{async _call(e){return new Kd(await super._call(e))}}class Kl extends Hl{}class Zl extends oe{}class ec extends Zl{}class tc extends Zl{async _call(e){return new Kd(await super._call(e))}}class sc extends Zl{}class rc extends oe{}class oc extends rc{}class nc extends oe{}class ic extends nc{forward_params=["input_ids","pixel_values","images_seq_mask","images_emb_mask","attention_mask","position_ids","past_key_values"];constructor(...e){super(...e),this._generation_mode="text"}async forward(e){const t=this._generation_mode??"text";let s;if("text"!==t&&e.past_key_values){const t=this.sessions.gen_img_embeds,r=(0,a.pick)({image_ids:e.input_ids},t.inputNames);s=await V(t,r)}else{const t=this.sessions.prepare_inputs_embeds,r=(0,a.pick)(e,t.inputNames);s=await V(t,r)}const r={...e,...s},o=await U(this,r),n=this.sessions["text"===t?"lm_head":"gen_head"];if(!n)throw new Error(`Unable to find "${n}" generation head`);const i=await V(n,(0,a.pick)(o,n.inputNames));return{...s,...o,...i}}async generate(e){return this._generation_mode="text",super.generate(e)}async generate_images(e){this._generation_mode="image";const t=(e.inputs??e[this.main_input_name]).dims[1],s=(await super.generate(e)).slice(null,[t,null]),r=this.sessions.image_decode,{decoded_image:o}=await V(r,{generated_tokens:s}),n=o.add_(1).mul_(127.5).clamp_(0,255).to("uint8"),i=[];for(const e of n){const t=p.RawImage.fromTensor(e);i.push(t)}return i}}class ac extends ne{constructor({char_logits:e,bpe_logits:t,wp_logits:s}){super(),this.char_logits=e,this.bpe_logits=t,this.wp_logits=s}get logits(){return[this.char_logits,this.bpe_logits,this.wp_logits]}}class lc extends oe{}class cc extends lc{async _call(e){return new ac(await super._call(e))}}class dc extends oe{}class uc extends dc{}class _c extends dc{}class pc extends oe{}class mc extends pc{}class hc extends pc{}class gc extends oe{forward_params=["input_ids","attention_mask","position_ids","audio_values","past_key_values"]}class fc extends gc{_merge_input_ids_with_audio_features(e){const t=e.audio_features.dims.at(-1),s=e.audio_features.view(-1,t);return H({audio_token_id:this.config.ignore_index,...e,audio_features:s})}}class wc extends oe{main_input_name="input_values";forward_params=["input_values"]}class Mc extends ne{constructor({audio_codes:e}){super(),this.audio_codes=e}}class xc extends ne{constructor({audio_values:e}){super(),this.audio_values=e}}class bc extends wc{async encode(e){return new Mc(await V(this.sessions.encoder_model,e))}async decode(e){return new xc(await V(this.sessions.decoder_model,e))}}class kc extends wc{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"encoder_model"})}}class yc extends wc{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"decoder_model"})}}class vc extends oe{main_input_name="input_values";forward_params=["input_values"]}class Tc extends ne{constructor({audio_codes:e}){super(),this.audio_codes=e}}class Pc extends ne{constructor({audio_values:e}){super(),this.audio_values=e}}class Fc extends vc{async encode(e){return new Tc(await V(this.sessions.encoder_model,e))}async decode(e){return new Pc(await V(this.sessions.decoder_model,e))}}class Cc extends vc{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"encoder_model"})}}class Sc extends vc{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"decoder_model"})}}class Ec extends oe{main_input_name="input_values";forward_params=["input_values"]}class Ac extends Ec{async encode(e){return await V(this.sessions.encoder_model,e)}async decode(e){return await V(this.sessions.decoder_model,e)}}class Lc extends Ec{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"encoder_model"})}}class Ic extends Ec{static async from_pretrained(e,t={}){return super.from_pretrained(e,{...t,model_file_name:t.model_file_name??"decoder_model"})}}class zc{static MODEL_CLASS_MAPPINGS=null;static BASE_IF_FAIL=!1;static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:o=null,local_files_only:n=!1,revision:i="main",model_file_name:a=null,subfolder:l="onnx",device:c=null,dtype:d=null,use_external_data_format:u=null,session_options:_={}}={}){const p={progress_callback:t,config:s,cache_dir:o,local_files_only:n,revision:i,model_file_name:a,subfolder:l,device:c,dtype:d,use_external_data_format:u,session_options:_};if(p.config=await r.AutoConfig.from_pretrained(e,p),!this.MODEL_CLASS_MAPPINGS)throw new Error("`MODEL_CLASS_MAPPINGS` not implemented for this type of `AutoClass`: "+this.name);const m=p.config.model_type;for(const t of this.MODEL_CLASS_MAPPINGS){let s=t.get(m);if(!s){for(const e of t.values())if(e[0]===m){s=e;break}if(!s)continue}return await s[1].from_pretrained(e,p)}if(this.BASE_IF_FAIL)return Md.has(m)||console.warn(`Unknown model class "${m}", attempting to construct from base class.`),await oe.from_pretrained(e,p);throw Error(`Unsupported model type: ${m}`)}}const jc=new Map([["bert",["BertModel",le]],["neobert",["NeoBertModel",me]],["modernbert",["ModernBertModel",xe]],["nomic_bert",["NomicBertModel",Te]],["roformer",["RoFormerModel",Fe]],["electra",["ElectraModel",Ve]],["esm",["EsmModel",ht]],["convbert",["ConvBertModel",Ie]],["camembert",["CamembertModel",We]],["deberta",["DebertaModel",Ye]],["deberta-v2",["DebertaV2Model",rt]],["mpnet",["MPNetModel",Tt]],["albert",["AlbertModel",Dt]],["distilbert",["DistilBertModel",ct]],["roberta",["RobertaModel",us]],["xlm",["XLMModel",fs]],["xlm-roberta",["XLMRobertaModel",ys]],["clap",["ClapModel",kl]],["clip",["CLIPModel",sr]],["clipseg",["CLIPSegModel",wr]],["chinese_clip",["ChineseCLIPModel",_r]],["siglip",["SiglipModel",lr]],["jina_clip",["JinaCLIPModel",mr]],["mobilebert",["MobileBertModel",xt]],["squeezebert",["SqueezeBertModel",At]],["wav2vec2",["Wav2Vec2Model",ka]],["wav2vec2-bert",["Wav2Vec2BertModel",Ga]],["unispeech",["UniSpeechModel",La]],["unispeech-sat",["UniSpeechSatModel",Da]],["hubert",["HubertModel",Wa]],["wavlm",["WavLMModel",Ha]],["audio-spectrogram-transformer",["ASTModel",Ss]],["vits",["VitsModel",Pl]],["pyannote",["PyAnnoteModel",Fa]],["wespeaker-resnet",["WeSpeakerResNetModel",Ea]],["detr",["DetrModel",Vn]],["rt_detr",["RTDetrModel",Wn]],["rt_detr_v2",["RTDetrV2Model",Hn]],["rf_detr",["RFDetrModel",Zn]],["d_fine",["DFineModel",ri]],["table-transformer",["TableTransformerModel",ii]],["vit",["ViTModel",en]],["ijepa",["IJepaModel",rn]],["pvt",["PvtModel",cn]],["vit_msn",["ViTMSNModel",mn]],["vit_mae",["ViTMAEModel",_n]],["groupvit",["GroupViTModel",fn]],["fastvit",["FastViTModel",Mn]],["mobilevit",["MobileViTModel",vn]],["mobilevitv2",["MobileViTV2Model",Fn]],["owlvit",["OwlViTModel",En]],["owlv2",["Owlv2Model",In]],["beit",["BeitModel",Dn]],["deit",["DeiTModel",di]],["hiera",["HieraModel",pi]],["convnext",["ConvNextModel",Hi]],["convnextv2",["ConvNextV2Model",Ki]],["dinov2",["Dinov2Model",ta]],["dinov2_with_registers",["Dinov2WithRegistersModel",oa]],["resnet",["ResNetModel",gi]],["swin",["SwinModel",Mi]],["swin2sr",["Swin2SRModel",yi]],["donut-swin",["DonutSwinModel",Qi]],["yolos",["YolosModel",ca]],["dpt",["DPTModel",Pi]],["glpn",["GLPNModel",qi]],["hifigan",["SpeechT5HifiGan",il]],["efficientnet",["EfficientNetModel",jl]],["decision_transformer",["DecisionTransformerModel",oc]],["patchtst",["PatchTSTForPrediction",uc]],["patchtsmixer",["PatchTSMixerForPrediction",mc]],["mobilenet_v1",["MobileNetV1Model",Rl]],["mobilenet_v2",["MobileNetV2Model",Ul]],["mobilenet_v3",["MobileNetV3Model",Jl]],["mobilenet_v4",["MobileNetV4Model",ec]],["maskformer",["MaskFormerModel",Gi]],["mgp-str",["MgpstrForSceneTextRecognition",cc]],["style_text_to_speech_2",["StyleTextToSpeech2Model",tl]]]),Dc=new Map([["t5",["T5Model",Gt]],["longt5",["LongT5Model",qt]],["mt5",["MT5Model",Qt]],["bart",["BartModel",Jt]],["mbart",["MBartModel",es]],["marian",["MarianModel",ga]],["whisper",["WhisperModel",Ls]],["m2m_100",["M2M100Model",Ma]],["blenderbot",["BlenderbotModel",ns]],["blenderbot-small",["BlenderbotSmallModel",ls]]]),Oc=new Map([["mimi",["MimiModel",bc]],["dac",["DacModel",Fc]],["snac",["SnacModel",Ac]]]),Nc=new Map([["bloom",["BloomModel",Wo]],["jais",["JAISModel",vr]],["gpt2",["GPT2Model",br]],["gptj",["GPTJModel",Ir]],["gpt_bigcode",["GPTBigCodeModel",Dr]],["gpt_neo",["GPTNeoModel",Fr]],["gpt_neox",["GPTNeoXModel",Er]],["codegen",["CodeGenModel",Vr]],["llama",["LlamaModel",Rr]],["smollm3",["SmolLM3Model",Wr]],["exaone",["ExaoneModel",eo]],["olmo",["OlmoModel",io]],["olmo2",["Olmo2Model",co]],["mobilellm",["MobileLLMModel",ro]],["granite",["GraniteModel",po]],["cohere",["CohereModel",go]],["gemma",["GemmaModel",Mo]],["gemma2",["Gemma2Model",ko]],["gemma3_text",["Gemma3Model",To]],["helium",["HeliumModel",Xr]],["glm",["GlmModel",Yr]],["openelm",["OpenELMModel",Co]],["qwen2",["Qwen2Model",Ao]],["qwen3",["Qwen3Model",zo]],["phi",["PhiModel",Vo]],["phi3",["Phi3Model",Ro]],["mpt",["MptModel",Xo]],["opt",["OPTModel",Yo]],["mistral",["MistralModel",dl]],["ernie4_5",["Ernie4_5_Model",pl]],["starcoder2",["Starcoder2Model",gl]],["falcon",["FalconModel",Ml]],["stablelm",["StableLmModel",Ll]]]),Vc=new Map([["speecht5",["SpeechT5ForSpeechToText",ol]],["whisper",["WhisperForConditionalGeneration",Is]],["lite-whisper",["LiteWhisperForConditionalGeneration",zs]],["moonshine",["MoonshineForConditionalGeneration",Os]]]),Bc=new Map([["speecht5",["SpeechT5ForTextToSpeech",nl]]]),Gc=new Map([["vits",["VitsModel",Pl]],["musicgen",["MusicgenForConditionalGeneration",Bl]]]),Rc=new Map([["bert",["BertForSequenceClassification",de]],["neobert",["NeoBertForSequenceClassification",ge]],["modernbert",["ModernBertForSequenceClassification",ke]],["roformer",["RoFormerForSequenceClassification",Se]],["electra",["ElectraForSequenceClassification",Ge]],["esm",["EsmForSequenceClassification",ft]],["convbert",["ConvBertForSequenceClassification",je]],["camembert",["CamembertForSequenceClassification",Qe]],["deberta",["DebertaForSequenceClassification",Ze]],["deberta-v2",["DebertaV2ForSequenceClassification",nt]],["mpnet",["MPNetForSequenceClassification",Ft]],["albert",["AlbertForSequenceClassification",Ot]],["distilbert",["DistilBertForSequenceClassification",dt]],["roberta",["RobertaForSequenceClassification",ps]],["xlm",["XLMForSequenceClassification",Ms]],["xlm-roberta",["XLMRobertaForSequenceClassification",Ts]],["bart",["BartForSequenceClassification",Kt]],["mbart",["MBartForSequenceClassification",ss]],["mobilebert",["MobileBertForSequenceClassification",kt]],["squeezebert",["SqueezeBertForSequenceClassification",It]]]),$c=new Map([["bert",["BertForTokenClassification",ue]],["neobert",["NeoBertForTokenClassification",fe]],["modernbert",["ModernBertForTokenClassification",ye]],["roformer",["RoFormerForTokenClassification",Ee]],["electra",["ElectraForTokenClassification",Re]],["esm",["EsmForTokenClassification",wt]],["convbert",["ConvBertForTokenClassification",De]],["camembert",["CamembertForTokenClassification",Xe]],["deberta",["DebertaForTokenClassification",et]],["deberta-v2",["DebertaV2ForTokenClassification",it]],["mpnet",["MPNetForTokenClassification",Ct]],["distilbert",["DistilBertForTokenClassification",ut]],["roberta",["RobertaForTokenClassification",ms]],["xlm",["XLMForTokenClassification",xs]],["xlm-roberta",["XLMRobertaForTokenClassification",Ps]]]),qc=new Map([["t5",["T5ForConditionalGeneration",Rt]],["longt5",["LongT5ForConditionalGeneration",Wt]],["mt5",["MT5ForConditionalGeneration",Xt]],["bart",["BartForConditionalGeneration",Yt]],["mbart",["MBartForConditionalGeneration",ts]],["marian",["MarianMTModel",fa]],["m2m_100",["M2M100ForConditionalGeneration",xa]],["blenderbot",["BlenderbotForConditionalGeneration",is]],["blenderbot-small",["BlenderbotSmallForConditionalGeneration",cs]]]),Wc=new Map([["bloom",["BloomForCausalLM",Uo]],["gpt2",["GPT2LMHeadModel",kr]],["jais",["JAISLMHeadModel",Tr]],["gptj",["GPTJForCausalLM",zr]],["gpt_bigcode",["GPTBigCodeForCausalLM",Or]],["gpt_neo",["GPTNeoForCausalLM",Cr]],["gpt_neox",["GPTNeoXForCausalLM",Ar]],["codegen",["CodeGenForCausalLM",Br]],["llama",["LlamaForCausalLM",$r]],["smollm3",["SmolLM3ForCausalLM",Ur]],["exaone",["ExaoneForCausalLM",to]],["olmo",["OlmoForCausalLM",ao]],["olmo2",["Olmo2ForCausalLM",uo]],["mobilellm",["MobileLLMForCausalLM",oo]],["granite",["GraniteForCausalLM",mo]],["cohere",["CohereForCausalLM",fo]],["gemma",["GemmaForCausalLM",xo]],["gemma2",["Gemma2ForCausalLM",yo]],["gemma3_text",["Gemma3ForCausalLM",Po]],["helium",["HeliumForCausalLM",Hr]],["glm",["GlmForCausalLM",Kr]],["openelm",["OpenELMForCausalLM",So]],["qwen2",["Qwen2ForCausalLM",Lo]],["qwen3",["Qwen3ForCausalLM",jo]],["phi",["PhiForCausalLM",Bo]],["phi3",["Phi3ForCausalLM",$o]],["mpt",["MptForCausalLM",Ho]],["opt",["OPTForCausalLM",Ko]],["mbart",["MBartForCausalLM",rs]],["mistral",["MistralForCausalLM",ul]],["ernie4_5",["Ernie4_5_ForCausalLM",ml]],["starcoder2",["Starcoder2ForCausalLM",fl]],["falcon",["FalconForCausalLM",xl]],["trocr",["TrOCRForCausalLM",ll]],["stablelm",["StableLmForCausalLM",Il]],["phi3_v",["Phi3VForCausalLM",er]]]),Uc=new Map([["multi_modality",["MultiModalityCausalLM",ic]]]),Qc=new Map([["bert",["BertForMaskedLM",ce]],["neobert",["NeoBertForMaskedLM",he]],["modernbert",["ModernBertForMaskedLM",be]],["roformer",["RoFormerForMaskedLM",Ce]],["electra",["ElectraForMaskedLM",Be]],["esm",["EsmForMaskedLM",gt]],["convbert",["ConvBertForMaskedLM",ze]],["camembert",["CamembertForMaskedLM",Ue]],["deberta",["DebertaForMaskedLM",Ke]],["deberta-v2",["DebertaV2ForMaskedLM",ot]],["mpnet",["MPNetForMaskedLM",Pt]],["albert",["AlbertForMaskedLM",Vt]],["distilbert",["DistilBertForMaskedLM",pt]],["roberta",["RobertaForMaskedLM",_s]],["xlm",["XLMWithLMHeadModel",ws]],["xlm-roberta",["XLMRobertaForMaskedLM",vs]],["mobilebert",["MobileBertForMaskedLM",bt]],["squeezebert",["SqueezeBertForMaskedLM",Lt]]]),Xc=new Map([["bert",["BertForQuestionAnswering",_e]],["neobert",["NeoBertForQuestionAnswering",we]],["roformer",["RoFormerForQuestionAnswering",Ae]],["electra",["ElectraForQuestionAnswering",$e]],["convbert",["ConvBertForQuestionAnswering",Oe]],["camembert",["CamembertForQuestionAnswering",He]],["deberta",["DebertaForQuestionAnswering",tt]],["deberta-v2",["DebertaV2ForQuestionAnswering",at]],["mpnet",["MPNetForQuestionAnswering",St]],["albert",["AlbertForQuestionAnswering",Nt]],["distilbert",["DistilBertForQuestionAnswering",_t]],["roberta",["RobertaForQuestionAnswering",hs]],["xlm",["XLMForQuestionAnswering",bs]],["xlm-roberta",["XLMRobertaForQuestionAnswering",Fs]],["mobilebert",["MobileBertForQuestionAnswering",yt]],["squeezebert",["SqueezeBertForQuestionAnswering",zt]]]),Hc=new Map([["vision-encoder-decoder",["VisionEncoderDecoderModel",Ns]],["idefics3",["Idefics3ForConditionalGeneration",Ys]],["smolvlm",["SmolVLMForConditionalGeneration",Ks]]]),Jc=new Map([["llava",["LlavaForConditionalGeneration",Bs]],["llava_onevision",["LlavaOnevisionForConditionalGeneration",Gs]],["moondream1",["Moondream1ForConditionalGeneration",Rs]],["florence2",["Florence2ForConditionalGeneration",qs]],["qwen2-vl",["Qwen2VLForConditionalGeneration",Oo]],["idefics3",["Idefics3ForConditionalGeneration",Ys]],["smolvlm",["SmolVLMForConditionalGeneration",Ks]],["paligemma",["PaliGemmaForConditionalGeneration",Us]],["llava_qwen2",["LlavaQwen2ForCausalLM",Qs]],["gemma3n",["Gemma3nForConditionalGeneration",Hs]]]),Yc=new Map([["ultravox",["UltravoxModel",fc]]]),Kc=new Map([["vision-encoder-decoder",["VisionEncoderDecoderModel",Ns]]]),Zc=new Map([["vit",["ViTForImageClassification",tn]],["ijepa",["IJepaForImageClassification",on]],["pvt",["PvtForImageClassification",dn]],["vit_msn",["ViTMSNForImageClassification",hn]],["fastvit",["FastViTForImageClassification",xn]],["mobilevit",["MobileViTForImageClassification",Tn]],["mobilevitv2",["MobileViTV2ForImageClassification",Cn]],["beit",["BeitForImageClassification",On]],["deit",["DeiTForImageClassification",ui]],["hiera",["HieraForImageClassification",mi]],["convnext",["ConvNextForImageClassification",Ji]],["convnextv2",["ConvNextV2ForImageClassification",Zi]],["dinov2",["Dinov2ForImageClassification",sa]],["dinov2_with_registers",["Dinov2WithRegistersForImageClassification",na]],["resnet",["ResNetForImageClassification",fi]],["swin",["SwinForImageClassification",xi]],["segformer",["SegformerForImageClassification",Sl]],["efficientnet",["EfficientNetForImageClassification",Dl]],["mobilenet_v1",["MobileNetV1ForImageClassification",$l]],["mobilenet_v2",["MobileNetV2ForImageClassification",Ql]],["mobilenet_v3",["MobileNetV3ForImageClassification",Yl]],["mobilenet_v4",["MobileNetV4ForImageClassification",tc]]]),ed=new Map([["detr",["DetrForObjectDetection",Bn]],["rt_detr",["RTDetrForObjectDetection",Un]],["rt_detr_v2",["RTDetrV2ForObjectDetection",Jn]],["rf_detr",["RFDetrForObjectDetection",ei]],["d_fine",["DFineForObjectDetection",oi]],["table-transformer",["TableTransformerForObjectDetection",ai]],["yolos",["YolosForObjectDetection",da]]]),td=new Map([["owlvit",["OwlViTForObjectDetection",An]],["owlv2",["Owlv2ForObjectDetection",zn]],["grounding-dino",["GroundingDinoForObjectDetection",aa]]]),sd=new Map([["detr",["DetrForSegmentation",Gn]],["clipseg",["CLIPSegForImageSegmentation",Mr]]]),rd=new Map([["segformer",["SegformerForSemanticSegmentation",El]],["sapiens",["SapiensForSemanticSegmentation",Ai]],["swin",["SwinForSemanticSegmentation",bi]],["mobilenet_v1",["MobileNetV1ForSemanticSegmentation",ql]],["mobilenet_v2",["MobileNetV2ForSemanticSegmentation",Xl]],["mobilenet_v3",["MobileNetV3ForSemanticSegmentation",Kl]],["mobilenet_v4",["MobileNetV4ForSemanticSegmentation",sc]]]),od=new Map([["detr",["DetrForSegmentation",Gn]],["maskformer",["MaskFormerForInstanceSegmentation",Ri]]]),nd=new Map([["sam",["SamModel",pa]]]),id=new Map([["wav2vec2",["Wav2Vec2ForCTC",ya]],["wav2vec2-bert",["Wav2Vec2BertForCTC",Ra]],["unispeech",["UniSpeechForCTC",Ia]],["unispeech-sat",["UniSpeechSatForCTC",Oa]],["wavlm",["WavLMForCTC",Ja]],["hubert",["HubertForCTC",Ua]]]),ad=new Map([["wav2vec2",["Wav2Vec2ForSequenceClassification",va]],["wav2vec2-bert",["Wav2Vec2BertForSequenceClassification",$a]],["unispeech",["UniSpeechForSequenceClassification",za]],["unispeech-sat",["UniSpeechSatForSequenceClassification",Na]],["wavlm",["WavLMForSequenceClassification",Ya]],["hubert",["HubertForSequenceClassification",Qa]],["audio-spectrogram-transformer",["ASTForAudioClassification",Es]]]),ld=new Map([["wavlm",["WavLMForXVector",Ka]]]),cd=new Map([["unispeech-sat",["UniSpeechSatForAudioFrameClassification",Va]],["wavlm",["WavLMForAudioFrameClassification",Za]],["wav2vec2",["Wav2Vec2ForAudioFrameClassification",Ta]],["pyannote",["PyAnnoteForAudioFrameClassification",Ca]]]),dd=new Map([["vitmatte",["VitMatteForImageMatting",kn]]]),ud=new Map([["patchtst",["PatchTSTForPrediction",_c]],["patchtsmixer",["PatchTSMixerForPrediction",hc]]]),_d=new Map([["swin2sr",["Swin2SRForImageSuperResolution",vi]]]),pd=new Map([["dpt",["DPTForDepthEstimation",Fi]],["depth_anything",["DepthAnythingForDepthEstimation",Si]],["glpn",["GLPNForDepthEstimation",Wi]],["sapiens",["SapiensForDepthEstimation",Li]],["depth_pro",["DepthProForDepthEstimation",ji]],["metric3d",["Metric3DForDepthEstimation",Oi]],["metric3dv2",["Metric3Dv2ForDepthEstimation",Vi]]]),md=new Map([["sapiens",["SapiensForNormalEstimation",Ii]]]),hd=new Map([["vitpose",["VitPoseForPoseEstimation",an]]]),gd=new Map([["clip",["CLIPVisionModelWithProjection",ir]],["siglip",["SiglipVisionModel",dr]],["jina_clip",["JinaCLIPVisionModel",gr]]]),fd=[[jc,x],[Dc,b],[Nc,v],[Oc,A],[Rc,x],[$c,x],[qc,k],[Vc,k],[Wc,v],[Uc,C],[Qc,x],[Xc,x],[Hc,y],[Jc,P],[Yc,E],[Zc,x],[sd,x],[od,x],[rd,x],[dd,x],[ud,x],[_d,x],[pd,x],[md,x],[hd,x],[ed,x],[td,x],[nd,T],[id,x],[ad,x],[Bc,k],[Gc,x],[ld,x],[cd,x],[gd,x]];for(const[e,t]of fd)for(const[s,r]of e.values())I.set(s,t),j.set(r,s),z.set(s,r);const wd=[["MusicgenForConditionalGeneration",Bl,F],["Phi3VForCausalLM",er,S],["CLIPTextModelWithProjection",or,x],["SiglipTextModel",cr,x],["JinaCLIPTextModel",hr,x],["ClapTextModelWithProjection",yl,x],["ClapAudioModelWithProjection",vl,x],["DacEncoderModel",Cc,x],["DacDecoderModel",Sc,x],["MimiEncoderModel",kc,x],["MimiDecoderModel",yc,x],["SnacEncoderModel",Lc,x],["SnacDecoderModel",Ic,x],["Gemma3nForConditionalGeneration",Hs,L]];for(const[e,t,s]of wd)I.set(e,s),j.set(t,e),z.set(e,t);const Md=new Map([["modnet",sd],["birefnet",sd],["isnet",sd],["ben",sd]]);for(const[e,t]of Md.entries())t.set(e,["PreTrainedModel",oe]),I.set(e,x),j.set(oe,e),z.set(e,oe);class xd extends zc{static MODEL_CLASS_MAPPINGS=fd.map((e=>e[0]));static BASE_IF_FAIL=!0}class bd extends zc{static MODEL_CLASS_MAPPINGS=[Rc]}class kd extends zc{static MODEL_CLASS_MAPPINGS=[$c]}class yd extends zc{static MODEL_CLASS_MAPPINGS=[qc]}class vd extends zc{static MODEL_CLASS_MAPPINGS=[Vc]}class Td extends zc{static MODEL_CLASS_MAPPINGS=[Bc]}class Pd extends zc{static MODEL_CLASS_MAPPINGS=[Gc]}class Fd extends zc{static MODEL_CLASS_MAPPINGS=[Wc]}class Cd extends zc{static MODEL_CLASS_MAPPINGS=[Qc]}class Sd extends zc{static MODEL_CLASS_MAPPINGS=[Xc]}class Ed extends zc{static MODEL_CLASS_MAPPINGS=[Hc]}class Ad extends zc{static MODEL_CLASS_MAPPINGS=[Zc]}class Ld extends zc{static MODEL_CLASS_MAPPINGS=[sd]}class Id extends zc{static MODEL_CLASS_MAPPINGS=[rd]}class zd extends zc{static MODEL_CLASS_MAPPINGS=[od]}class jd extends zc{static MODEL_CLASS_MAPPINGS=[ed]}class Dd extends zc{static MODEL_CLASS_MAPPINGS=[td]}class Od extends zc{static MODEL_CLASS_MAPPINGS=[nd]}class Nd extends zc{static MODEL_CLASS_MAPPINGS=[id]}class Vd extends zc{static MODEL_CLASS_MAPPINGS=[ad]}class Bd extends zc{static MODEL_CLASS_MAPPINGS=[ld]}class Gd extends zc{static MODEL_CLASS_MAPPINGS=[cd]}class Rd extends zc{static MODEL_CLASS_MAPPINGS=[Kc]}class $d extends zc{static MODEL_CLASS_MAPPINGS=[dd]}class qd extends zc{static MODEL_CLASS_MAPPINGS=[_d]}class Wd extends zc{static MODEL_CLASS_MAPPINGS=[pd]}class Ud extends zc{static MODEL_CLASS_MAPPINGS=[md]}class Qd extends zc{static MODEL_CLASS_MAPPINGS=[hd]}class Xd extends zc{static MODEL_CLASS_MAPPINGS=[gd]}class Hd extends zc{static MODEL_CLASS_MAPPINGS=[Jc]}class Jd extends zc{static MODEL_CLASS_MAPPINGS=[Yc]}class Yd extends ne{constructor({logits:e,past_key_values:t,encoder_outputs:s,decoder_attentions:r=null,cross_attentions:o=null}){super(),this.logits=e,this.past_key_values=t,this.encoder_outputs=s,this.decoder_attentions=r,this.cross_attentions=o}}class Kd extends ne{constructor({logits:e,...t}){super(),this.logits=e;const s=Object.values(t);s.length>0&&(this.attentions=s)}}class Zd extends ne{constructor({logits:e,embeddings:t}){super(),this.logits=e,this.embeddings=t}}class eu extends ne{constructor({logits:e}){super(),this.logits=e}}class tu extends ne{constructor({logits:e}){super(),this.logits=e}}class su extends ne{constructor({start_logits:e,end_logits:t}){super(),this.start_logits=e,this.end_logits=t}}class ru extends ne{constructor({logits:e}){super(),this.logits=e}}class ou extends ne{constructor({logits:e,past_key_values:t}){super(),this.logits=e,this.past_key_values=t}}class nu extends ne{constructor({alphas:e}){super(),this.alphas=e}}class iu extends ne{constructor({waveform:e,spectrogram:t}){super(),this.waveform=e,this.spectrogram=t}}},"./src/models/audio_spectrogram_transformer/feature_extraction_audio_spectrogram_transformer.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{ASTFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/audio.js"));class n extends r.FeatureExtractor{constructor(e){super(e);const t=this.config.sampling_rate,s=(0,o.mel_filter_bank)(257,this.config.num_mel_bins,20,Math.floor(t/2),t,null,"kaldi",!0);this.mel_filters=s,this.window=(0,o.window_function)(400,"hann",{periodic:!1}),this.mean=this.config.mean,this.std=this.config.std}async _extract_fbank_features(e,t){return(0,o.spectrogram)(e,this.window,400,160,{fft_length:512,power:2,center:!1,preemphasis:.97,mel_filters:this.mel_filters,log_mel:"log",mel_floor:1.192092955078125e-7,remove_dc_offset:!0,max_num_frames:t,transpose:!0})}async _call(e){(0,r.validate_audio_inputs)(e,"ASTFeatureExtractor");const t=await this._extract_fbank_features(e,this.config.max_length);if(this.config.do_normalize){const e=2*this.std,s=t.data;for(let t=0;t<s.length;++t)s[t]=(s[t]-this.mean)/e}return{input_values:t.unsqueeze_(0)}}}},"./src/models/auto/feature_extraction_auto.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{AutoFeatureExtractor:()=>i});var r=s("./src/utils/constants.js"),o=s("./src/utils/hub.js"),n=(s("./src/base/feature_extraction_utils.js"),s("./src/models/feature_extractors.js"));class i{static async from_pretrained(e,t={}){const s=await(0,o.getModelJSON)(e,r.FEATURE_EXTRACTOR_NAME,!0,t),i=s.feature_extractor_type,a=n[i];if(!a)throw new Error(`Unknown feature_extractor_type: '${i}'. Please report this at ${r.GITHUB_ISSUE_URL}.`);return new a(s)}}},"./src/models/auto/image_processing_auto.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{AutoImageProcessor:()=>a});var r=s("./src/utils/constants.js"),o=s("./src/utils/hub.js"),n=s("./src/base/image_processors_utils.js"),i=s("./src/models/image_processors.js");class a{static async from_pretrained(e,t={}){const s=await(0,o.getModelJSON)(e,r.IMAGE_PROCESSOR_NAME,!0,t),a=s.image_processor_type??s.feature_extractor_type;let l=i[a?.replace(/Fast$/,"")];return l||(void 0!==a&&console.warn(`Image processor type '${a}' not found, assuming base ImageProcessor. Please report this at ${r.GITHUB_ISSUE_URL}.`),l=n.ImageProcessor),new l(s)}}},"./src/models/auto/processing_auto.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{AutoProcessor:()=>c});var r=s("./src/utils/constants.js"),o=s("./src/utils/hub.js"),n=s("./src/base/processing_utils.js"),i=s("./src/models/processors.js"),a=s("./src/models/image_processors.js"),l=s("./src/models/feature_extractors.js");class c{static async from_pretrained(e,t={}){const s=await(0,o.getModelJSON)(e,r.IMAGE_PROCESSOR_NAME,!0,t),{image_processor_type:c,feature_extractor_type:d,processor_class:u}=s;if(u&&i[u])return i[u].from_pretrained(e,t);if(!c&&!d)throw new Error("No `image_processor_type` or `feature_extractor_type` found in the config.");const _={};if(c){const e=a[c.replace(/Fast$/,"")];if(!e)throw new Error(`Unknown image_processor_type: '${c}'.`);_.image_processor=new e(s)}if(d){const e=a[d];if(e)_.image_processor=new e(s);else{const e=l[d];if(!e)throw new Error(`Unknown feature_extractor_type: '${d}'.`);_.feature_extractor=new e(s)}}return new n.Processor({},_,null)}}},"./src/models/beit/image_processing_beit.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{BeitFeatureExtractor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/bit/image_processing_bit.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{BitImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/chinese_clip/image_processing_chinese_clip.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{ChineseCLIPFeatureExtractor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/clap/feature_extraction_clap.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{ClapFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/audio.js"));class n extends r.FeatureExtractor{constructor(e){super(e),this.mel_filters=(0,o.mel_filter_bank)(this.config.nb_frequency_bins,this.config.feature_size,this.config.frequency_min,this.config.frequency_max,this.config.sampling_rate,null,"htk"),this.mel_filters_slaney=(0,o.mel_filter_bank)(this.config.nb_frequency_bins,this.config.feature_size,this.config.frequency_min,this.config.frequency_max,this.config.sampling_rate,"slaney","slaney"),this.window=(0,o.window_function)(this.config.fft_window_size,"hann")}async _get_input_mel(e,t,s,r){let o,n=!1;const i=e.length-t;if(i>0){if("rand_trunc"!==s)throw new Error(`Truncation strategy "${s}" not implemented`);{n=!0;const s=Math.floor(Math.random()*(i+1));e=e.subarray(s,s+t),o=await this._extract_fbank_features(e,this.mel_filters_slaney,this.config.nb_max_samples)}}else{if(i<0){let s=new Float64Array(t);if(s.set(e),"repeat"===r)for(let r=e.length;r<t;r+=e.length)s.set(e.subarray(0,Math.min(e.length,t-r)),r);else if("repeatpad"===r)for(let t=e.length;t<-i;t+=e.length)s.set(e,t);e=s}if("fusion"===s)throw new Error(`Truncation strategy "${s}" not implemented`);o=await this._extract_fbank_features(e,this.mel_filters_slaney,this.config.nb_max_samples)}return o.unsqueeze_(0)}async _extract_fbank_features(e,t,s=null){return(0,o.spectrogram)(e,this.window,this.config.fft_window_size,this.config.hop_length,{power:2,mel_filters:t,log_mel:"dB",max_num_frames:s,do_pad:!1,transpose:!0})}async _call(e,{max_length:t=null}={}){(0,r.validate_audio_inputs)(e,"ClapFeatureExtractor");return{input_features:(await this._get_input_mel(e,t??this.config.nb_max_samples,this.config.truncation,this.config.padding)).unsqueeze_(0)}}}},"./src/models/clip/image_processing_clip.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{CLIPFeatureExtractor:()=>n,CLIPImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/convnext/image_processing_convnext.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{ConvNextFeatureExtractor:()=>n,ConvNextImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{constructor(e){super(e),this.crop_pct=this.config.crop_pct??.875}async resize(e){const t=this.size?.shortest_edge;if(void 0===t)throw new Error("Size dictionary must contain 'shortest_edge' key.");if(t<384){const s=Math.floor(t/this.crop_pct),[r,o]=this.get_resize_output_image_size(e,{shortest_edge:s});e=await e.resize(r,o,{resample:this.resample}),e=await e.center_crop(t,t)}else e=await e.resize(t,t,{resample:this.resample});return e}}class n extends o{}},"./src/models/dac/feature_extraction_dac.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{DacFeatureExtractor:()=>o});var r=s("./src/models/encodec/feature_extraction_encodec.js");class o extends r.EncodecFeatureExtractor{}},"./src/models/deit/image_processing_deit.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{DeiTFeatureExtractor:()=>n,DeiTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/detr/image_processing_detr.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{DetrFeatureExtractor:()=>i,DetrImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{async _call(e){const t=await super._call(e),s=[t.pixel_values.dims[0],64,64],r=(0,o.full)(s,1n);return{...t,pixel_mask:r}}post_process_object_detection(...e){return(0,r.post_process_object_detection)(...e)}post_process_panoptic_segmentation(...e){return(0,r.post_process_panoptic_segmentation)(...e)}post_process_instance_segmentation(...e){return(0,r.post_process_instance_segmentation)(...e)}}class i extends n{}},"./src/models/donut/image_processing_donut.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{DonutFeatureExtractor:()=>n,DonutImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{pad_image(e,t,s,r={}){const[o,n,i]=t;let a=this.image_mean;Array.isArray(this.image_mean)||(a=new Array(i).fill(a));let l=this.image_std;Array.isArray(l)||(l=new Array(i).fill(a));const c=a.map(((e,t)=>-e/l[t]));return super.pad_image(e,t,s,{center:!0,constant_values:c,...r})}}class n extends o{}},"./src/models/dpt/image_processing_dpt.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{DPTFeatureExtractor:()=>n,DPTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/efficientnet/image_processing_efficientnet.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{EfficientNetImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{constructor(e){super(e),this.include_top=this.config.include_top??!0,this.include_top&&(this.image_std=this.image_std.map((e=>e*e)))}}},"./src/models/encodec/feature_extraction_encodec.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{EncodecFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js");class n extends r.FeatureExtractor{async _call(e){(0,r.validate_audio_inputs)(e,"EncodecFeatureExtractor"),e instanceof Float64Array&&(e=new Float32Array(e));const t=this.config.feature_size;if(e.length%t!=0)throw new Error(`The length of the audio data must be a multiple of the number of channels (${t}).`);const s=[1,t,e.length/t];return{input_values:new o.Tensor("float32",e,s)}}}},"./src/models/feature_extractors.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{ASTFeatureExtractor:()=>r.ASTFeatureExtractor,ClapFeatureExtractor:()=>n.ClapFeatureExtractor,DacFeatureExtractor:()=>i.DacFeatureExtractor,EncodecFeatureExtractor:()=>o.EncodecFeatureExtractor,Gemma3nAudioFeatureExtractor:()=>a.Gemma3nAudioFeatureExtractor,ImageFeatureExtractor:()=>g.ImageProcessor,MoonshineFeatureExtractor:()=>l.MoonshineFeatureExtractor,PyAnnoteFeatureExtractor:()=>c.PyAnnoteFeatureExtractor,SeamlessM4TFeatureExtractor:()=>d.SeamlessM4TFeatureExtractor,SnacFeatureExtractor:()=>u.SnacFeatureExtractor,SpeechT5FeatureExtractor:()=>_.SpeechT5FeatureExtractor,Wav2Vec2FeatureExtractor:()=>p.Wav2Vec2FeatureExtractor,WeSpeakerFeatureExtractor:()=>m.WeSpeakerFeatureExtractor,WhisperFeatureExtractor:()=>h.WhisperFeatureExtractor});var r=s("./src/models/audio_spectrogram_transformer/feature_extraction_audio_spectrogram_transformer.js"),o=s("./src/models/encodec/feature_extraction_encodec.js"),n=s("./src/models/clap/feature_extraction_clap.js"),i=s("./src/models/dac/feature_extraction_dac.js"),a=s("./src/models/gemma3n/feature_extraction_gemma3n.js"),l=s("./src/models/moonshine/feature_extraction_moonshine.js"),c=s("./src/models/pyannote/feature_extraction_pyannote.js"),d=s("./src/models/seamless_m4t/feature_extraction_seamless_m4t.js"),u=s("./src/models/snac/feature_extraction_snac.js"),_=s("./src/models/speecht5/feature_extraction_speecht5.js"),p=s("./src/models/wav2vec2/feature_extraction_wav2vec2.js"),m=s("./src/models/wespeaker/feature_extraction_wespeaker.js"),h=s("./src/models/whisper/feature_extraction_whisper.js"),g=s("./src/base/image_processors_utils.js")},"./src/models/florence2/processing_florence2.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Florence2Processor:()=>i});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");class i extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;constructor(e,t,s){super(e,t,s);const{tasks_answer_post_processing_type:r,task_prompts_without_inputs:o,task_prompts_with_input:n}=this.image_processor.config;this.tasks_answer_post_processing_type=new Map(Object.entries(r??{})),this.task_prompts_without_inputs=new Map(Object.entries(o??{})),this.task_prompts_with_input=new Map(Object.entries(n??{})),this.regexes={quad_boxes:/(.+?)<loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)>/gm,bboxes:/([^<]+)?<loc_(\d+)><loc_(\d+)><loc_(\d+)><loc_(\d+)>/gm},this.size_per_bin=1e3}construct_prompts(e){"string"==typeof e&&(e=[e]);const t=[];for(const s of e)if(this.task_prompts_without_inputs.has(s))t.push(this.task_prompts_without_inputs.get(s));else{for(const[e,r]of this.task_prompts_with_input)if(s.includes(e)){t.push(r.replaceAll("{input}",s).replaceAll(e,""));break}t.length!==e.length&&t.push(s)}return t}post_process_generation(e,t,s){const r=this.tasks_answer_post_processing_type.get(t)??"pure_text";let o;switch(e=e.replaceAll("<s>","").replaceAll("</s>",""),r){case"pure_text":o=e;break;case"description_with_bboxes":case"bboxes":case"phrase_grounding":case"ocr":const n="ocr"===r?"quad_boxes":"bboxes",i=e.matchAll(this.regexes[n]),a=[],l=[];for(const[e,t,...r]of i)a.push(t?t.trim():a.at(-1)??""),l.push(r.map(((e,t)=>(Number(e)+.5)/this.size_per_bin*s[t%2])));o={labels:a,[n]:l};break;default:throw new Error(`Task "${t}" (of type "${r}") not yet implemented.`)}return{[t]:o}}async _call(e,t=null,s={}){if(!e&&!t)throw new Error("Either text or images must be provided");return{...await this.image_processor(e,s),...t?this.tokenizer(this.construct_prompts(t),s):{}}}}},"./src/models/gemma3n/feature_extraction_gemma3n.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Gemma3nAudioFeatureExtractor:()=>i});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/audio.js");class i extends r.FeatureExtractor{constructor(e){super(e);const{fft_length:t,feature_size:s,min_frequency:r,max_frequency:o,sampling_rate:i,frame_length:a}=this.config,l=(0,n.mel_filter_bank)(Math.floor(1+t/2),s,r,o,i,null,"htk",!1);this.mel_filters=l,this.window=(0,n.window_function)(a,"hann")}async _extract_fbank_features(e,t){return(0,n.spectrogram)(e,this.window,this.config.frame_length,this.config.hop_length,{fft_length:this.config.fft_length,center:!1,onesided:!0,preemphasis:this.config.preemphasis,preemphasis_htk_flavor:this.config.preemphasis_htk_flavor,mel_filters:this.mel_filters,log_mel:"log",mel_floor:this.config.mel_floor,remove_dc_offset:!1,transpose:!0})}async _call(e,{max_length:t=48e4,truncation:s=!0,padding:n=!0,pad_to_multiple_of:i=128}={}){if((0,r.validate_audio_inputs)(e,"Gemma3nAudioFeatureExtractor"),s&&e.length>t&&(e=e.slice(0,t)),n&&e.length%i!=0){const t=i-e.length%i,s=new Float64Array(e.length+t);s.set(e),0!==this.config.padding_value&&s.fill(this.config.padding_value,e.length),e=s}const a=await this._extract_fbank_features(e,this.config.max_length),l=(0,o.full)([1,a.dims[0]],!0);return{input_features:a.unsqueeze_(0),input_features_mask:l}}}},"./src/models/gemma3n/processing_gemma3n.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Gemma3nProcessor:()=>a});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/models/auto/feature_extraction_auto.js"),i=s("./src/tokenizers.js");s("./src/utils/image.js"),s("./src/utils/audio.js");class a extends r.Processor{static image_processor_class=o.AutoImageProcessor;static feature_extractor_class=n.AutoFeatureExtractor;static tokenizer_class=i.AutoTokenizer;static uses_processor_config=!0;static uses_chat_template_file=!0;constructor(e,t,s){super(e,t,s),this.audio_seq_length=this.config.audio_seq_length,this.image_seq_length=this.config.image_seq_length;const{audio_token_id:r,boa_token:o,audio_token:n,eoa_token:i,image_token_id:a,boi_token:l,image_token:c,eoi_token:d}=this.tokenizer.config;this.audio_token_id=r,this.boa_token=o,this.audio_token=n;const u=n.repeat(this.audio_seq_length);this.full_audio_sequence=`\n\n${o}${u}${i}\n\n`,this.image_token_id=a,this.boi_token=l,this.image_token=c;const _=c.repeat(this.image_seq_length);this.full_image_sequence=`\n\n${l}${_}${d}\n\n`}async _call(e,t=null,s=null,r={}){let o,n;return"string"==typeof e&&(e=[e]),s&&(o=await this.feature_extractor(s,r),e=e.map((e=>e.replaceAll(this.audio_token,this.full_audio_sequence)))),t&&(n=await this.image_processor(t,r),e=e.map((e=>e.replaceAll(this.image_token,this.full_image_sequence)))),{...this.tokenizer(e,r),...n,...o}}}},"./src/models/glpn/image_processing_glpn.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{GLPNFeatureExtractor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/grounding_dino/image_processing_grounding_dino.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{GroundingDinoImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{async _call(e){const t=await super._call(e),s=t.pixel_values.dims,r=(0,o.ones)([s[0],s[2],s[3]]);return{...t,pixel_mask:r}}}},"./src/models/grounding_dino/processing_grounding_dino.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{GroundingDinoProcessor:()=>l});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js"),i=s("./src/base/image_processors_utils.js");function a(e,t){const s=e.dims.at(-1)-1,r=e.tolist();r.fill(!1,0,1),r.fill(!1,s);const o=t.tolist();return r.map(((e,t)=>e?t:null)).filter((e=>null!==e)).map((e=>o[e]))}class l extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;async _call(e,t,s={}){const r=e?await this.image_processor(e,s):{};return{...t?this.tokenizer(t,s):{},...r}}post_process_grounded_object_detection(e,t,{box_threshold:s=.25,text_threshold:r=.25,target_sizes:o=null}={}){const{logits:n,pred_boxes:l}=e,c=n.dims[0];if(null!==o&&o.length!==c)throw Error("Make sure that you pass in as many target sizes as the batch dimension of the logits");const d=n.dims.at(1),u=n.sigmoid(),_=u.max(-1).tolist(),p=l.tolist().map((e=>e.map((e=>(0,i.center_to_corners_format)(e))))),m=[];for(let e=0;e<c;++e){const n=null!==o?o[e]:null;null!==n&&(p[e]=p[e].map((e=>e.map(((e,t)=>e*n[(t+1)%2])))));const i=_[e],l=[],c=[],h=[];for(let o=0;o<d;++o){const n=i[o];if(n<=s)continue;const d=p[e][o],_=u[e][o];l.push(n),h.push(d);const m=a(_.gt(r),t[e]);c.push(m)}m.push({scores:l,boxes:h,labels:this.batch_decode(c)})}return m}}},"./src/models/idefics3/image_processing_idefics3.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Idefics3ImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{constructor(e){super(e),this.do_image_splitting=e.do_image_splitting??!0,this.max_image_size=e.max_image_size}get_resize_for_vision_encoder(e,t){let[s,r]=e.dims.slice(-2);const o=r/s;return r>=s?(r=Math.ceil(r/t)*t,s=Math.floor(r/o),s=Math.ceil(s/t)*t):(s=Math.ceil(s/t)*t,r=Math.floor(s*o),r=Math.ceil(r/t)*t),{height:s,width:r}}async _call(e,{do_image_splitting:t=null,return_row_col_info:s=!1}={}){let r;if(Array.isArray(e)){if(0===e.length||!e[0])throw new Error("No images provided.");r=Array.isArray(e[0])?e:[e]}else r=[[e]];let n=[],i=[],a=[];const l=[],c=[];for(const e of r){let s=await Promise.all(e.map((e=>this.preprocess(e))));l.push(...s.map((e=>e.original_size))),c.push(...s.map((e=>e.reshaped_input_size))),s.forEach((e=>e.pixel_values.unsqueeze_(0)));const{longest_edge:r}=this.max_image_size;let d;if(t??this.do_image_splitting){let e=new Array(s.length),t=new Array(s.length);d=await Promise.all(s.map((async(s,n)=>{const i=this.get_resize_for_vision_encoder(s.pixel_values,r),a=await(0,o.interpolate_4d)(s.pixel_values,{size:[i.height,i.width]}),{frames:l,num_splits_h:c,num_splits_w:d}=await this.split_image(a,this.max_image_size);return e[n]=c,t[n]=d,(0,o.cat)(l,0)}))),i.push(e),a.push(t)}else{const e=[r,r];d=await Promise.all(s.map((t=>(0,o.interpolate_4d)(t.pixel_values,{size:e})))),i.push(new Array(s.length).fill(0)),a.push(new Array(s.length).fill(0))}n.push((0,o.cat)(d,0))}const d=n.length,[u,_,p,m]=n[0].dims;let h,g;if(1===d)h=n[0].unsqueeze_(0),g=(0,o.full)([d,u,p,m],!0);else{const e=Math.max(...n.map((e=>e.dims.at(0))));g=(0,o.full)([d,e,p,m],!0);const t=g.data,s=e*p*m;for(let r=0;r<d;++r){const i=n[r].dims[0];if(i<e){n[r]=(0,o.cat)([n[r],(0,o.full)([e-i,_,p,m],0)],0);const a=r*s+i*p*m,l=(r+1)*s;t.fill(!1,a,l)}}h=(0,o.stack)(n,0)}return{pixel_values:h,pixel_attention_mask:g,original_sizes:l,reshaped_input_sizes:c,...s?{rows:i,cols:a}:{}}}async split_image(e,{longest_edge:t}){const s=t,r=t,n=[],[i,a]=e.dims.slice(-2);let l=0,c=0;if(i>s||a>r){l=Math.ceil(i/s),c=Math.ceil(a/r);const t=Math.ceil(i/l),d=Math.ceil(a/c);for(let s=0;s<l;++s)for(let r=0;r<c;++r){let u,_,p,m;s===l-1?(_=i-t,m=i):(_=s*t,m=(s+1)*t),r===c-1?(u=a-d,p=a):(u=r*d,p=(r+1)*d);const h=[_,u],g=[m,p],f=await(0,o.slice)(e,h,g,[2,3]);n.push(f)}const u=s,_=r;i===u&&a===_||(e=await(0,o.interpolate_4d)(e,{size:[u,_]}))}return n.push(e),{frames:n,num_splits_h:l,num_splits_w:c}}}},"./src/models/idefics3/processing_idefics3.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Idefics3Processor:()=>l});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js"),i=(s("./src/utils/image.js"),s("./src/utils/core.js"));function a(e,t,s,r,o,n){return 0===e&&0===t?function(e,t,s,r){return`${t}${r}`+s.repeat(e)+`${t}`}(s,r,o,n):function(e,t,s,r,o,n){let i="";for(let n=0;n<t;++n){for(let t=0;t<s;++t)i+=r+`<row_${n+1}_col_${t+1}>`+o.repeat(e);i+="\n"}return i+=`\n${r}${n}`+o.repeat(e)+`${r}`,i}(s,e,t,r,o,n)}class l extends r.Processor{static image_processor_class=o.AutoImageProcessor;static tokenizer_class=n.AutoTokenizer;static uses_processor_config=!0;fake_image_token="<fake_token_around_image>";image_token="<image>";global_img_token="<global-img>";async _call(e,t=null,s={}){let r;s.return_row_col_info??=!0,t&&(r=await this.image_processor(t,s)),Array.isArray(e)||(e=[e]);const o=r.rows??[new Array(e.length).fill(0)],n=r.cols??[new Array(e.length).fill(0)],l=this.config.image_seq_len,c=[],d=[];for(let t=0;t<e.length;++t){const s=e[t],r=o[t],u=n[t];c.push((0,i.count)(s,this.image_token));const _=r.map(((e,t)=>a(e,u[t],l,this.fake_image_token,this.image_token,this.global_img_token))),p=s.split(this.image_token);if(0===p.length)throw new Error("The image token should be present in the text.");let m=p[0];for(let e=0;e<_.length;++e)m+=_[e]+p[e+1];d.push(m)}return{...this.tokenizer(d),...r}}}},"./src/models/image_processors.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{BeitFeatureExtractor:()=>r.BeitFeatureExtractor,BitImageProcessor:()=>o.BitImageProcessor,CLIPFeatureExtractor:()=>i.CLIPFeatureExtractor,CLIPImageProcessor:()=>i.CLIPImageProcessor,ChineseCLIPFeatureExtractor:()=>n.ChineseCLIPFeatureExtractor,ConvNextFeatureExtractor:()=>a.ConvNextFeatureExtractor,ConvNextImageProcessor:()=>a.ConvNextImageProcessor,DPTFeatureExtractor:()=>u.DPTFeatureExtractor,DPTImageProcessor:()=>u.DPTImageProcessor,DeiTFeatureExtractor:()=>l.DeiTFeatureExtractor,DeiTImageProcessor:()=>l.DeiTImageProcessor,DetrFeatureExtractor:()=>c.DetrFeatureExtractor,DetrImageProcessor:()=>c.DetrImageProcessor,DonutFeatureExtractor:()=>d.DonutFeatureExtractor,DonutImageProcessor:()=>d.DonutImageProcessor,EfficientNetImageProcessor:()=>_.EfficientNetImageProcessor,GLPNFeatureExtractor:()=>p.GLPNFeatureExtractor,GroundingDinoImageProcessor:()=>m.GroundingDinoImageProcessor,Idefics3ImageProcessor:()=>h.Idefics3ImageProcessor,JinaCLIPImageProcessor:()=>f.JinaCLIPImageProcessor,LlavaOnevisionImageProcessor:()=>w.LlavaOnevisionImageProcessor,Mask2FormerImageProcessor:()=>M.Mask2FormerImageProcessor,MaskFormerFeatureExtractor:()=>x.MaskFormerFeatureExtractor,MaskFormerImageProcessor:()=>x.MaskFormerImageProcessor,MobileNetV1FeatureExtractor:()=>b.MobileNetV1FeatureExtractor,MobileNetV1ImageProcessor:()=>b.MobileNetV1ImageProcessor,MobileNetV2FeatureExtractor:()=>k.MobileNetV2FeatureExtractor,MobileNetV2ImageProcessor:()=>k.MobileNetV2ImageProcessor,MobileNetV3FeatureExtractor:()=>y.MobileNetV3FeatureExtractor,MobileNetV3ImageProcessor:()=>y.MobileNetV3ImageProcessor,MobileNetV4FeatureExtractor:()=>v.MobileNetV4FeatureExtractor,MobileNetV4ImageProcessor:()=>v.MobileNetV4ImageProcessor,MobileViTFeatureExtractor:()=>T.MobileViTFeatureExtractor,MobileViTImageProcessor:()=>T.MobileViTImageProcessor,NougatImageProcessor:()=>P.NougatImageProcessor,OwlViTFeatureExtractor:()=>C.OwlViTFeatureExtractor,OwlViTImageProcessor:()=>C.OwlViTImageProcessor,Owlv2ImageProcessor:()=>F.Owlv2ImageProcessor,Phi3VImageProcessor:()=>S.Phi3VImageProcessor,PvtImageProcessor:()=>E.PvtImageProcessor,Qwen2VLImageProcessor:()=>A.Qwen2VLImageProcessor,RTDetrImageProcessor:()=>L.RTDetrImageProcessor,SamImageProcessor:()=>I.SamImageProcessor,SegformerFeatureExtractor:()=>z.SegformerFeatureExtractor,SegformerImageProcessor:()=>z.SegformerImageProcessor,SiglipImageProcessor:()=>j.SiglipImageProcessor,SmolVLMImageProcessor:()=>D.SmolVLMImageProcessor,Swin2SRImageProcessor:()=>O.Swin2SRImageProcessor,VLMImageProcessor:()=>g.VLMImageProcessor,ViTFeatureExtractor:()=>N.ViTFeatureExtractor,ViTImageProcessor:()=>N.ViTImageProcessor,VitMatteImageProcessor:()=>V.VitMatteImageProcessor,VitPoseImageProcessor:()=>B.VitPoseImageProcessor,YolosFeatureExtractor:()=>G.YolosFeatureExtractor,YolosImageProcessor:()=>G.YolosImageProcessor});var r=s("./src/models/beit/image_processing_beit.js"),o=s("./src/models/bit/image_processing_bit.js"),n=s("./src/models/chinese_clip/image_processing_chinese_clip.js"),i=s("./src/models/clip/image_processing_clip.js"),a=s("./src/models/convnext/image_processing_convnext.js"),l=s("./src/models/deit/image_processing_deit.js"),c=s("./src/models/detr/image_processing_detr.js"),d=s("./src/models/donut/image_processing_donut.js"),u=s("./src/models/dpt/image_processing_dpt.js"),_=s("./src/models/efficientnet/image_processing_efficientnet.js"),p=s("./src/models/glpn/image_processing_glpn.js"),m=s("./src/models/grounding_dino/image_processing_grounding_dino.js"),h=s("./src/models/idefics3/image_processing_idefics3.js"),g=s("./src/models/janus/image_processing_janus.js"),f=s("./src/models/jina_clip/image_processing_jina_clip.js"),w=s("./src/models/llava_onevision/image_processing_llava_onevision.js"),M=s("./src/models/mask2former/image_processing_mask2former.js"),x=s("./src/models/maskformer/image_processing_maskformer.js"),b=s("./src/models/mobilenet_v1/image_processing_mobilenet_v1.js"),k=s("./src/models/mobilenet_v2/image_processing_mobilenet_v2.js"),y=s("./src/models/mobilenet_v3/image_processing_mobilenet_v3.js"),v=s("./src/models/mobilenet_v4/image_processing_mobilenet_v4.js"),T=s("./src/models/mobilevit/image_processing_mobilevit.js"),P=s("./src/models/nougat/image_processing_nougat.js"),F=s("./src/models/owlv2/image_processing_owlv2.js"),C=s("./src/models/owlvit/image_processing_owlvit.js"),S=s("./src/models/phi3_v/image_processing_phi3_v.js"),E=s("./src/models/pvt/image_processing_pvt.js"),A=s("./src/models/qwen2_vl/image_processing_qwen2_vl.js"),L=s("./src/models/rt_detr/image_processing_rt_detr.js"),I=s("./src/models/sam/image_processing_sam.js"),z=s("./src/models/segformer/image_processing_segformer.js"),j=s("./src/models/siglip/image_processing_siglip.js"),D=s("./src/models/smolvlm/image_processing_smolvlm.js"),O=s("./src/models/swin2sr/image_processing_swin2sr.js"),N=s("./src/models/vit/image_processing_vit.js"),V=s("./src/models/vitmatte/image_processing_vitmatte.js"),B=s("./src/models/vitpose/image_processing_vitpose.js"),G=s("./src/models/yolos/image_processing_yolos.js")},"./src/models/janus/image_processing_janus.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{VLMImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{constructor(e){super({do_pad:!0,pad_size:{width:e.image_size,height:e.image_size},...e}),this.constant_values=this.config.background_color.map((e=>e*this.rescale_factor))}pad_image(e,t,s,r){return super.pad_image(e,t,s,{constant_values:this.constant_values,center:!0,...r})}}},"./src/models/janus/processing_janus.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{VLChatProcessor:()=>c});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js"),i=s("./src/utils/core.js"),a=s("./src/utils/tensor.js"),l=s("./src/utils/image.js");class c extends r.Processor{static image_processor_class=o.AutoImageProcessor;static tokenizer_class=n.AutoTokenizer;static uses_processor_config=!0;constructor(e,t,s){super(e,t,s),this.image_tag=this.config.image_tag,this.image_start_tag=this.config.image_start_tag,this.image_end_tag=this.config.image_end_tag,this.num_image_tokens=this.config.num_image_tokens}async _call(e,{images:t=null,chat_template:s="default"}={}){t?Array.isArray(t)||(t=[t]):t=await Promise.all(e.filter((e=>e.images)).flatMap((e=>e.images)).map((e=>l.RawImage.read(e))));const r=this.tokenizer,o=e=>r.encode(e,{add_special_tokens:!1}),n=r.apply_chat_template(e,{tokenize:!1,add_generation_prompt:!0,chat_template:s}).split(this.image_tag),c=n.length-1;if(t.length!==c)throw new Error(`Number of images provided (${t.length}) does not match number of "${this.image_tag}" image tags (${c})`);const[d,u,_]=r.model.convert_tokens_to_ids([this.image_tag,this.image_start_tag,this.image_end_tag]);let p=o(n[0]),m=new Array(p.length).fill(!1);for(let e=1;e<n.length;++e){const t=new Array(this.num_image_tokens).fill(d),s=o(n[e]);p=(0,i.mergeArrays)(p,[u],t,[_],s);const r=new Array(this.num_image_tokens).fill(!0);m=(0,i.mergeArrays)(m,[!1],r,[!1],new Array(s.length).fill(!1))}const h=[1,p.length],g={input_ids:new a.Tensor("int64",p,h),attention_mask:new a.Tensor("int64",new Array(p.length).fill(1),h),images_seq_mask:new a.Tensor("bool",m,h),images_emb_mask:new a.Tensor("bool",new Array(c*this.num_image_tokens).fill(!0),[1,c,this.num_image_tokens])};if(t&&t.length>0){const e=await this.image_processor(t);return e.pixel_values.unsqueeze_(0),{...g,...e}}return g}}},"./src/models/jina_clip/image_processing_jina_clip.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{JinaCLIPImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{constructor(e){const{resize_mode:t,fill_color:s,interpolation:r,size:o,...n}=e;super({...n,size:"squash"===t?{width:o,height:o}:"shortest"===t?{shortest_edge:o}:{longest_edge:o},resample:"bicubic"===r?3:2,do_center_crop:!0,crop_size:o,do_normalize:!0})}}},"./src/models/jina_clip/processing_jina_clip.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{JinaCLIPProcessor:()=>i});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");class i extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;async _call(e=null,t=null,s={}){if(!e&&!t)throw new Error("Either text or images must be provided");return{...e?this.tokenizer(e,s):{},...t?await this.image_processor(t,s):{}}}}},"./src/models/llava/processing_llava.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{LlavaProcessor:()=>i});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");class i extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;static uses_processor_config=!0;async _call(e,t=null,s={}){const r=await this.image_processor(e,s);if(t){const[e,s]=r.pixel_values.dims.slice(-2),{image_token:o,patch_size:n,num_additional_image_tokens:i}=this.config,a=Math.floor(e/n)*Math.floor(s/n)+i;t=structuredClone(t),Array.isArray(t)||(t=[t]);for(let e=0;e<t.length;++e)t[e]=t[e].replace(o,o.repeat(a))}const o=t?this.tokenizer(t,s):{};return{...r,...o}}}},"./src/models/llava_onevision/image_processing_llava_onevision.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{LlavaOnevisionImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/mask2former/image_processing_mask2former.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Mask2FormerImageProcessor:()=>o});var r=s("./src/models/maskformer/image_processing_maskformer.js");class o extends r.MaskFormerImageProcessor{}},"./src/models/maskformer/image_processing_maskformer.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MaskFormerFeatureExtractor:()=>n,MaskFormerImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_panoptic_segmentation(...e){return(0,r.post_process_panoptic_segmentation)(...e)}post_process_instance_segmentation(...e){return(0,r.post_process_instance_segmentation)(...e)}}class n extends o{}},"./src/models/mgp_str/processing_mgp_str.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MgpstrProcessor:()=>l});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js"),i=s("./src/utils/maths.js");const a={char:["char_decode",1],bpe:["bpe_decode",2],wp:["wp_decode",102]};class l extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;get char_tokenizer(){return this.components.char_tokenizer}get bpe_tokenizer(){return this.components.bpe_tokenizer}get wp_tokenizer(){return this.components.wp_tokenizer}_decode_helper(e,t){if(!a.hasOwnProperty(t))throw new Error(`Format ${t} is not supported.`);const[s,r]=a[t],o=this[s].bind(this),[n,l]=e.dims,c=[],d=[],u=e.tolist();for(let e=0;e<n;++e){const t=u[e],s=[],o=[];for(let e=1;e<l;++e){const[n,a]=(0,i.max)((0,i.softmax)(t[e]));if(o.push(n),a==r)break;s.push(a)}const n=o.length>0?o.reduce(((e,t)=>e*t),1):0;d.push(s),c.push(n)}return[o(d),c]}char_decode(e){return this.char_tokenizer.batch_decode(e).map((e=>e.replaceAll(" ","")))}bpe_decode(e){return this.bpe_tokenizer.batch_decode(e)}wp_decode(e){return this.wp_tokenizer.batch_decode(e).map((e=>e.replaceAll(" ","")))}batch_decode([e,t,s]){const[r,o]=this._decode_helper(e,"char"),[n,a]=this._decode_helper(t,"bpe"),[l,c]=this._decode_helper(s,"wp"),d=[],u=[];for(let e=0;e<r.length;++e){const[t,s]=(0,i.max)([o[e],a[e],c[e]]);d.push([r[e],n[e],l[e]][s]),u.push(t)}return{generated_text:d,scores:u,char_preds:r,bpe_preds:n,wp_preds:l}}static async from_pretrained(...e){const t=await super.from_pretrained(...e),s=await n.AutoTokenizer.from_pretrained("Xenova/gpt2"),r=await n.AutoTokenizer.from_pretrained("Xenova/bert-base-uncased");return t.components={image_processor:t.image_processor,char_tokenizer:t.tokenizer,bpe_tokenizer:s,wp_tokenizer:r},t}async _call(e,t=null){const s=await this.image_processor(e);return t&&(s.labels=this.tokenizer(t).input_ids),s}}},"./src/models/mobilenet_v1/image_processing_mobilenet_v1.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MobileNetV1FeatureExtractor:()=>n,MobileNetV1ImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/mobilenet_v2/image_processing_mobilenet_v2.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MobileNetV2FeatureExtractor:()=>n,MobileNetV2ImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/mobilenet_v3/image_processing_mobilenet_v3.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MobileNetV3FeatureExtractor:()=>n,MobileNetV3ImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/mobilenet_v4/image_processing_mobilenet_v4.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MobileNetV4FeatureExtractor:()=>n,MobileNetV4ImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/mobilevit/image_processing_mobilevit.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MobileViTFeatureExtractor:()=>n,MobileViTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/moonshine/feature_extraction_moonshine.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MoonshineFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js");class n extends r.FeatureExtractor{async _call(e){(0,r.validate_audio_inputs)(e,"MoonshineFeatureExtractor"),e instanceof Float64Array&&(e=new Float32Array(e));const t=[1,e.length];return{input_values:new o.Tensor("float32",e,t)}}}},"./src/models/moonshine/processing_moonshine.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MoonshineProcessor:()=>i});var r=s("./src/models/auto/feature_extraction_auto.js"),o=s("./src/tokenizers.js"),n=s("./src/base/processing_utils.js");class i extends n.Processor{static tokenizer_class=o.AutoTokenizer;static feature_extractor_class=r.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/nougat/image_processing_nougat.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{NougatImageProcessor:()=>o});var r=s("./src/models/donut/image_processing_donut.js");class o extends r.DonutImageProcessor{}},"./src/models/owlv2/image_processing_owlv2.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Owlv2ImageProcessor:()=>o});var r=s("./src/models/owlvit/image_processing_owlvit.js");class o extends r.OwlViTImageProcessor{}},"./src/models/owlvit/image_processing_owlvit.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{OwlViTFeatureExtractor:()=>n,OwlViTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_object_detection(...e){return(0,r.post_process_object_detection)(...e)}}class n extends o{}},"./src/models/owlvit/processing_owlvit.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{OwlViTProcessor:()=>i});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");class i extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor}},"./src/models/paligemma/processing_paligemma.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{PaliGemmaProcessor:()=>a});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");const i="<image>";class a extends r.Processor{static tokenizer_class=n.AutoTokenizer;static image_processor_class=o.AutoImageProcessor;static uses_processor_config=!1;async _call(e,t=null,s={}){t||(console.warn("You are using PaliGemma without a text prefix. It will perform as a picture-captioning model."),t=""),Array.isArray(e)||(e=[e]),Array.isArray(t)||(t=[t]);const r=this.tokenizer.bos_token,o=this.image_processor.config.image_seq_length;let n;t.some((e=>e.includes(i)))?n=t.map((e=>{const t=e.replaceAll(i,i.repeat(o)),s=t.lastIndexOf(i),n=-1===s?0:s+7;return t.slice(0,n)+r+t.slice(n)+"\n"})):(console.warn("You are passing both `text` and `images` to `PaliGemmaProcessor`. The processor expects special image tokens in the text, as many tokens as there are images per each text. It is recommended to add `<image>` tokens in the very beginning of your text. For this call, we will infer how many images each text has and add special tokens."),n=t.map((t=>function(e,t,s,r,o){return`${r.repeat(s*o)}${t}${e}\n`}(t,r,o,i,e.length))));const a=this.tokenizer(n,s);return{...await this.image_processor(e,s),...a}}}},"./src/models/phi3_v/image_processing_phi3_v.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Phi3VImageProcessor:()=>d});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");const n=336,i=[2,3],{ceil:a,floor:l,sqrt:c}=Math;class d extends r.ImageProcessor{constructor(e){super({...e,do_normalize:!0,do_pad:!0,pad_size:"custom",do_convert_rgb:!0,do_resize:!0}),this._num_crops=e.num_crops}calc_num_image_tokens_from_image_size(e,t){const{num_img_tokens:s}=this.config;return l((l(t/n)*l(e/n)+1)*s+1+(l(t/n)+1)*c(s))}get_resize_output_image_size(e,t){const s=this._num_crops,[r,o]=e.size;let n=r/o,i=1;for(;i*Math.ceil(i/n)<=s;)i+=1;i-=1;const a=Math.floor(336*i);return[a,Math.floor(a/n)]}pad_image(e,t,s,r={}){const[o,i]=t,l=n*a(o/n),c=n*a(i/n),d=[1,1,1].map(((e,t)=>(e-this.image_mean[t])/this.image_std[t]));return super.pad_image(e,t,{width:c,height:l},{center:!0,constant_values:d,...r})}async _call(e,{num_crops:t=null}={}){if(this._num_crops=t??=this.config.num_crops,t<4||c(t)%1!=0)throw new Error("num_crops must be a square number >= 4");Array.isArray(e)||(e=[e]);const s=e.length,r=await Promise.all(e.map((e=>this.preprocess(e)))),d=r.map((e=>e.original_size)),u=r.map((e=>e.reshaped_input_size)),_=[];for(const{pixel_values:e}of r){e.unsqueeze_(0);const[s,r]=e.dims.slice(-2),a=await(0,o.interpolate_4d)(e,{size:[n,n],mode:"bicubic"});if(t>0){const d=[],u=c(t),p=l(r/u),m=l(s/u);for(let t=0;t<u;++t)for(let n=0;n<u;++n){let a,l,c,_;t===u-1?(l=s-m,_=s):(l=t*m,_=(t+1)*m),n===u-1?(a=r-p,c=r):(a=n*p,c=(n+1)*p);const h=[l,a],g=[_,c],f=await(0,o.slice)(e,h,g,i);d.push(f)}const h=await(0,o.interpolate_4d)((0,o.cat)(d,0),{size:[n,n],mode:"bicubic"});_.push((0,o.cat)([a,h],0))}else _.push(a)}const p=(0,o.stack)(_,0),m=u.map((e=>e.map((e=>n*a(e/n)))));return{pixel_values:p,original_sizes:d,reshaped_input_sizes:u,image_sizes:new o.Tensor("int64",m.flat(),[s,2]),num_img_tokens:m.map((([e,t])=>this.calc_num_image_tokens_from_image_size(t,e)))}}}},"./src/models/phi3_v/processing_phi3_v.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Phi3VProcessor:()=>l});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");s("./src/utils/image.js");const i="<|image|>",a=/<\|image_\d+\|>/g;class l extends r.Processor{static image_processor_class=o.AutoImageProcessor;static tokenizer_class=n.AutoTokenizer;async _call(e,t=null,{padding:s=!0,truncation:r=!0,num_crops:o=null}={}){let n,l;if(Array.isArray(e)||(e=[e]),t){l=await this.image_processor(t,{num_crops:o});const{num_img_tokens:c}=l,d=e.map(((e,t)=>e.split(a).join(i.repeat(c[t]))));n=this.tokenizer(d,{padding:s,truncation:r});const u=this.tokenizer.model.convert_tokens_to_ids([i])[0];n.input_ids.map_((e=>e==u?-e:e))}else n=this.tokenizer(e);return{...n,...l}}}},"./src/models/processors.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Florence2Processor:()=>r.Florence2Processor,Gemma3nProcessor:()=>o.Gemma3nProcessor,GroundingDinoProcessor:()=>n.GroundingDinoProcessor,Idefics3Processor:()=>i.Idefics3Processor,JinaCLIPProcessor:()=>l.JinaCLIPProcessor,LlavaProcessor:()=>c.LlavaProcessor,MgpstrProcessor:()=>d.MgpstrProcessor,MoonshineProcessor:()=>u.MoonshineProcessor,OwlViTProcessor:()=>_.OwlViTProcessor,PaliGemmaProcessor:()=>m.PaliGemmaProcessor,Phi3VProcessor:()=>p.Phi3VProcessor,PyAnnoteProcessor:()=>h.PyAnnoteProcessor,Qwen2VLProcessor:()=>g.Qwen2VLProcessor,SamProcessor:()=>f.SamProcessor,SmolVLMProcessor:()=>w.SmolVLMProcessor,SpeechT5Processor:()=>M.SpeechT5Processor,UltravoxProcessor:()=>x.UltravoxProcessor,VLChatProcessor:()=>a.VLChatProcessor,Wav2Vec2Processor:()=>b.Wav2Vec2Processor,Wav2Vec2ProcessorWithLM:()=>k.Wav2Vec2ProcessorWithLM,WhisperProcessor:()=>y.WhisperProcessor});var r=s("./src/models/florence2/processing_florence2.js"),o=s("./src/models/gemma3n/processing_gemma3n.js"),n=s("./src/models/grounding_dino/processing_grounding_dino.js"),i=s("./src/models/idefics3/processing_idefics3.js"),a=s("./src/models/janus/processing_janus.js"),l=s("./src/models/jina_clip/processing_jina_clip.js"),c=s("./src/models/llava/processing_llava.js"),d=s("./src/models/mgp_str/processing_mgp_str.js"),u=s("./src/models/moonshine/processing_moonshine.js"),_=s("./src/models/owlvit/processing_owlvit.js"),p=s("./src/models/phi3_v/processing_phi3_v.js"),m=s("./src/models/paligemma/processing_paligemma.js"),h=s("./src/models/pyannote/processing_pyannote.js"),g=s("./src/models/qwen2_vl/processing_qwen2_vl.js"),f=s("./src/models/sam/processing_sam.js"),w=s("./src/models/smolvlm/processing_smolvlm.js"),M=s("./src/models/speecht5/processing_speecht5.js"),x=s("./src/models/ultravox/processing_ultravox.js"),b=s("./src/models/wav2vec2/processing_wav2vec2.js"),k=s("./src/models/wav2vec2_with_lm/processing_wav2vec2_with_lm.js"),y=s("./src/models/whisper/processing_whisper.js")},"./src/models/pvt/image_processing_pvt.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{PvtImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/pyannote/feature_extraction_pyannote.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{PyAnnoteFeatureExtractor:()=>i});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/maths.js");class i extends r.FeatureExtractor{async _call(e){(0,r.validate_audio_inputs)(e,"PyAnnoteFeatureExtractor"),e instanceof Float64Array&&(e=new Float32Array(e));const t=[1,1,e.length];return{input_values:new o.Tensor("float32",e,t)}}samples_to_frames(e){return(e-this.config.offset)/this.config.step}post_process_speaker_diarization(e,t){const s=t/this.samples_to_frames(t)/this.config.sampling_rate,r=[];for(const t of e.tolist()){const e=[];let o=-1;for(let s=0;s<t.length;++s){const r=(0,n.softmax)(t[s]),[i,a]=(0,n.max)(r),[l,c]=[s,s+1];a!==o?(o=a,e.push({id:a,start:l,end:c,score:i})):(e.at(-1).end=c,e.at(-1).score+=i)}r.push(e.map((({id:e,start:t,end:r,score:o})=>({id:e,start:t*s,end:r*s,confidence:o/(r-t)}))))}return r}}},"./src/models/pyannote/processing_pyannote.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{PyAnnoteProcessor:()=>n});var r=s("./src/base/processing_utils.js"),o=s("./src/models/pyannote/feature_extraction_pyannote.js");class n extends r.Processor{static feature_extractor_class=o.PyAnnoteFeatureExtractor;async _call(e){return await this.feature_extractor(e)}post_process_speaker_diarization(...e){return this.feature_extractor.post_process_speaker_diarization(...e)}get sampling_rate(){return this.feature_extractor.config.sampling_rate}}},"./src/models/qwen2_vl/image_processing_qwen2_vl.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Qwen2VLImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{async _call(e,...t){const{pixel_values:s,original_sizes:r,reshaped_input_sizes:n}=await super._call(e,...t);let i=s;const{temporal_patch_size:a,merge_size:l,patch_size:c}=this.config;1===i.dims[0]&&(i=(0,o.cat)(Array.from({length:a},(()=>i)),0));const d=i.dims[0]/a,u=i.dims[1],_=Math.floor(i.dims[2]/c),p=Math.floor(i.dims[3]/c);return{pixel_values:i.view(d,a,u,Math.floor(_/l),l,c,Math.floor(p/l),l,c).permute(0,3,6,4,7,2,1,5,8).view(d*_*p,u*a*c*c),image_grid_thw:new o.Tensor("int64",[d,_,p],[1,3]),original_sizes:r,reshaped_input_sizes:n}}}},"./src/models/qwen2_vl/processing_qwen2_vl.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Qwen2VLProcessor:()=>i});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js"),n=s("./src/tokenizers.js");s("./src/utils/image.js");class i extends r.Processor{static image_processor_class=o.AutoImageProcessor;static tokenizer_class=n.AutoTokenizer;async _call(e,t=null,...s){let r,o;if(Array.isArray(e)||(e=[e]),t&&(r=await this.image_processor(t),o=r.image_grid_thw),o){let t=this.image_processor.config.merge_size**2,s=0;const r=o.tolist();e=e.map((e=>{for(;e.includes("<|image_pad|>");){const o=Number(r[s++].reduce(((e,t)=>e*t),1n));e=e.replace("<|image_pad|>","<|placeholder|>".repeat(Math.floor(o/t)))}return e.replaceAll("<|placeholder|>","<|image_pad|>")}))}return{...this.tokenizer(e),...r}}}},"./src/models/rt_detr/image_processing_rt_detr.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{RTDetrImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_object_detection(...e){return(0,r.post_process_object_detection)(...e)}}},"./src/models/sam/image_processing_sam.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SamImageProcessor:()=>i});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/core.js"),n=s("./src/utils/tensor.js");class i extends r.ImageProcessor{reshape_input_points(e,t,s,r=!1){e=structuredClone(e);let i=(0,o.calculateDimensions)(e);if(3===i.length)r||(i=[1,...i]),e=[e];else if(4!==i.length)throw Error("The input_points must be a 4D tensor of shape `batch_size`, `point_batch_size`, `nb_points_per_image`, `2`.");for(let r=0;r<e.length;++r){let o=t[r],n=s[r],i=[n[0]/o[0],n[1]/o[1]];for(let t=0;t<e[r].length;++t)for(let s=0;s<e[r][t].length;++s)for(let o=0;o<e[r][t][s].length;++o)e[r][t][s][o]*=i[o%2]}return new n.Tensor("float32",Float32Array.from(e.flat(1/0)),i)}add_input_labels(e,t){let s=(0,o.calculateDimensions)(e);if(2===s.length)s=[1,...s],e=[e];else if(3!==s.length)throw Error("The input_points must be a 4D tensor of shape `batch_size`, `point_batch_size`, `nb_points_per_image`, `2`.");if(s.some(((e,s)=>e!==t.dims[s])))throw Error(`The first ${s.length} dimensions of 'input_points' and 'input_labels' must be the same.`);return new n.Tensor("int64",e.flat(1/0).map(BigInt),s)}async _call(e,{input_points:t=null,input_labels:s=null,input_boxes:r=null}={}){const o=await super._call(e);if(t&&(o.input_points=this.reshape_input_points(t,o.original_sizes,o.reshaped_input_sizes)),s){if(!o.input_points)throw Error("`input_points` must be provided if `input_labels` are provided.");o.input_labels=this.add_input_labels(s,o.input_points)}return r&&(o.input_boxes=this.reshape_input_points(r,o.original_sizes,o.reshaped_input_sizes,!0)),o}async post_process_masks(e,t,s,{mask_threshold:r=0,binarize:o=!0,pad_size:i=null}={}){const a=[],l=[(i=i??this.pad_size).height,i.width];for(let i=0;i<t.length;++i){const c=t[i],d=s[i];let u=await(0,n.interpolate_4d)(e[i],{mode:"bilinear",size:l});if(u=u.slice(null,null,[0,d[0]],[0,d[1]]),u=await(0,n.interpolate_4d)(u,{mode:"bilinear",size:c}),o){const e=u.data,t=new Uint8Array(e.length);for(let s=0;s<e.length;++s)e[s]>r&&(t[s]=1);u=new n.Tensor("bool",t,u.dims)}a.push(u)}return a}generate_crop_boxes(e,t,{crop_n_layers:s=0,overlap_ratio:r=512/1500,points_per_crop:o=32,crop_n_points_downscale_factor:n=1}={}){}}},"./src/models/sam/processing_sam.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SamProcessor:()=>n});var r=s("./src/base/processing_utils.js"),o=s("./src/models/auto/image_processing_auto.js");class n extends r.Processor{static image_processor_class=o.AutoImageProcessor;async _call(...e){return await this.image_processor(...e)}post_process_masks(...e){return this.image_processor.post_process_masks(...e)}reshape_input_points(...e){return this.image_processor.reshape_input_points(...e)}}},"./src/models/seamless_m4t/feature_extraction_seamless_m4t.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SeamlessM4TFeatureExtractor:()=>i});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js"),n=s("./src/utils/audio.js");class i extends r.FeatureExtractor{constructor(e){super(e);const t=this.config.sampling_rate,s=(0,n.mel_filter_bank)(257,this.config.num_mel_bins,20,Math.floor(t/2),t,null,"kaldi",!0);this.mel_filters=s,this.window=(0,n.window_function)(400,"povey",{periodic:!1})}async _extract_fbank_features(e,t){return e=e.map((e=>32768*e)),(0,n.spectrogram)(e,this.window,400,160,{fft_length:512,power:2,center:!1,preemphasis:.97,mel_filters:this.mel_filters,log_mel:"log",mel_floor:1.192092955078125e-7,remove_dc_offset:!0,max_num_frames:t,transpose:!0})}async _call(e,{padding:t=!0,pad_to_multiple_of:s=2,do_normalize_per_mel_bins:n=!0,return_attention_mask:i=!0}={}){(0,r.validate_audio_inputs)(e,"SeamlessM4TFeatureExtractor");let a,l=await this._extract_fbank_features(e,this.config.max_length);if(n){const[e,t]=l.dims,s=l.data;for(let r=0;r<t;++r){let o=0;for(let n=0;n<e;++n)o+=s[n*t+r];const n=o/e;let i=0;for(let o=0;o<e;++o)i+=(s[o*t+r]-n)**2;i/=e-1;const a=Math.sqrt(i+1e-7);for(let o=0;o<e;++o){const e=o*t+r;s[e]=(s[e]-n)/a}}}if(t){const[e,t]=l.dims,r=l.data,n=e%s;if(n>0){const s=new Float32Array(t*(e+n));s.set(r),s.fill(this.config.padding_value,r.length);const c=e+n;l=new o.Tensor(l.type,s,[c,t]),i&&(a=new o.Tensor("int64",new BigInt64Array(c),[1,c]),a.data.fill(1n,0,e))}}const[c,d]=l.dims,u=this.config.stride;if(0!==c%u)throw new Error(`The number of frames (${c}) must be a multiple of the stride (${u}).`);const _=l.view(1,Math.floor(c/u),d*u),p={input_features:_};if(i){const e=_.dims[1],t=new BigInt64Array(e);if(a){const e=a.data;for(let s=1,r=0;s<c;s+=u,++r)t[r]=e[s]}else t.fill(1n);p.attention_mask=new o.Tensor("int64",t,[1,e])}return p}}},"./src/models/segformer/image_processing_segformer.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SegformerFeatureExtractor:()=>n,SegformerImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_semantic_segmentation(...e){return(0,r.post_process_semantic_segmentation)(...e)}}class n extends o{}},"./src/models/siglip/image_processing_siglip.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SiglipImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}},"./src/models/smolvlm/image_processing_smolvlm.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SmolVLMImageProcessor:()=>r.Idefics3ImageProcessor});var r=s("./src/models/idefics3/image_processing_idefics3.js")},"./src/models/smolvlm/processing_smolvlm.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SmolVLMProcessor:()=>r.Idefics3Processor});var r=s("./src/models/idefics3/processing_idefics3.js")},"./src/models/snac/feature_extraction_snac.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SnacFeatureExtractor:()=>o});var r=s("./src/models/dac/feature_extraction_dac.js");class o extends r.DacFeatureExtractor{}},"./src/models/speecht5/feature_extraction_speecht5.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SpeechT5FeatureExtractor:()=>o});var r=s("./src/base/feature_extraction_utils.js");class o extends r.FeatureExtractor{}},"./src/models/speecht5/processing_speecht5.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{SpeechT5Processor:()=>i});var r=s("./src/base/processing_utils.js"),o=s("./src/tokenizers.js"),n=s("./src/models/auto/feature_extraction_auto.js");class i extends r.Processor{static tokenizer_class=o.AutoTokenizer;static feature_extractor_class=n.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/swin2sr/image_processing_swin2sr.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Swin2SRImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{pad_image(e,t,s,r={}){const[o,n,i]=t;return super.pad_image(e,t,{width:n+(s-n%s)%s,height:o+(s-o%s)%s},{mode:"symmetric",center:!1,constant_values:-1,...r})}}},"./src/models/ultravox/processing_ultravox.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{UltravoxProcessor:()=>i});var r=s("./src/models/auto/feature_extraction_auto.js"),o=s("./src/tokenizers.js"),n=s("./src/base/processing_utils.js");class i extends n.Processor{static tokenizer_class=o.AutoTokenizer;static feature_extractor_class=r.AutoFeatureExtractor;static uses_processor_config=!0;async _call(e,t=null,s={}){if(Array.isArray(e))throw new Error("Batched inputs are not supported yet.");let r={};if(t){const o=t.length,{input_features:n}=await this.feature_extractor(t,{...s,max_length:o}),i=Math.round(o/this.config.encoder_ds_factor+1e-4),a=1+Math.ceil(i/this.config.stack_factor);r.audio_token_len=[a],r.audio_values=n;const l=this.config.audio_placeholder;if(!e.includes(l))throw new Error(`The input text does not contain the image token ${l}.`);e=e.replaceAll(l,l.repeat(a))}return{...this.tokenizer(e,{add_special_tokens:!1,...s}),...r}}}},"./src/models/vit/image_processing_vit.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{ViTFeatureExtractor:()=>n,ViTImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{}class n extends o{}},"./src/models/vitmatte/image_processing_vitmatte.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{VitMatteImageProcessor:()=>n});var r=s("./src/base/image_processors_utils.js"),o=s("./src/utils/tensor.js");class n extends r.ImageProcessor{async _call(e,t){Array.isArray(e)||(e=[e]),Array.isArray(t)||(t=[t]);const s=await Promise.all(e.map((e=>this.preprocess(e)))),r=await Promise.all(t.map((e=>this.preprocess(e,{do_normalize:!1,do_convert_rgb:!1,do_convert_grayscale:!0}))));return{pixel_values:(0,o.stack)(s.map(((e,t)=>(0,o.cat)([e.pixel_values,r[t].pixel_values],0))),0),original_sizes:s.map((e=>e.original_size)),reshaped_input_sizes:s.map((e=>e.reshaped_input_size))}}}},"./src/models/vitpose/image_processing_vitpose.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{VitPoseImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_pose_estimation(e,t,{threshold:s=null}={}){const r=e.tolist(),[o,n,i,a]=e.dims,l=[];for(let e=0;e<o;++e){const o=r[e],n=t[e],c=[];for(let e=0;e<n.length;++e){const t=n[e],r=[],l=[],d=[],u=t.at(-2)/a,_=t.at(-1)/i;for(let e=0;e<o.length;++e){let[t,n]=[0,0],i=0,a=-1/0;const c=o[e];for(let e=0;e<c.length;++e){const s=c[e];for(let r=0;r<s.length;++r){const o=s[r];i+=o,a=Math.max(a,o),t+=(r+.5)*o,n+=e*o}}if(null!=s&&a<s)continue;const p=[u*t/i,_*n/i];r.push(p),d.push(e),l.push(a)}c.push({bbox:t,scores:l,labels:d,keypoints:r})}l.push(c)}return l}}},"./src/models/wav2vec2/feature_extraction_wav2vec2.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Wav2Vec2FeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=s("./src/utils/tensor.js");class n extends r.FeatureExtractor{_zero_mean_unit_var_norm(e){const t=e.reduce(((e,t)=>e+t),0)/e.length,s=e.reduce(((e,s)=>e+(s-t)**2),0)/e.length;return e.map((e=>(e-t)/Math.sqrt(s+1e-7)))}async _call(e){(0,r.validate_audio_inputs)(e,"Wav2Vec2FeatureExtractor"),e instanceof Float64Array&&(e=new Float32Array(e));let t=e;this.config.do_normalize&&(t=this._zero_mean_unit_var_norm(t));const s=[1,t.length];return{input_values:new o.Tensor("float32",t,s),attention_mask:new o.Tensor("int64",new BigInt64Array(t.length).fill(1n),s)}}}},"./src/models/wav2vec2/processing_wav2vec2.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Wav2Vec2Processor:()=>i});var r=s("./src/tokenizers.js"),o=s("./src/models/auto/feature_extraction_auto.js"),n=s("./src/base/processing_utils.js");class i extends n.Processor{static tokenizer_class=r.AutoTokenizer;static feature_extractor_class=o.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/wav2vec2_with_lm/processing_wav2vec2_with_lm.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Wav2Vec2ProcessorWithLM:()=>i});var r=s("./src/tokenizers.js"),o=s("./src/models/auto/feature_extraction_auto.js"),n=s("./src/base/processing_utils.js");class i extends n.Processor{static tokenizer_class=r.AutoTokenizer;static feature_extractor_class=o.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/wespeaker/feature_extraction_wespeaker.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{WeSpeakerFeatureExtractor:()=>n});var r=s("./src/base/feature_extraction_utils.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/audio.js"));class n extends r.FeatureExtractor{constructor(e){super(e);const t=this.config.sampling_rate,s=(0,o.mel_filter_bank)(257,this.config.num_mel_bins,20,Math.floor(t/2),t,null,"kaldi",!0);this.mel_filters=s,this.window=(0,o.window_function)(400,"hamming",{periodic:!1}),this.min_num_frames=this.config.min_num_frames}async _extract_fbank_features(e){return e=e.map((e=>32768*e)),(0,o.spectrogram)(e,this.window,400,160,{fft_length:512,power:2,center:!1,preemphasis:.97,mel_filters:this.mel_filters,log_mel:"log",mel_floor:1.192092955078125e-7,remove_dc_offset:!0,transpose:!0,min_num_frames:this.min_num_frames})}async _call(e){(0,r.validate_audio_inputs)(e,"WeSpeakerFeatureExtractor");const t=(await this._extract_fbank_features(e)).unsqueeze_(0);if(null===this.config.fbank_centering_span){const e=t.mean(1).data,s=t.data,[r,o,n]=t.dims;for(let t=0;t<r;++t){const r=t*o*n,i=t*n;for(let t=0;t<o;++t){const o=r+t*n;for(let t=0;t<n;++t)s[o+t]-=e[i+t]}}}return{input_features:t}}}},"./src/models/whisper/common_whisper.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{WHISPER_LANGUAGE_MAPPING:()=>o,WHISPER_TO_LANGUAGE_CODE_MAPPING:()=>n,whisper_language_to_code:()=>i});const r=[["en","english"],["zh","chinese"],["de","german"],["es","spanish"],["ru","russian"],["ko","korean"],["fr","french"],["ja","japanese"],["pt","portuguese"],["tr","turkish"],["pl","polish"],["ca","catalan"],["nl","dutch"],["ar","arabic"],["sv","swedish"],["it","italian"],["id","indonesian"],["hi","hindi"],["fi","finnish"],["vi","vietnamese"],["he","hebrew"],["uk","ukrainian"],["el","greek"],["ms","malay"],["cs","czech"],["ro","romanian"],["da","danish"],["hu","hungarian"],["ta","tamil"],["no","norwegian"],["th","thai"],["ur","urdu"],["hr","croatian"],["bg","bulgarian"],["lt","lithuanian"],["la","latin"],["mi","maori"],["ml","malayalam"],["cy","welsh"],["sk","slovak"],["te","telugu"],["fa","persian"],["lv","latvian"],["bn","bengali"],["sr","serbian"],["az","azerbaijani"],["sl","slovenian"],["kn","kannada"],["et","estonian"],["mk","macedonian"],["br","breton"],["eu","basque"],["is","icelandic"],["hy","armenian"],["ne","nepali"],["mn","mongolian"],["bs","bosnian"],["kk","kazakh"],["sq","albanian"],["sw","swahili"],["gl","galician"],["mr","marathi"],["pa","punjabi"],["si","sinhala"],["km","khmer"],["sn","shona"],["yo","yoruba"],["so","somali"],["af","afrikaans"],["oc","occitan"],["ka","georgian"],["be","belarusian"],["tg","tajik"],["sd","sindhi"],["gu","gujarati"],["am","amharic"],["yi","yiddish"],["lo","lao"],["uz","uzbek"],["fo","faroese"],["ht","haitian creole"],["ps","pashto"],["tk","turkmen"],["nn","nynorsk"],["mt","maltese"],["sa","sanskrit"],["lb","luxembourgish"],["my","myanmar"],["bo","tibetan"],["tl","tagalog"],["mg","malagasy"],["as","assamese"],["tt","tatar"],["haw","hawaiian"],["ln","lingala"],["ha","hausa"],["ba","bashkir"],["jw","javanese"],["su","sundanese"]],o=new Map(r),n=new Map([...r.map((([e,t])=>[t,e])),["burmese","my"],["valencian","ca"],["flemish","nl"],["haitian","ht"],["letzeburgesch","lb"],["pushto","ps"],["panjabi","pa"],["moldavian","ro"],["moldovan","ro"],["sinhalese","si"],["castilian","es"]]);function i(e){e=e.toLowerCase();let t=n.get(e);if(void 0===t){const s=e.match(/^<\|([a-z]{2})\|>$/);if(s&&(e=s[1]),!o.has(e)){const t=2===e.length?o.keys():o.values();throw new Error(`Language "${e}" is not supported. Must be one of: ${JSON.stringify(Array.from(t))}`)}t=e}return t}},"./src/models/whisper/feature_extraction_whisper.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{WhisperFeatureExtractor:()=>i});var r=s("./src/base/feature_extraction_utils.js"),o=(s("./src/utils/tensor.js"),s("./src/utils/audio.js")),n=s("./src/utils/maths.js");class i extends r.FeatureExtractor{constructor(e){super(e),this.config.mel_filters??=(0,o.mel_filter_bank)(Math.floor(1+this.config.n_fft/2),this.config.feature_size,0,8e3,this.config.sampling_rate,"slaney","slaney"),this.window=(0,o.window_function)(this.config.n_fft,"hann")}async _extract_fbank_features(e){const t=await(0,o.spectrogram)(e,this.window,this.config.n_fft,this.config.hop_length,{power:2,mel_filters:this.config.mel_filters,log_mel:"log10",max_num_frames:Math.min(Math.floor(e.length/this.config.hop_length),this.config.nb_max_frames)}),s=t.data,r=(0,n.max)(s)[0];for(let e=0;e<s.length;++e)s[e]=(Math.max(s[e],r-8)+4)/4;return t}async _call(e,{max_length:t=null}={}){let s;(0,r.validate_audio_inputs)(e,"WhisperFeatureExtractor");const o=t??this.config.n_samples;e.length>o?(e.length>this.config.n_samples&&console.warn("Attempting to extract features for audio longer than 30 seconds. If using a pipeline to extract transcript from a long audio clip, remember to specify `chunk_length_s` and/or `stride_length_s`."),s=e.slice(0,o)):(s=new Float32Array(o),s.set(e));return{input_features:(await this._extract_fbank_features(s)).unsqueeze_(0)}}}},"./src/models/whisper/generation_whisper.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{WhisperGenerationConfig:()=>o});var r=s("./src/generation/configuration_utils.js");class o extends r.GenerationConfig{return_timestamps=null;return_token_timestamps=null;num_frames=null;alignment_heads=null;task=null;language=null;no_timestamps_token_id=null;prompt_ids=null;is_multilingual=null;lang_to_id=null;task_to_id=null;max_initial_timestamp_index=1}},"./src/models/whisper/processing_whisper.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{WhisperProcessor:()=>i});var r=s("./src/models/auto/feature_extraction_auto.js"),o=s("./src/tokenizers.js"),n=s("./src/base/processing_utils.js");class i extends n.Processor{static tokenizer_class=o.AutoTokenizer;static feature_extractor_class=r.AutoFeatureExtractor;async _call(e){return await this.feature_extractor(e)}}},"./src/models/yolos/image_processing_yolos.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{YolosFeatureExtractor:()=>n,YolosImageProcessor:()=>o});var r=s("./src/base/image_processors_utils.js");class o extends r.ImageProcessor{post_process_object_detection(...e){return(0,r.post_process_object_detection)(...e)}}class n extends o{}},"./src/ops/registry.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{TensorOpRegistry:()=>l});var r=s("./src/backends/onnx.js"),o=s("./src/utils/tensor.js"),n=s("./src/env.js");const i=n.apis.IS_BROWSER_ENV||n.apis.IS_WEBWORKER_ENV,a=async(e,t,s)=>{const n=await(0,r.createInferenceSession)(new Uint8Array(e),t);let a=Promise.resolve();return async e=>{const t=(0,r.isONNXProxy)(),l=Object.fromEntries(Object.entries(e).map((([e,s])=>[e,(t?s.clone():s).ort_tensor]))),c=await(a=i?a.then((()=>n.run(l))):n.run(l));return Array.isArray(s)?s.map((e=>new o.Tensor(c[e]))):new o.Tensor(c[s])}};class l{static session_options={};static get nearest_interpolate_4d(){return this._nearest_interpolate_4d||(this._nearest_interpolate_4d=a([8,10,18,0,58,129,1,10,41,10,1,120,10,0,10,0,10,1,115,18,1,121,34,6,82,101,115,105,122,101,42,18,10,4,109,111,100,101,34,7,110,101,97,114,101,115,116,160,1,3,18,1,114,90,31,10,1,120,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,90,15,10,1,115,18,10,10,8,8,7,18,4,10,2,8,4,98,31,10,1,121,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,66,2,16,21],this.session_options,"y")),this._nearest_interpolate_4d}static get bilinear_interpolate_4d(){return this._bilinear_interpolate_4d||(this._bilinear_interpolate_4d=a([8,9,18,0,58,128,1,10,40,10,1,120,10,0,10,0,10,1,115,18,1,121,34,6,82,101,115,105,122,101,42,17,10,4,109,111,100,101,34,6,108,105,110,101,97,114,160,1,3,18,1,114,90,31,10,1,120,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,90,15,10,1,115,18,10,10,8,8,7,18,4,10,2,8,4,98,31,10,1,121,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,66,2,16,20],this.session_options,"y")),this._bilinear_interpolate_4d}static get bicubic_interpolate_4d(){return this._bicubic_interpolate_4d||(this._bicubic_interpolate_4d=a([8,9,18,0,58,127,10,39,10,1,120,10,0,10,0,10,1,115,18,1,121,34,6,82,101,115,105,122,101,42,16,10,4,109,111,100,101,34,5,99,117,98,105,99,160,1,3,18,1,114,90,31,10,1,120,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,90,15,10,1,115,18,10,10,8,8,7,18,4,10,2,8,4,98,31,10,1,121,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,99,10,3,18,1,104,10,3,18,1,119,66,2,16,20],this.session_options,"y")),this._bicubic_interpolate_4d}static get matmul(){return this._matmul||(this._matmul=a([8,9,18,0,58,55,10,17,10,1,97,10,1,98,18,1,99,34,6,77,97,116,77,117,108,18,1,114,90,9,10,1,97,18,4,10,2,8,1,90,9,10,1,98,18,4,10,2,8,1,98,9,10,1,99,18,4,10,2,8,1,66,2,16,20],this.session_options,"c")),this._matmul}static get stft(){return this._stft||(this._stft=a([8,7,18,0,58,148,1,10,38,10,1,115,10,1,106,10,1,119,10,1,108,18,1,111,34,4,83,84,70,84,42,15,10,8,111,110,101,115,105,100,101,100,24,1,160,1,2,18,1,115,90,26,10,1,115,18,21,10,19,8,1,18,15,10,3,18,1,98,10,3,18,1,115,10,3,18,1,99,90,11,10,1,106,18,6,10,4,8,7,18,0,90,16,10,1,119,18,11,10,9,8,1,18,5,10,3,18,1,119,90,11,10,1,108,18,6,10,4,8,7,18,0,98,31,10,1,111,18,26,10,24,8,1,18,20,10,3,18,1,98,10,3,18,1,102,10,3,18,1,100,10,3,18,1,99,66,2,16,17],this.session_options,"o")),this._stft}static get rfft(){return this._rfft||(this._rfft=a([8,9,18,0,58,97,10,33,10,1,120,10,0,10,1,97,18,1,121,34,3,68,70,84,42,15,10,8,111,110,101,115,105,100,101,100,24,1,160,1,2,18,1,100,90,21,10,1,120,18,16,10,14,8,1,18,10,10,3,18,1,115,10,3,18,1,99,90,11,10,1,97,18,6,10,4,8,7,18,0,98,21,10,1,121,18,16,10,14,8,1,18,10,10,3,18,1,115,10,3,18,1,99,66,2,16,20],this.session_options,"y")),this._rfft}static get top_k(){return this._top_k||(this._top_k=a([8,10,18,0,58,73,10,18,10,1,120,10,1,107,18,1,118,18,1,105,34,4,84,111,112,75,18,1,116,90,9,10,1,120,18,4,10,2,8,1,90,15,10,1,107,18,10,10,8,8,7,18,4,10,2,8,1,98,9,10,1,118,18,4,10,2,8,1,98,9,10,1,105,18,4,10,2,8,7,66,2,16,21],this.session_options,["v","i"])),this._top_k}static get slice(){return this._slice||(this._slice=a([8,7,18,0,58,96,10,25,10,1,120,10,1,115,10,1,101,10,1,97,10,1,116,18,1,121,34,5,83,108,105,99,101,18,1,114,90,9,10,1,120,18,4,10,2,8,1,90,9,10,1,115,18,4,10,2,8,7,90,9,10,1,101,18,4,10,2,8,7,90,9,10,1,97,18,4,10,2,8,7,90,9,10,1,116,18,4,10,2,8,7,98,9,10,1,121,18,4,10,2,8,1,66,2,16,13],this.session_options,"y")),this._slice}}},"./src/pipelines.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{AudioClassificationPipeline:()=>C,AutomaticSpeechRecognitionPipeline:()=>E,BackgroundRemovalPipeline:()=>z,DepthEstimationPipeline:()=>G,DocumentQuestionAnsweringPipeline:()=>N,FeatureExtractionPipeline:()=>P,FillMaskPipeline:()=>M,ImageClassificationPipeline:()=>L,ImageFeatureExtractionPipeline:()=>F,ImageSegmentationPipeline:()=>I,ImageToImagePipeline:()=>B,ImageToTextPipeline:()=>A,ObjectDetectionPipeline:()=>D,Pipeline:()=>h,QuestionAnsweringPipeline:()=>w,SummarizationPipeline:()=>b,Text2TextGenerationPipeline:()=>x,TextClassificationPipeline:()=>g,TextGenerationPipeline:()=>v,TextToAudioPipeline:()=>V,TokenClassificationPipeline:()=>f,TranslationPipeline:()=>k,ZeroShotAudioClassificationPipeline:()=>S,ZeroShotClassificationPipeline:()=>T,ZeroShotImageClassificationPipeline:()=>j,ZeroShotObjectDetectionPipeline:()=>O,pipeline:()=>q});var r=s("./src/tokenizers.js"),o=s("./src/models.js"),n=s("./src/models/auto/processing_auto.js"),i=(s("./src/base/processing_utils.js"),s("./src/utils/generic.js")),a=s("./src/utils/core.js"),l=s("./src/utils/maths.js"),c=s("./src/utils/audio.js"),d=s("./src/utils/tensor.js"),u=s("./src/utils/image.js");async function _(e){return Array.isArray(e)||(e=[e]),await Promise.all(e.map((e=>u.RawImage.read(e))))}async function p(e,t){return Array.isArray(e)||(e=[e]),await Promise.all(e.map((e=>"string"==typeof e||e instanceof URL?(0,c.read_audio)(e,t):e instanceof Float64Array?new Float32Array(e):e)))}function m(e,t){t&&(e=e.map((e=>0|e)));const[s,r,o,n]=e;return{xmin:s,ymin:r,xmax:o,ymax:n}}class h extends i.Callable{constructor({task:e,model:t,tokenizer:s=null,processor:r=null}){super(),this.task=e,this.model=t,this.tokenizer=s,this.processor=r}async dispose(){await this.model.dispose()}}class g extends h{constructor(e){super(e)}async _call(e,{top_k:t=1}={}){const s=this.tokenizer(e,{padding:!0,truncation:!0}),r=await this.model(s),o="multi_label_classification"===this.model.config.problem_type?e=>e.sigmoid():e=>new d.Tensor("float32",(0,l.softmax)(e.data),e.dims),n=this.model.config.id2label,i=[];for(const e of r.logits){const s=o(e),r=await(0,d.topk)(s,t),a=r[0].tolist(),l=r[1].tolist().map(((e,t)=>({label:n?n[e]:`LABEL_${e}`,score:a[t]})));1===t?i.push(...l):i.push(l)}return Array.isArray(e)||1===t?i:i[0]}}class f extends h{constructor(e){super(e)}async _call(e,{ignore_labels:t=["O"]}={}){const s=Array.isArray(e),r=this.tokenizer(s?e:[e],{padding:!0,truncation:!0}),o=(await this.model(r)).logits,n=this.model.config.id2label,i=[];for(let e=0;e<o.dims[0];++e){const s=r.input_ids[e],a=o[e],c=[];for(let e=0;e<a.dims[0];++e){const r=a[e],o=(0,l.max)(r.data)[1],i=n?n[o]:`LABEL_${o}`;if(t.includes(i))continue;const d=this.tokenizer.decode([s[e].item()],{skip_special_tokens:!0});if(""===d)continue;const u=(0,l.softmax)(r.data);c.push({entity:i,score:u[o],index:e,word:d})}i.push(c)}return s?i:i[0]}}class w extends h{constructor(e){super(e)}async _call(e,t,{top_k:s=1}={}){const r=this.tokenizer(e,{text_pair:t,padding:!0,truncation:!0}),{start_logits:o,end_logits:n}=await this.model(r),i=r.input_ids.tolist(),c=r.attention_mask.tolist(),d=this.tokenizer.all_special_ids,u=[];for(let e=0;e<o.dims[0];++e){const t=i[e],r=t.findIndex((e=>e==this.tokenizer.sep_token_id)),_=(c[e].map(((e,s)=>1==e&&(0===s||s>r&&-1===d.findIndex((e=>e==t[s]))))),o[e].tolist()),p=n[e].tolist();for(let s=1;s<_.length;++s)(0==c[e]||s<=r||-1!==d.findIndex((e=>e==t[s])))&&(_[s]=-1/0,p[s]=-1/0);const m=(0,l.softmax)(_).map(((e,t)=>[e,t])),h=(0,l.softmax)(p).map(((e,t)=>[e,t]));m[0][0]=0,h[0][0]=0;const g=(0,a.product)(m,h).filter((e=>e[0][1]<=e[1][1])).map((e=>[e[0][1],e[1][1],e[0][0]*e[1][0]])).sort(((e,t)=>t[2]-e[2]));for(let e=0;e<Math.min(g.length,s);++e){const[s,r,o]=g[e],n=t.slice(s,r+1),i=this.tokenizer.decode(n,{skip_special_tokens:!0});u.push({answer:i,score:o})}}return 1===s?u[0]:u}}class M extends h{constructor(e){super(e)}async _call(e,{top_k:t=5}={}){const s=this.tokenizer(e,{padding:!0,truncation:!0}),{logits:r}=await this.model(s),o=[],n=s.input_ids.tolist();for(let e=0;e<n.length;++e){const s=n[e],i=s.findIndex((e=>e==this.tokenizer.mask_token_id));if(-1===i)throw Error(`Mask token (${this.tokenizer.mask_token}) not found in text.`);const a=r[e][i],c=await(0,d.topk)(new d.Tensor("float32",(0,l.softmax)(a.data),a.dims),t),u=c[0].tolist(),_=c[1].tolist();o.push(_.map(((e,t)=>{const r=s.slice();return r[i]=e,{score:u[t],token:Number(e),token_str:this.tokenizer.decode([e]),sequence:this.tokenizer.decode(r,{skip_special_tokens:!0})}})))}return Array.isArray(e)?o:o[0]}}class x extends h{_key="generated_text";constructor(e){super(e)}async _call(e,t={}){Array.isArray(e)||(e=[e]),this.model.config.prefix&&(e=e.map((e=>this.model.config.prefix+e)));const s=this.model.config.task_specific_params;s&&s[this.task]&&s[this.task].prefix&&(e=e.map((e=>s[this.task].prefix+e)));const r=this.tokenizer,o={padding:!0,truncation:!0};let n;n=this instanceof k&&"_build_translation_inputs"in r?r._build_translation_inputs(e,o,t):r(e,o);const i=await this.model.generate({...n,...t});return r.batch_decode(i,{skip_special_tokens:!0}).map((e=>({[this._key]:e})))}}class b extends x{_key="summary_text";constructor(e){super(e)}}class k extends x{_key="translation_text";constructor(e){super(e)}}function y(e){return Array.isArray(e)&&e.every((e=>"role"in e&&"content"in e))}class v extends h{constructor(e){super(e)}async _call(e,t={}){let s,r=!1,o=!1;if("string"==typeof e)s=e=[e];else if(Array.isArray(e)&&e.every((e=>"string"==typeof e)))r=!0,s=e;else{if(y(e))e=[e];else{if(!Array.isArray(e)||!e.every(y))throw new Error("Input must be a string, an array of strings, a Chat, or an array of Chats");r=!0}o=!0,s=e.map((e=>this.tokenizer.apply_chat_template(e,{tokenize:!1,add_generation_prompt:!0})))}const n=t.add_special_tokens??!1,i=!o&&(t.return_full_text??!0);this.tokenizer.padding_side="left";const a=this.tokenizer(s,{add_special_tokens:n,padding:!0,truncation:!0}),l=await this.model.generate({...a,...t}),c=this.tokenizer.batch_decode(l,{skip_special_tokens:!0});let d;!i&&a.input_ids.dims.at(-1)>0&&(d=this.tokenizer.batch_decode(a.input_ids,{skip_special_tokens:!0}).map((e=>e.length)));const u=Array.from({length:e.length},(e=>[]));for(let t=0;t<c.length;++t){const s=Math.floor(t/l.dims[0]*e.length);d&&(c[t]=c[t].slice(d[s])),u[s].push({generated_text:o?[...e[s],{role:"assistant",content:c[t]}]:c[t]})}return r||1!==u.length?u:u[0]}}class T extends h{constructor(e){super(e),this.label2id=Object.fromEntries(Object.entries(this.model.config.label2id).map((([e,t])=>[e.toLowerCase(),t]))),this.entailment_id=this.label2id.entailment,void 0===this.entailment_id&&(console.warn("Could not find 'entailment' in label2id mapping. Using 2 as entailment_id."),this.entailment_id=2),this.contradiction_id=this.label2id.contradiction??this.label2id.not_entailment,void 0===this.contradiction_id&&(console.warn("Could not find 'contradiction' in label2id mapping. Using 0 as contradiction_id."),this.contradiction_id=0)}async _call(e,t,{hypothesis_template:s="This example is {}.",multi_label:r=!1}={}){const o=Array.isArray(e);o||(e=[e]),Array.isArray(t)||(t=[t]);const n=t.map((e=>s.replace("{}",e))),i=r||1===t.length,a=[];for(const s of e){const e=[];for(const t of n){const r=this.tokenizer(s,{text_pair:t,padding:!0,truncation:!0}),o=await this.model(r);i?e.push([o.logits.data[this.contradiction_id],o.logits.data[this.entailment_id]]):e.push(o.logits.data[this.entailment_id])}const r=(i?e.map((e=>(0,l.softmax)(e)[1])):(0,l.softmax)(e)).map(((e,t)=>[e,t])).sort(((e,t)=>t[0]-e[0]));a.push({sequence:s,labels:r.map((e=>t[e[1]])),scores:r.map((e=>e[0]))})}return o?a:a[0]}}class P extends h{constructor(e){super(e)}async _call(e,{pooling:t="none",normalize:s=!1,quantize:r=!1,precision:o="binary"}={}){const n=this.tokenizer(e,{padding:!0,truncation:!0}),i=await this.model(n);let a=i.last_hidden_state??i.logits??i.token_embeddings;switch(t){case"none":break;case"mean":a=(0,d.mean_pooling)(a,n.attention_mask);break;case"first_token":case"cls":a=a.slice(null,0);break;case"last_token":case"eos":a=a.slice(null,-1);break;default:throw Error(`Pooling method '${t}' not supported.`)}return s&&(a=a.normalize(2,-1)),r&&(a=(0,d.quantize_embeddings)(a,o)),a}}class F extends h{constructor(e){super(e)}async _call(e,{pool:t=null}={}){const s=await _(e),{pixel_values:r}=await this.processor(s),o=await this.model({pixel_values:r});let n;if(t){if(!("pooler_output"in o))throw Error("No pooled output was returned. Make sure the model has a 'pooler' layer when using the 'pool' option.");n=o.pooler_output}else n=o.last_hidden_state??o.logits??o.image_embeds;return n}}class C extends h{constructor(e){super(e)}async _call(e,{top_k:t=5}={}){const s=this.processor.feature_extractor.config.sampling_rate,r=await p(e,s),o=this.model.config.id2label,n=[];for(const e of r){const s=await this.processor(e),r=(await this.model(s)).logits[0],i=await(0,d.topk)(new d.Tensor("float32",(0,l.softmax)(r.data),r.dims),t),a=i[0].tolist(),c=i[1].tolist().map(((e,t)=>({label:o?o[e]:`LABEL_${e}`,score:a[t]})));n.push(c)}return Array.isArray(e)?n:n[0]}}class S extends h{constructor(e){super(e)}async _call(e,t,{hypothesis_template:s="This is a sound of {}."}={}){const r=!Array.isArray(e);r&&(e=[e]);const o=t.map((e=>s.replace("{}",e))),n=this.tokenizer(o,{padding:!0,truncation:!0}),i=this.processor.feature_extractor.config.sampling_rate,a=await p(e,i),c=[];for(const e of a){const s=await this.processor(e),r=await this.model({...n,...s}),o=(0,l.softmax)(r.logits_per_audio.data);c.push([...o].map(((e,s)=>({score:e,label:t[s]}))))}return r?c[0]:c}}class E extends h{constructor(e){super(e)}async _call(e,t={}){switch(this.model.config.model_type){case"whisper":case"lite-whisper":return this._call_whisper(e,t);case"wav2vec2":case"wav2vec2-bert":case"unispeech":case"unispeech-sat":case"hubert":return this._call_wav2vec2(e,t);case"moonshine":return this._call_moonshine(e,t);default:throw new Error(`AutomaticSpeechRecognitionPipeline does not support model type '${this.model.config.model_type}'.`)}}async _call_wav2vec2(e,t){t.language&&console.warn('`language` parameter is not yet supported for `wav2vec2` models, defaulting to "English".'),t.task&&console.warn('`task` parameter is not yet supported for `wav2vec2` models, defaulting to "transcribe".');const s=!Array.isArray(e);s&&(e=[e]);const r=this.processor.feature_extractor.config.sampling_rate,o=await p(e,r),n=[];for(const e of o){const t=await this.processor(e),s=(await this.model(t)).logits[0],r=[];for(const e of s)r.push((0,l.max)(e.data)[1]);const o=this.tokenizer.decode(r);n.push({text:o})}return s?n[0]:n}async _call_whisper(e,t){const s=t.return_timestamps??!1,r=t.chunk_length_s??0,o=t.force_full_sequences??!1;let n=t.stride_length_s??null;const i={...t};"word"===s&&(i.return_token_timestamps=!0,i.return_timestamps=!1);const a=!Array.isArray(e);a&&(e=[e]);const c=this.processor.feature_extractor.config.chunk_length/this.model.config.max_source_positions,d=this.processor.feature_extractor.config.hop_length,u=this.processor.feature_extractor.config.sampling_rate,_=await p(e,u),m=[];for(const e of _){let t=[];if(r>0){if(null===n)n=r/6;else if(r<=n)throw Error("`chunk_length_s` must be larger than `stride_length_s`.");const s=u*r,o=u*n,i=s-2*o;let a=0;for(;;){const r=a+s,n=e.subarray(a,r),l=await this.processor(n),c=0===a,d=r>=e.length;if(t.push({stride:[n.length,c?0:o,d?0:o],input_features:l.input_features,is_last:d}),d)break;a+=i}}else t=[{stride:[e.length,0,0],input_features:(await this.processor(e)).input_features,is_last:!0}];for(const e of t){i.num_frames=Math.floor(e.stride[0]/d);const t=await this.model.generate({inputs:e.input_features,...i});"word"===s?(e.tokens=t.sequences.tolist()[0],e.token_timestamps=t.token_timestamps.tolist()[0].map((e=>(0,l.round)(e,2)))):e.tokens=t[0].tolist(),e.stride=e.stride.map((e=>e/u))}const[a,_]=this.tokenizer._decode_asr(t,{time_precision:c,return_timestamps:s,force_full_sequences:o});m.push({text:a,..._})}return a?m[0]:m}async _call_moonshine(e,t){const s=!Array.isArray(e);s&&(e=[e]);const r=this.processor.feature_extractor.config.sampling_rate,o=await p(e,r),n=[];for(const e of o){const s=await this.processor(e),o=6*Math.floor(e.length/r),i=await this.model.generate({max_new_tokens:o,...t,...s}),a=this.processor.batch_decode(i,{skip_special_tokens:!0})[0];n.push({text:a})}return s?n[0]:n}}class A extends h{constructor(e){super(e)}async _call(e,t={}){const s=Array.isArray(e),r=await _(e),{pixel_values:o}=await this.processor(r),n=[];for(const e of o){e.dims=[1,...e.dims];const s=await this.model.generate({inputs:e,...t}),r=this.tokenizer.batch_decode(s,{skip_special_tokens:!0}).map((e=>({generated_text:e.trim()})));n.push(r)}return s?n:n[0]}}class L extends h{constructor(e){super(e)}async _call(e,{top_k:t=5}={}){const s=await _(e),{pixel_values:r}=await this.processor(s),o=await this.model({pixel_values:r}),n=this.model.config.id2label,i=[];for(const e of o.logits){const s=await(0,d.topk)(new d.Tensor("float32",(0,l.softmax)(e.data),e.dims),t),r=s[0].tolist(),o=s[1].tolist().map(((e,t)=>({label:n?n[e]:`LABEL_${e}`,score:r[t]})));i.push(o)}return Array.isArray(e)?i:i[0]}}class I extends h{constructor(e){super(e),this.subtasks_mapping={panoptic:"post_process_panoptic_segmentation",instance:"post_process_instance_segmentation",semantic:"post_process_semantic_segmentation"}}async _call(e,{threshold:t=.5,mask_threshold:s=.5,overlap_mask_area_threshold:r=.8,label_ids_to_fuse:o=null,target_sizes:n=null,subtask:i=null}={}){if(Array.isArray(e)&&1!==e.length)throw Error("Image segmentation pipeline currently only supports a batch size of 1.");const a=await _(e),l=a.map((e=>[e.height,e.width])),c=await this.processor(a),{inputNames:d,outputNames:p}=this.model.sessions.model;if(!d.includes("pixel_values")){if(1!==d.length)throw Error(`Expected a single input name, but got ${d.length} inputs: ${d}.`);const e=d[0];if(e in c)throw Error(`Input name ${e} already exists in the inputs.`);c[e]=c.pixel_values}const m=await this.model(c);let h=null;if(null!==i)h=this.subtasks_mapping[i];else if(this.processor.image_processor)for(const[e,t]of Object.entries(this.subtasks_mapping))if(t in this.processor.image_processor){h=this.processor.image_processor[t].bind(this.processor.image_processor),i=e;break}const g=this.model.config.id2label,f=[];if(i)if("panoptic"===i||"instance"===i){const e=h(m,t,s,r,o,n??l)[0],i=e.segmentation;for(const t of e.segments_info){const e=new Uint8ClampedArray(i.data.length);for(let s=0;s<i.data.length;++s)i.data[s]===t.id&&(e[s]=255);const s=new u.RawImage(e,i.dims[1],i.dims[0],1);f.push({score:t.score,label:g[t.label_id],mask:s})}}else{if("semantic"!==i)throw Error(`Subtask ${i} not supported.`);{const{segmentation:e,labels:t}=h(m,n??l)[0];for(const s of t){const t=new Uint8ClampedArray(e.data.length);for(let r=0;r<e.data.length;++r)e.data[r]===s&&(t[r]=255);const r=new u.RawImage(t,e.dims[1],e.dims[0],1);f.push({score:null,label:g[s],mask:r})}}}else{const e=1e-5,t=m[p[0]];for(let s=0;s<l.length;++s){const r=l[s],o=t[s];o.data.some((t=>t<-e||t>1+e))&&o.sigmoid_();const n=await u.RawImage.fromTensor(o.mul_(255).to("uint8")).resize(r[1],r[0]);f.push({label:null,score:null,mask:n})}}return f}}class z extends I{constructor(e){super(e)}async _call(e,t={}){if(Array.isArray(e)&&1!==e.length)throw Error("Background removal pipeline currently only supports a batch size of 1.");const s=await _(e),r=await super._call(e,t);return s.map(((e,t)=>{const s=e.clone();return s.putAlpha(r[t].mask),s}))}}class j extends h{constructor(e){super(e)}async _call(e,t,{hypothesis_template:s="This is a photo of {}"}={}){const r=Array.isArray(e),o=await _(e),n=t.map((e=>s.replace("{}",e))),i=this.tokenizer(n,{padding:"siglip"!==this.model.config.model_type||"max_length",truncation:!0}),{pixel_values:a}=await this.processor(o),c=await this.model({...i,pixel_values:a}),d="siglip"===this.model.config.model_type?e=>e.sigmoid().data:e=>(0,l.softmax)(e.data),u=[];for(const e of c.logits_per_image){const s=[...d(e)].map(((e,s)=>({score:e,label:t[s]})));s.sort(((e,t)=>t.score-e.score)),u.push(s)}return r?u:u[0]}}class D extends h{constructor(e){super(e)}async _call(e,{threshold:t=.9,percentage:s=!1}={}){const r=Array.isArray(e);if(r&&1!==e.length)throw Error("Object detection pipeline currently only supports a batch size of 1.");const o=await _(e),n=s?null:o.map((e=>[e.height,e.width])),{pixel_values:i,pixel_mask:a}=await this.processor(o),l=await this.model({pixel_values:i,pixel_mask:a}),c=this.processor.image_processor.post_process_object_detection(l,t,n),d=this.model.config.id2label,u=c.map((e=>e.boxes.map(((t,r)=>({score:e.scores[r],label:d[e.classes[r]],box:m(t,!s)})))));return r?u:u[0]}}class O extends h{constructor(e){super(e)}async _call(e,t,{threshold:s=.1,top_k:r=null,percentage:o=!1}={}){const n=Array.isArray(e),i=await _(e),a=this.tokenizer(t,{padding:!0,truncation:!0}),l=await this.processor(i),c=[];for(let e=0;e<i.length;++e){const n=i[e],d=o?null:[[n.height,n.width]],u=l.pixel_values[e].unsqueeze_(0),_=await this.model({...a,pixel_values:u});let p;if("post_process_grounded_object_detection"in this.processor){const e=this.processor.post_process_grounded_object_detection(_,a.input_ids,{box_threshold:s,text_threshold:s,target_sizes:d})[0];p=e.boxes.map(((t,s)=>({score:e.scores[s],label:e.labels[s],box:m(t,!o)})))}else{const e=this.processor.image_processor.post_process_object_detection(_,s,d,!0)[0];p=e.boxes.map(((s,r)=>({score:e.scores[r],label:t[e.classes[r]],box:m(s,!o)})))}p.sort(((e,t)=>t.score-e.score)),null!==r&&(p=p.slice(0,r)),c.push(p)}return n?c:c[0]}}class N extends h{constructor(e){super(e)}async _call(e,t,s={}){const r=(await _(e))[0],{pixel_values:o}=await this.processor(r),n=`<s_docvqa><s_question>${t}</s_question><s_answer>`,i=this.tokenizer(n,{add_special_tokens:!1,padding:!0,truncation:!0}).input_ids,a=await this.model.generate({inputs:o,max_length:this.model.config.decoder.max_position_embeddings,decoder_input_ids:i,...s}),l=this.tokenizer.batch_decode(a)[0].match(/<s_answer>(.*?)<\/s_answer>/);let c=null;return l&&l.length>=2&&(c=l[1].trim()),[{answer:c}]}}class V extends h{DEFAULT_VOCODER_ID="Xenova/speecht5_hifigan";constructor(e){super(e),this.vocoder=e.vocoder??null}async _call(e,{speaker_embeddings:t=null}={}){return this.processor?this._call_text_to_spectrogram(e,{speaker_embeddings:t}):this._call_text_to_waveform(e)}async _call_text_to_waveform(e){const t=this.tokenizer(e,{padding:!0,truncation:!0}),{waveform:s}=await this.model(t),r=this.model.config.sampling_rate;return new c.RawAudio(s.data,r)}async _call_text_to_spectrogram(e,{speaker_embeddings:t}){if(this.vocoder||(console.log("No vocoder specified, using default HifiGan vocoder."),this.vocoder=await o.AutoModel.from_pretrained(this.DEFAULT_VOCODER_ID,{dtype:"fp32"})),("string"==typeof t||t instanceof URL)&&(t=new Float32Array(await(await fetch(t)).arrayBuffer())),t instanceof Float32Array)t=new d.Tensor("float32",t,[1,t.length]);else if(!(t instanceof d.Tensor))throw new Error("Speaker embeddings must be a `Tensor`, `Float32Array`, `string`, or `URL`.");const{input_ids:s}=this.tokenizer(e,{padding:!0,truncation:!0}),{waveform:r}=await this.model.generate_speech(s,t,{vocoder:this.vocoder}),n=this.processor.feature_extractor.config.sampling_rate;return new c.RawAudio(r.data,n)}}class B extends h{constructor(e){super(e)}async _call(e){const t=await _(e),s=await this.processor(t),r=await this.model(s),o=[];for(const e of r.reconstruction){const t=e.squeeze().clamp_(0,1).mul_(255).round_().to("uint8");o.push(u.RawImage.fromTensor(t))}return o.length>1?o:o[0]}}class G extends h{constructor(e){super(e)}async _call(e){const t=await _(e),s=await this.processor(t),{predicted_depth:r}=await this.model(s),o=[];for(let e=0;e<t.length;++e){const s=r[e],[n,i]=s.dims.slice(-2),[a,l]=t[e].size,c=(await(0,d.interpolate_4d)(s.view(1,1,n,i),{size:[l,a],mode:"bilinear"})).view(l,a),_=c.min().item(),p=c.max().item(),m=c.sub(_).div_(p-_).mul_(255).to("uint8").unsqueeze(0),h=u.RawImage.fromTensor(m);o.push({predicted_depth:c,depth:h})}return o.length>1?o:o[0]}}const R=Object.freeze({"text-classification":{tokenizer:r.AutoTokenizer,pipeline:g,model:o.AutoModelForSequenceClassification,default:{model:"Xenova/distilbert-base-uncased-finetuned-sst-2-english"},type:"text"},"token-classification":{tokenizer:r.AutoTokenizer,pipeline:f,model:o.AutoModelForTokenClassification,default:{model:"Xenova/bert-base-multilingual-cased-ner-hrl"},type:"text"},"question-answering":{tokenizer:r.AutoTokenizer,pipeline:w,model:o.AutoModelForQuestionAnswering,default:{model:"Xenova/distilbert-base-cased-distilled-squad"},type:"text"},"fill-mask":{tokenizer:r.AutoTokenizer,pipeline:M,model:o.AutoModelForMaskedLM,default:{model:"Xenova/bert-base-uncased"},type:"text"},summarization:{tokenizer:r.AutoTokenizer,pipeline:b,model:o.AutoModelForSeq2SeqLM,default:{model:"Xenova/distilbart-cnn-6-6"},type:"text"},translation:{tokenizer:r.AutoTokenizer,pipeline:k,model:o.AutoModelForSeq2SeqLM,default:{model:"Xenova/t5-small"},type:"text"},"text2text-generation":{tokenizer:r.AutoTokenizer,pipeline:x,model:o.AutoModelForSeq2SeqLM,default:{model:"Xenova/flan-t5-small"},type:"text"},"text-generation":{tokenizer:r.AutoTokenizer,pipeline:v,model:o.AutoModelForCausalLM,default:{model:"Xenova/gpt2"},type:"text"},"zero-shot-classification":{tokenizer:r.AutoTokenizer,pipeline:T,model:o.AutoModelForSequenceClassification,default:{model:"Xenova/distilbert-base-uncased-mnli"},type:"text"},"audio-classification":{pipeline:C,model:o.AutoModelForAudioClassification,processor:n.AutoProcessor,default:{model:"Xenova/wav2vec2-base-superb-ks"},type:"audio"},"zero-shot-audio-classification":{tokenizer:r.AutoTokenizer,pipeline:S,model:o.AutoModel,processor:n.AutoProcessor,default:{model:"Xenova/clap-htsat-unfused"},type:"multimodal"},"automatic-speech-recognition":{tokenizer:r.AutoTokenizer,pipeline:E,model:[o.AutoModelForSpeechSeq2Seq,o.AutoModelForCTC],processor:n.AutoProcessor,default:{model:"Xenova/whisper-tiny.en"},type:"multimodal"},"text-to-audio":{tokenizer:r.AutoTokenizer,pipeline:V,model:[o.AutoModelForTextToWaveform,o.AutoModelForTextToSpectrogram],processor:[n.AutoProcessor,null],default:{model:"Xenova/speecht5_tts"},type:"text"},"image-to-text":{tokenizer:r.AutoTokenizer,pipeline:A,model:o.AutoModelForVision2Seq,processor:n.AutoProcessor,default:{model:"Xenova/vit-gpt2-image-captioning"},type:"multimodal"},"image-classification":{pipeline:L,model:o.AutoModelForImageClassification,processor:n.AutoProcessor,default:{model:"Xenova/vit-base-patch16-224"},type:"multimodal"},"image-segmentation":{pipeline:I,model:[o.AutoModelForImageSegmentation,o.AutoModelForSemanticSegmentation,o.AutoModelForUniversalSegmentation],processor:n.AutoProcessor,default:{model:"Xenova/detr-resnet-50-panoptic"},type:"multimodal"},"background-removal":{pipeline:z,model:[o.AutoModelForImageSegmentation,o.AutoModelForSemanticSegmentation,o.AutoModelForUniversalSegmentation],processor:n.AutoProcessor,default:{model:"Xenova/modnet"},type:"image"},"zero-shot-image-classification":{tokenizer:r.AutoTokenizer,pipeline:j,model:o.AutoModel,processor:n.AutoProcessor,default:{model:"Xenova/clip-vit-base-patch32"},type:"multimodal"},"object-detection":{pipeline:D,model:o.AutoModelForObjectDetection,processor:n.AutoProcessor,default:{model:"Xenova/detr-resnet-50"},type:"multimodal"},"zero-shot-object-detection":{tokenizer:r.AutoTokenizer,pipeline:O,model:o.AutoModelForZeroShotObjectDetection,processor:n.AutoProcessor,default:{model:"Xenova/owlvit-base-patch32"},type:"multimodal"},"document-question-answering":{tokenizer:r.AutoTokenizer,pipeline:N,model:o.AutoModelForDocumentQuestionAnswering,processor:n.AutoProcessor,default:{model:"Xenova/donut-base-finetuned-docvqa"},type:"multimodal"},"image-to-image":{pipeline:B,model:o.AutoModelForImageToImage,processor:n.AutoProcessor,default:{model:"Xenova/swin2SR-classical-sr-x2-64"},type:"image"},"depth-estimation":{pipeline:G,model:o.AutoModelForDepthEstimation,processor:n.AutoProcessor,default:{model:"Xenova/dpt-large"},type:"image"},"feature-extraction":{tokenizer:r.AutoTokenizer,pipeline:P,model:o.AutoModel,default:{model:"Xenova/all-MiniLM-L6-v2"},type:"text"},"image-feature-extraction":{processor:n.AutoProcessor,pipeline:F,model:[o.AutoModelForImageFeatureExtraction,o.AutoModel],default:{model:"Xenova/vit-base-patch16-224-in21k"},type:"image"}}),$=Object.freeze({"sentiment-analysis":"text-classification",ner:"token-classification",asr:"automatic-speech-recognition","text-to-speech":"text-to-audio",embeddings:"feature-extraction"});async function q(e,t=null,{progress_callback:s=null,config:r=null,cache_dir:o=null,local_files_only:n=!1,revision:i="main",device:l=null,dtype:c=null,subfolder:d="onnx",use_external_data_format:u=null,model_file_name:_=null,session_options:p={}}={}){e=$[e]??e;const m=R[e.split("_",1)[0]];if(!m)throw Error(`Unsupported pipeline: ${e}. Must be one of [${Object.keys(R)}]`);t||(t=m.default.model,console.log(`No model specified. Using default model: "${t}".`));const h={progress_callback:s,config:r,cache_dir:o,local_files_only:n,revision:i,device:l,dtype:c,subfolder:d,use_external_data_format:u,model_file_name:_,session_options:p},g=new Map([["tokenizer",m.tokenizer],["model",m.model],["processor",m.processor]]),f=await async function(e,t,s){const r=Object.create(null),o=[];for(const[n,i]of e.entries()){if(!i)continue;let e;e=Array.isArray(i)?new Promise((async(e,r)=>{let o;for(const n of i){if(null===n)return void e(null);try{return void e(await n.from_pretrained(t,s))}catch(e){if(e.message?.includes("Unsupported model type"))o=e;else{if(!e.message?.includes("Could not locate file"))return void r(e);o=e}}}r(o)})):i.from_pretrained(t,s),r[n]=e,o.push(e)}await Promise.all(o);for(const[e,t]of Object.entries(r))r[e]=await t;return r}(g,t,h);f.task=e,(0,a.dispatchCallback)(s,{status:"ready",task:e,model:t});return new(0,m.pipeline)(f)}},"./src/tokenizers.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{AlbertTokenizer:()=>ve,AutoTokenizer:()=>ft,BartTokenizer:()=>Ne,BertTokenizer:()=>ye,BlenderbotSmallTokenizer:()=>dt,BlenderbotTokenizer:()=>ct,BloomTokenizer:()=>Re,CLIPTokenizer:()=>nt,CamembertTokenizer:()=>Ie,CodeGenTokenizer:()=>ot,CodeLlamaTokenizer:()=>We,CohereTokenizer:()=>mt,ConvBertTokenizer:()=>Ee,DebertaTokenizer:()=>Fe,DebertaV2Tokenizer:()=>Ce,DistilBertTokenizer:()=>Le,ElectraTokenizer:()=>je,Ernie4_5_Tokenizer:()=>gt,EsmTokenizer:()=>Je,FalconTokenizer:()=>Xe,GPT2Tokenizer:()=>Oe,GPTNeoXTokenizer:()=>He,GemmaTokenizer:()=>Ke,Grok1Tokenizer:()=>Ze,HerbertTokenizer:()=>Se,LlamaTokenizer:()=>qe,M2M100Tokenizer:()=>st,MBart50Tokenizer:()=>Be,MBartTokenizer:()=>Ve,MPNetTokenizer:()=>Qe,MarianTokenizer:()=>at,MgpstrTokenizer:()=>ht,MobileBertTokenizer:()=>Te,NllbTokenizer:()=>tt,NougatTokenizer:()=>_t,PreTrainedTokenizer:()=>ke,Qwen2Tokenizer:()=>Ye,RoFormerTokenizer:()=>Ae,RobertaTokenizer:()=>Ge,SiglipTokenizer:()=>it,SpeechT5Tokenizer:()=>ut,SqueezeBertTokenizer:()=>Pe,T5Tokenizer:()=>De,TokenizerModel:()=>y,VitsTokenizer:()=>pt,Wav2Vec2CTCTokenizer:()=>lt,WhisperTokenizer:()=>rt,XLMRobertaTokenizer:()=>Ue,XLMTokenizer:()=>ze,is_chinese_char:()=>f});var r=s("./src/utils/generic.js"),o=s("./src/utils/core.js"),n=s("./src/utils/hub.js"),i=s("./src/utils/maths.js"),a=s("./src/utils/tensor.js"),l=s("./src/utils/data-structures.js"),c=s("./node_modules/@huggingface/jinja/dist/index.js"),d=s("./src/models/whisper/common_whisper.js");async function u(e,t){const s=await Promise.all([(0,n.getModelJSON)(e,"tokenizer.json",!0,t),(0,n.getModelJSON)(e,"tokenizer_config.json",!0,t)]);return null!==t.legacy&&(s[1].legacy=t.legacy),s}function _(e,t=!0){if(void 0!==e.Regex){let t=e.Regex.replace(/\\([#&~])/g,"$1");for(const[e,s]of b)t=t.replaceAll(e,s);return new RegExp(t,"gu")}if(void 0!==e.String){const s=(0,o.escapeRegExp)(e.String);return new RegExp(t?s:`(${s})`,"gu")}return console.warn("Unknown pattern type:",e),null}function p(e){return new Map(Object.entries(e))}function m(e){const t=e.dims;switch(t.length){case 1:return e.tolist();case 2:if(1!==t[0])throw new Error("Unable to decode tensor with `batch size !== 1`. Use `tokenizer.batch_decode(...)` for batched inputs.");return e.tolist()[0];default:throw new Error(`Expected tensor to have 1-2 dimensions, got ${t.length}.`)}}function h(e){return e.replace(/ \./g,".").replace(/ \?/g,"?").replace(/ \!/g,"!").replace(/ ,/g,",").replace(/ \' /g,"'").replace(/ n\'t/g,"n't").replace(/ \'m/g,"'m").replace(/ \'s/g,"'s").replace(/ \'ve/g,"'ve").replace(/ \'re/g,"'re")}function g(e){return e.replace(/\p{M}/gu,"")}function f(e){return e>=19968&&e<=40959||e>=13312&&e<=19903||e>=131072&&e<=173791||e>=173824&&e<=177983||e>=177984&&e<=178207||e>=178208&&e<=183983||e>=63744&&e<=64255||e>=194560&&e<=195103}const w="\\p{P}\\u0021-\\u002F\\u003A-\\u0040\\u005B-\\u0060\\u007B-\\u007E",M=new RegExp(`^[${w}]+$`,"gu"),x=".,!?…。，、।۔،",b=new Map([["(?i:'s|'t|'re|'ve|'m|'ll|'d)","(?:'([sS]|[tT]|[rR][eE]|[vV][eE]|[mM]|[lL][lL]|[dD]))"],[` ?[^(\\s|[${x}])]+`,` ?[^\\s${x}]+`]]);class k{constructor(e){this.content=e.content,this.id=e.id,this.single_word=e.single_word??!1,this.lstrip=e.lstrip??!1,this.rstrip=e.rstrip??!1,this.special=e.special??!1,this.normalized=e.normalized??null}}class y extends r.Callable{constructor(e){super(),this.config=e,this.vocab=[],this.tokens_to_ids=new Map,this.unk_token_id=void 0,this.unk_token=void 0,this.end_of_word_suffix=void 0,this.fuse_unk=this.config.fuse_unk??!1}static fromConfig(e,...t){switch(e.type){case"WordPiece":return new v(e);case"Unigram":return new T(e,...t);case"BPE":return new C(e);default:if(e.vocab)return Array.isArray(e.vocab)?new T(e,...t):Object.hasOwn(e,"continuing_subword_prefix")&&Object.hasOwn(e,"unk_token")?Object.hasOwn(e,"merges")?new C(e):new v(e):new S(e,...t);throw new Error(`Unknown TokenizerModel type: ${e.type}`)}}_call(e){return e=this.encode(e),this.fuse_unk&&(e=function(e,t,s){const r=[];let o=0;for(;o<e.length;)if(r.push(e[o]),(t.get(e[o])??s)===s)for(;++o<e.length&&(t.get(e[o])??s)===s;)t.get(r.at(-1))!==s&&(r[r.length-1]+=e[o]);else++o;return r}(e,this.tokens_to_ids,this.unk_token_id)),e}encode(e){throw Error("encode should be implemented in subclass.")}convert_tokens_to_ids(e){return e.map((e=>this.tokens_to_ids.get(e)??this.unk_token_id))}convert_ids_to_tokens(e){return e.map((e=>this.vocab[e]??this.unk_token))}}class v extends y{constructor(e){super(e),this.tokens_to_ids=p(e.vocab),this.unk_token_id=this.tokens_to_ids.get(e.unk_token),this.unk_token=e.unk_token,this.max_input_chars_per_word=e.max_input_chars_per_word??100,this.vocab=new Array(this.tokens_to_ids.size);for(const[e,t]of this.tokens_to_ids)this.vocab[t]=e}encode(e){const t=[];for(const s of e){const e=[...s];if(e.length>this.max_input_chars_per_word){t.push(this.unk_token);continue}let r=!1,o=0;const n=[];for(;o<e.length;){let t=e.length,s=null;for(;o<t;){let r=e.slice(o,t).join("");if(o>0&&(r=this.config.continuing_subword_prefix+r),this.tokens_to_ids.has(r)){s=r;break}--t}if(null===s){r=!0;break}n.push(s),o=t}r?t.push(this.unk_token):t.push(...n)}return t}}class T extends y{constructor(e,t){super(e);const s=e.vocab.length;this.vocab=new Array(s),this.scores=new Array(s);for(let t=0;t<s;++t)[this.vocab[t],this.scores[t]]=e.vocab[t];this.unk_token_id=e.unk_id,this.unk_token=this.vocab[e.unk_id],this.tokens_to_ids=new Map(this.vocab.map(((e,t)=>[e,t]))),this.bos_token=" ",this.bos_token_id=this.tokens_to_ids.get(this.bos_token),this.eos_token=t.eos_token,this.eos_token_id=this.tokens_to_ids.get(this.eos_token),this.unk_token=this.vocab[this.unk_token_id],this.minScore=(0,i.min)(this.scores)[0],this.unk_score=this.minScore-10,this.scores[this.unk_token_id]=this.unk_score,this.trie=new l.CharTrie,this.trie.extend(this.vocab),this.fuse_unk=!0}populateNodes(e){const t=e.chars;let s=0;for(;s<t.length;){let r=!1;const n=[],i=t.slice(s).join(""),a=this.trie.commonPrefixSearch(i);for(const t of a){n.push(t);const i=this.tokens_to_ids.get(t),a=this.scores[i],l=(0,o.len)(t);e.insert(s,l,a,i),r||1!==l||(r=!0)}r||e.insert(s,1,this.unk_score,this.unk_token_id),s+=1}}tokenize(e){const t=new l.TokenLattice(e,this.bos_token_id,this.eos_token_id);return this.populateNodes(t),t.tokens()}encode(e){const t=[];for(const s of e){const e=this.tokenize(s);t.push(...e)}return t}}const P=(()=>{const e=[...Array.from({length:"~".charCodeAt(0)-"!".charCodeAt(0)+1},((e,t)=>t+"!".charCodeAt(0))),...Array.from({length:"¬".charCodeAt(0)-"¡".charCodeAt(0)+1},((e,t)=>t+"¡".charCodeAt(0))),...Array.from({length:"ÿ".charCodeAt(0)-"®".charCodeAt(0)+1},((e,t)=>t+"®".charCodeAt(0)))],t=e.slice();let s=0;for(let r=0;r<256;++r)e.includes(r)||(e.push(r),t.push(256+s),s+=1);const r=t.map((e=>String.fromCharCode(e)));return Object.fromEntries(e.map(((e,t)=>[e,r[t]])))})(),F=(0,o.reverseDictionary)(P);class C extends y{constructor(e){super(e),this.tokens_to_ids=p(e.vocab),this.unk_token_id=this.tokens_to_ids.get(e.unk_token),this.unk_token=e.unk_token,this.vocab=new Array(this.tokens_to_ids.size);for(const[e,t]of this.tokens_to_ids)this.vocab[t]=e;const t=Array.isArray(e.merges[0]);this.merges=t?e.merges:e.merges.map((e=>e.split(" ",2))),this.bpe_ranks=new Map(this.merges.map(((e,t)=>[JSON.stringify(e),t]))),this.end_of_word_suffix=e.end_of_word_suffix,this.continuing_subword_suffix=e.continuing_subword_suffix??null,this.byte_fallback=this.config.byte_fallback??!1,this.byte_fallback&&(this.text_encoder=new TextEncoder),this.ignore_merges=this.config.ignore_merges??!1,this.max_length_to_cache=256,this.cache_capacity=1e4,this.cache=new l.LRUCache(this.cache_capacity)}clear_cache(){this.cache.clear()}bpe(e){if(0===e.length)return[];const t=this.cache.get(e);if(void 0!==t)return t;const s=Array.from(e);this.end_of_word_suffix&&(s[s.length-1]+=this.end_of_word_suffix);let r=[];if(s.length>1){const e=new l.PriorityQueue(((e,t)=>e.score<t.score));let t={token:s[0],bias:0,prev:null,next:null},o=t;for(let t=1;t<s.length;++t){const r={bias:t/s.length,token:s[t],prev:o,next:null};o.next=r,this._add_node(e,o),o=r}for(;!e.isEmpty();){const s=e.pop();if(s.deleted||!s.next||s.next.deleted)continue;if(s.deleted=!0,s.next.deleted=!0,s.prev){const e={...s.prev};s.prev.deleted=!0,s.prev=e,e.prev?e.prev.next=e:t=e}const r={token:s.token+s.next.token,bias:s.bias,prev:s.prev,next:s.next.next};r.prev?(r.prev.next=r,this._add_node(e,r.prev)):t=r,r.next&&(r.next.prev=r,this._add_node(e,r))}for(let e=t;null!==e;e=e.next)r.push(e.token)}else r=s;if(this.continuing_subword_suffix)for(let e=0;e<r.length-1;++e)r[e]+=this.continuing_subword_suffix;return e.length<this.max_length_to_cache&&this.cache.put(e,r),r}_add_node(e,t){const s=this.bpe_ranks.get(JSON.stringify([t.token,t.next.token]));void 0!==s&&(t.score=s+t.bias,e.push(t))}encode(e){const t=[];for(const s of e){if(this.ignore_merges&&this.tokens_to_ids.has(s)){t.push(s);continue}const e=this.bpe(s);for(const s of e)if(this.tokens_to_ids.has(s))t.push(s);else if(this.byte_fallback){const e=Array.from(this.text_encoder.encode(s)).map((e=>`<0x${e.toString(16).toUpperCase().padStart(2,"0")}>`));e.every((e=>this.tokens_to_ids.has(e)))?t.push(...e):t.push(this.unk_token)}else t.push(this.unk_token)}return t}}class S extends y{constructor(e,t){super(e),this.tokens_to_ids=p(t.target_lang?e.vocab[t.target_lang]:e.vocab),this.bos_token=t.bos_token,this.bos_token_id=this.tokens_to_ids.get(this.bos_token),this.eos_token=t.eos_token,this.eos_token_id=this.tokens_to_ids.get(this.eos_token),this.pad_token=t.pad_token,this.pad_token_id=this.tokens_to_ids.get(this.pad_token),this.unk_token=t.unk_token,this.unk_token_id=this.tokens_to_ids.get(this.unk_token),this.vocab=new Array(this.tokens_to_ids.size);for(const[e,t]of this.tokens_to_ids)this.vocab[t]=e}encode(e){return e}}class E extends r.Callable{constructor(e){super(),this.config=e}static fromConfig(e){if(null===e)return null;switch(e.type){case"BertNormalizer":return new R(e);case"Precompiled":return new me(e);case"Sequence":return new G(e);case"Replace":return new A(e);case"NFC":return new I(e);case"NFD":return new z(e);case"NFKC":return new j(e);case"NFKD":return new D(e);case"Strip":return new O(e);case"StripAccents":return new N(e);case"Lowercase":return new V(e);case"Prepend":return new B(e);default:throw new Error(`Unknown Normalizer type: ${e.type}`)}}normalize(e){throw Error("normalize should be implemented in subclass.")}_call(e){return this.normalize(e)}}class A extends E{normalize(e){const t=_(this.config.pattern);return null===t?e:e.replaceAll(t,this.config.content)}}class L extends E{form=void 0;normalize(e){return e=e.normalize(this.form)}}class I extends L{form="NFC"}class z extends L{form="NFD"}class j extends L{form="NFKC"}class D extends L{form="NFKD"}class O extends E{normalize(e){return this.config.strip_left&&this.config.strip_right?e=e.trim():(this.config.strip_left&&(e=e.trimStart()),this.config.strip_right&&(e=e.trimEnd())),e}}class N extends E{normalize(e){return e=g(e)}}class V extends E{normalize(e){return e=e.toLowerCase()}}class B extends E{normalize(e){return e=this.config.prepend+e}}class G extends E{constructor(e){super(e),this.normalizers=e.normalizers.map((e=>E.fromConfig(e)))}normalize(e){return this.normalizers.reduce(((e,t)=>t.normalize(e)),e)}}class R extends E{_tokenize_chinese_chars(e){const t=[];for(let s=0;s<e.length;++s){const r=e[s];f(r.charCodeAt(0))?(t.push(" "),t.push(r),t.push(" ")):t.push(r)}return t.join("")}stripAccents(e){return e.normalize("NFD").replace(/\p{Mn}/gu,"")}_is_control(e){switch(e){case"\t":case"\n":case"\r":return!1;default:return/^\p{Cc}|\p{Cf}|\p{Co}|\p{Cs}$/u.test(e)}}_clean_text(e){const t=[];for(const s of e){const e=s.charCodeAt(0);0===e||65533===e||this._is_control(s)||(/^\s$/.test(s)?t.push(" "):t.push(s))}return t.join("")}normalize(e){return this.config.clean_text&&(e=this._clean_text(e)),this.config.handle_chinese_chars&&(e=this._tokenize_chinese_chars(e)),this.config.lowercase?(e=e.toLowerCase(),!1!==this.config.strip_accents&&(e=this.stripAccents(e))):this.config.strip_accents&&(e=this.stripAccents(e)),e}}class $ extends r.Callable{static fromConfig(e){if(null===e)return null;switch(e.type){case"BertPreTokenizer":return new q(e);case"Sequence":return new he(e);case"Whitespace":return new ge(e);case"WhitespaceSplit":return new fe(e);case"Metaspace":return new _e(e);case"ByteLevel":return new W(e);case"Split":return new U(e);case"Punctuation":return new Q(e);case"Digits":return new X(e);case"Replace":return new we(e);default:throw new Error(`Unknown PreTokenizer type: ${e.type}`)}}pre_tokenize_text(e,t){throw Error("pre_tokenize_text should be implemented in subclass.")}pre_tokenize(e,t){return(Array.isArray(e)?e.map((e=>this.pre_tokenize_text(e,t))):this.pre_tokenize_text(e,t)).flat()}_call(e,t){return this.pre_tokenize(e,t)}}class q extends ${constructor(e){super(),this.pattern=new RegExp(`[^\\s${w}]+|[${w}]`,"gu")}pre_tokenize_text(e,t){return e.trim().match(this.pattern)||[]}}class W extends ${constructor(e){super(),this.config=e,this.add_prefix_space=this.config.add_prefix_space,this.trim_offsets=this.config.trim_offsets,this.use_regex=this.config.use_regex??!0,this.pattern=/'s|'t|'re|'ve|'m|'ll|'d| ?\p{L}+| ?\p{N}+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+/gu,this.byte_encoder=P,this.text_encoder=new TextEncoder}pre_tokenize_text(e,t){this.add_prefix_space&&!e.startsWith(" ")&&(e=" "+e);return(this.use_regex?e.match(this.pattern)||[]:[e]).map((e=>Array.from(this.text_encoder.encode(e),(e=>this.byte_encoder[e])).join("")))}}class U extends ${constructor(e){super(),this.config=e,this.pattern=_(this.config.pattern,this.config.invert)}pre_tokenize_text(e,t){return null===this.pattern?[]:this.config.invert?e.match(this.pattern)||[]:"removed"===this.config.behavior?.toLowerCase()?e.split(this.pattern).filter((e=>e)):function(e,t){const s=[];let r=0;for(const o of e.matchAll(t)){const t=o[0];r<o.index&&s.push(e.slice(r,o.index)),t.length>0&&s.push(t),r=o.index+t.length}return r<e.length&&s.push(e.slice(r)),s}(e,this.pattern)}}class Q extends ${constructor(e){super(),this.config=e,this.pattern=new RegExp(`[^${w}]+|[${w}]+`,"gu")}pre_tokenize_text(e,t){return e.match(this.pattern)||[]}}class X extends ${constructor(e){super(),this.config=e;const t="[^\\d]+|\\d"+(this.config.individual_digits?"":"+");this.pattern=new RegExp(t,"gu")}pre_tokenize_text(e,t){return e.match(this.pattern)||[]}}class H extends r.Callable{constructor(e){super(),this.config=e}static fromConfig(e){if(null===e)return null;switch(e.type){case"TemplateProcessing":return new K(e);case"ByteLevel":return new Z(e);case"RobertaProcessing":return new Y(e);case"BertProcessing":return new J(e);case"Sequence":return new ee(e);default:throw new Error(`Unknown PostProcessor type: ${e.type}`)}}post_process(e,...t){throw Error("post_process should be implemented in subclass.")}_call(e,...t){return this.post_process(e,...t)}}class J extends H{constructor(e){super(e),this.cls=e.cls[0],this.sep=e.sep[0]}post_process(e,t=null,{add_special_tokens:s=!0}={}){s&&(e=(0,o.mergeArrays)([this.cls],e,[this.sep]));let r=new Array(e.length).fill(0);if(null!==t){const n=s&&this instanceof Y?[this.sep]:[],i=s?[this.sep]:[];e=(0,o.mergeArrays)(e,n,t,i),r=(0,o.mergeArrays)(r,new Array(t.length+n.length+i.length).fill(1))}return{tokens:e,token_type_ids:r}}}class Y extends J{}class K extends H{constructor(e){super(e),this.single=e.single,this.pair=e.pair}post_process(e,t=null,{add_special_tokens:s=!0}={}){const r=null===t?this.single:this.pair;let n=[],i=[];for(const a of r)"SpecialToken"in a?s&&(n.push(a.SpecialToken.id),i.push(a.SpecialToken.type_id)):"Sequence"in a&&("A"===a.Sequence.id?(n=(0,o.mergeArrays)(n,e),i=(0,o.mergeArrays)(i,new Array(e.length).fill(a.Sequence.type_id))):"B"===a.Sequence.id&&(n=(0,o.mergeArrays)(n,t),i=(0,o.mergeArrays)(i,new Array(t.length).fill(a.Sequence.type_id))));return{tokens:n,token_type_ids:i}}}class Z extends H{post_process(e,t=null){return t&&(e=(0,o.mergeArrays)(e,t)),{tokens:e}}}class ee extends H{constructor(e){super(e),this.processors=e.processors.map((e=>H.fromConfig(e)))}post_process(e,t=null,s={}){let r;for(const o of this.processors)if(o instanceof Z){if(e=o.post_process(e).tokens,t){t=o.post_process(t).tokens}}else{const n=o.post_process(e,t,s);e=n.tokens,r=n.token_type_ids}return{tokens:e,token_type_ids:r}}}class te extends r.Callable{constructor(e){super(),this.config=e,this.added_tokens=[],this.end_of_word_suffix=null,this.trim_offsets=e.trim_offsets}static fromConfig(e){if(null===e)return null;switch(e.type){case"WordPiece":return new ie(e);case"Metaspace":return new pe(e);case"ByteLevel":return new ae(e);case"Replace":return new se(e);case"ByteFallback":return new re(e);case"Fuse":return new oe(e);case"Strip":return new ne(e);case"Sequence":return new ce(e);case"CTC":return new le(e);case"BPEDecoder":return new de(e);default:throw new Error(`Unknown Decoder type: ${e.type}`)}}_call(e){return this.decode(e)}decode(e){return this.decode_chain(e).join("")}decode_chain(e){throw Error("`decode_chain` should be implemented in subclass.")}}class se extends te{decode_chain(e){const t=_(this.config.pattern);return null===t?e:e.map((e=>e.replaceAll(t,this.config.content)))}}class re extends te{constructor(e){super(e),this.text_decoder=new TextDecoder}decode_chain(e){const t=[];let s=[];for(const r of e){let e=null;if(6===r.length&&r.startsWith("<0x")&&r.endsWith(">")){const t=parseInt(r.slice(3,5),16);isNaN(t)||(e=t)}if(null!==e)s.push(e);else{if(s.length>0){const e=this.text_decoder.decode(Uint8Array.from(s));t.push(e),s=[]}t.push(r)}}if(s.length>0){const e=this.text_decoder.decode(Uint8Array.from(s));t.push(e),s=[]}return t}}class oe extends te{decode_chain(e){return[e.join("")]}}class ne extends te{constructor(e){super(e),this.content=this.config.content,this.start=this.config.start,this.stop=this.config.stop}decode_chain(e){return e.map((e=>{let t=0;for(let s=0;s<this.start&&e[s]===this.content;++s)t=s+1;let s=e.length;for(let t=0;t<this.stop;++t){const r=e.length-t-1;if(e[r]!==this.content)break;s=r}return e.slice(t,s)}))}}class ie extends te{constructor(e){super(e),this.cleanup=e.cleanup}decode_chain(e){return e.map(((e,t)=>(0!==t&&(e=e.startsWith(this.config.prefix)?e.replace(this.config.prefix,""):" "+e),this.cleanup&&(e=h(e)),e)))}}class ae extends te{constructor(e){super(e),this.byte_decoder=F,this.text_decoder=new TextDecoder("utf-8",{fatal:!1,ignoreBOM:!0}),this.end_of_word_suffix=null}convert_tokens_to_string(e){const t=e.join(""),s=new Uint8Array([...t].map((e=>this.byte_decoder[e])));return this.text_decoder.decode(s)}decode_chain(e){const t=[];let s=[];for(const r of e)void 0!==this.added_tokens.find((e=>e.content===r))?(s.length>0&&(t.push(this.convert_tokens_to_string(s)),s=[]),t.push(r)):s.push(r);return s.length>0&&t.push(this.convert_tokens_to_string(s)),t}}class le extends te{constructor(e){super(e),this.pad_token=this.config.pad_token,this.word_delimiter_token=this.config.word_delimiter_token,this.cleanup=this.config.cleanup}convert_tokens_to_string(e){if(0===e.length)return"";const t=[e[0]];for(let s=1;s<e.length;++s)e[s]!==t.at(-1)&&t.push(e[s]);let s=t.filter((e=>e!==this.pad_token)).join("");return this.cleanup&&(s=h(s).replaceAll(this.word_delimiter_token," ").trim()),s}decode_chain(e){return[this.convert_tokens_to_string(e)]}}class ce extends te{constructor(e){super(e),this.decoders=e.decoders.map((e=>te.fromConfig(e)))}decode_chain(e){return this.decoders.reduce(((e,t)=>t.decode_chain(e)),e)}}class de extends te{constructor(e){super(e),this.suffix=this.config.suffix}decode_chain(e){return e.map(((t,s)=>t.replaceAll(this.suffix,s===e.length-1?"":" ")))}}class ue extends te{decode_chain(e){let t="";for(let s=1;s<e.length;s+=2)t+=e[s];return[t]}}class _e extends ${constructor(e){super(),this.addPrefixSpace=e.add_prefix_space,this.replacement=e.replacement,this.strRep=e.str_rep||this.replacement,this.prepend_scheme=e.prepend_scheme??"always"}pre_tokenize_text(e,{section_index:t}={}){let s=e.replaceAll(" ",this.strRep);return this.addPrefixSpace&&!s.startsWith(this.replacement)&&("always"===this.prepend_scheme||"first"===this.prepend_scheme&&0===t)&&(s=this.strRep+s),[s]}}class pe extends te{constructor(e){super(e),this.addPrefixSpace=e.add_prefix_space,this.replacement=e.replacement}decode_chain(e){const t=[];for(let s=0;s<e.length;++s){let r=e[s].replaceAll(this.replacement," ");this.addPrefixSpace&&0==s&&r.startsWith(" ")&&(r=r.substring(1)),t.push(r)}return t}}class me extends E{constructor(e){super(e),this.charsmap=e.precompiled_charsmap}normalize(e){if((e=(e=e.replace(/[\u0001-\u0008\u000B\u000E-\u001F\u007F\u008F\u009F]/gm,"")).replace(/[\u0009\u000A\u000C\u000D\u00A0\u1680\u2000-\u200F\u2028\u2029\u202F\u205F\u2581\u3000\uFEFF\uFFFD]/gm," ")).includes("～")){const t=e.split("～");e=t.map((e=>e.normalize("NFKC"))).join("～")}else e=e.normalize("NFKC");return e}}class he extends ${constructor(e){super(),this.tokenizers=e.pretokenizers.map((e=>$.fromConfig(e)))}pre_tokenize_text(e,t){return this.tokenizers.reduce(((e,s)=>s.pre_tokenize(e,t)),[e])}}class ge extends ${constructor(e){super()}pre_tokenize_text(e,t){return e.match(/\w+|[^\w\s]+/g)||[]}}class fe extends ${constructor(e){super()}pre_tokenize_text(e,t){return function(e){return e.match(/\S+/g)||[]}(e)}}class we extends ${constructor(e){super(),this.config=e,this.pattern=_(this.config.pattern),this.content=this.config.content}pre_tokenize_text(e,t){return null===this.pattern?[e]:[e.replaceAll(this.pattern,this.config.content)]}}const Me=["bos_token","eos_token","unk_token","sep_token","pad_token","cls_token","mask_token"];function xe(e,t,s,r){for(const n of Object.keys(e)){const i=t-e[n].length,a=s(n),l=new Array(i).fill(a);e[n]="right"===r?(0,o.mergeArrays)(e[n],l):(0,o.mergeArrays)(l,e[n])}}function be(e,t){for(const s of Object.keys(e))e[s].length=t}class ke extends r.Callable{return_token_type_ids=!1;padding_side="right";constructor(e,t){super(),this.config=t,this.normalizer=E.fromConfig(e.normalizer),this.pre_tokenizer=$.fromConfig(e.pre_tokenizer),this.model=y.fromConfig(e.model,t),this.post_processor=H.fromConfig(e.post_processor),this.decoder=te.fromConfig(e.decoder),this.special_tokens=[],this.all_special_ids=[],this.added_tokens=[];for(const t of e.added_tokens){const e=new k(t);this.added_tokens.push(e),this.model.tokens_to_ids.set(e.content,e.id),this.model.vocab[e.id]=e.content,e.special&&(this.special_tokens.push(e.content),this.all_special_ids.push(e.id))}if(this.additional_special_tokens=t.additional_special_tokens??[],this.special_tokens.push(...this.additional_special_tokens),this.special_tokens=[...new Set(this.special_tokens)],this.decoder&&(this.decoder.added_tokens=this.added_tokens,this.decoder.end_of_word_suffix=this.model.end_of_word_suffix),this.added_tokens_splitter=new l.DictionarySplitter(this.added_tokens.map((e=>e.content))),this.added_tokens_map=new Map(this.added_tokens.map((e=>[e.content,e]))),this.mask_token=this.getToken("mask_token"),this.mask_token_id=this.model.tokens_to_ids.get(this.mask_token),this.pad_token=this.getToken("pad_token","eos_token"),this.pad_token_id=this.model.tokens_to_ids.get(this.pad_token),this.sep_token=this.getToken("sep_token"),this.sep_token_id=this.model.tokens_to_ids.get(this.sep_token),this.unk_token=this.getToken("unk_token"),this.unk_token_id=this.model.tokens_to_ids.get(this.unk_token),this.bos_token=this.getToken("bos_token"),this.bos_token_id=this.model.tokens_to_ids.get(this.bos_token),this.eos_token=this.getToken("eos_token"),this.eos_token_id=this.model.tokens_to_ids.get(this.eos_token),this.model_max_length=t.model_max_length,this.remove_space=t.remove_space,this.clean_up_tokenization_spaces=t.clean_up_tokenization_spaces??!0,this.do_lowercase_and_remove_accent=t.do_lowercase_and_remove_accent??!1,t.padding_side&&(this.padding_side=t.padding_side),this.legacy=!1,this.chat_template=t.chat_template??null,Array.isArray(this.chat_template)){const e=Object.create(null);for(const{name:t,template:s}of this.chat_template){if("string"!=typeof t||"string"!=typeof s)throw new Error('Chat template must be a list of objects with "name" and "template" properties');e[t]=s}this.chat_template=e}this._compiled_template_cache=new Map}getToken(...e){for(const t of e){const e=this.config[t];if(e){if("object"==typeof e){if("AddedToken"===e.__type)return e.content;throw Error(`Unknown token: ${e}`)}return e}}return null}static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:r=null,local_files_only:o=!1,revision:n="main",legacy:i=null}={}){return new this(...await u(e,{progress_callback:t,config:s,cache_dir:r,local_files_only:o,revision:n,legacy:i}))}_call(e,{text_pair:t=null,add_special_tokens:s=!0,padding:r=!1,truncation:o=null,max_length:n=null,return_tensor:l=!0,return_token_type_ids:c=null}={}){const d=Array.isArray(e);let u;if(d){if(0===e.length)throw Error("text array must be non-empty");if(null!==t){if(!Array.isArray(t))throw Error("text_pair must also be an array");if(e.length!==t.length)throw Error("text and text_pair must have the same length");u=e.map(((e,r)=>this._encode_plus(e,{text_pair:t[r],add_special_tokens:s,return_token_type_ids:c})))}else u=e.map((e=>this._encode_plus(e,{add_special_tokens:s,return_token_type_ids:c})))}else{if(null==e)throw Error("text may not be null or undefined");if(Array.isArray(t))throw Error("When specifying `text_pair`, since `text` is a string, `text_pair` must also be a string (i.e., not an array).");u=[this._encode_plus(e,{text_pair:t,add_special_tokens:s,return_token_type_ids:c})]}if(null===n?n=this.model_max_length:null===o&&(!0===r?(console.warn("`max_length` is ignored when `padding: true` and there is no truncation strategy. To pad to max length, use `padding: 'max_length'`."),n=this.model_max_length):!1===r&&(console.warn("Truncation was not explicitly activated but `max_length` is provided a specific value, please use `truncation: true` to explicitly truncate examples to max length."),o=!0)),!0===r&&(n=Math.min((0,i.max)(u.map((e=>e.input_ids.length)))[0],n??1/0)),n=Math.min(n,this.model_max_length??1/0),r||o)for(let e=0;e<u.length;++e)u[e].input_ids.length!==n&&(u[e].input_ids.length>n?o&&be(u[e],n):r&&xe(u[e],n,(e=>"input_ids"===e?this.pad_token_id:0),this.padding_side));const _={};if(l){if((!r||!o)&&u.some((e=>{for(const t of Object.keys(e))if(e[t].length!==u[0][t]?.length)return!0;return!1})))throw Error("Unable to create tensor, you should probably activate truncation and/or padding with 'padding=true' and 'truncation=true' to have batched tensors with the same length.");const e=[u.length,u[0].input_ids.length];for(const t of Object.keys(u[0]))_[t]=new a.Tensor("int64",BigInt64Array.from(u.flatMap((e=>e[t])).map(BigInt)),e)}else{for(const e of Object.keys(u[0]))_[e]=u.map((t=>t[e]));if(!d)for(const e of Object.keys(_))_[e]=_[e][0]}return _}_encode_text(e){if(null===e)return null;const t=this.added_tokens_splitter.split(e);for(let e=0;e<t.length;++e){const s=this.added_tokens_map.get(t[e]);s&&(s.lstrip&&e>0&&(t[e-1]=t[e-1].trimEnd()),s.rstrip&&e<t.length-1&&(t[e+1]=t[e+1].trimStart()))}const s=t.flatMap(((e,t)=>{if(0===e.length)return[];if(this.added_tokens_map.has(e))return[e];if(!0===this.remove_space&&(e=e.trim().split(/\s+/).join(" ")),this.do_lowercase_and_remove_accent&&(e=function(e){return g(e.toLowerCase())}(e)),null!==this.normalizer&&(e=this.normalizer(e)),0===e.length)return[];const s=null!==this.pre_tokenizer?this.pre_tokenizer(e,{section_index:t}):[e];return this.model(s)}));return s}_encode_plus(e,{text_pair:t=null,add_special_tokens:s=!0,return_token_type_ids:r=null}={}){const{tokens:o,token_type_ids:n}=this._tokenize_helper(e,{pair:t,add_special_tokens:s}),i=this.model.convert_tokens_to_ids(o),a={input_ids:i,attention_mask:new Array(i.length).fill(1)};return(r??this.return_token_type_ids)&&n&&(a.token_type_ids=n),a}_tokenize_helper(e,{pair:t=null,add_special_tokens:s=!1}={}){const r=this._encode_text(e),n=this._encode_text(t);return this.post_processor?this.post_processor(r,n,{add_special_tokens:s}):{tokens:(0,o.mergeArrays)(r??[],n??[])}}tokenize(e,{pair:t=null,add_special_tokens:s=!1}={}){return this._tokenize_helper(e,{pair:t,add_special_tokens:s}).tokens}encode(e,{text_pair:t=null,add_special_tokens:s=!0,return_token_type_ids:r=null}={}){return this._encode_plus(e,{text_pair:t,add_special_tokens:s,return_token_type_ids:r}).input_ids}batch_decode(e,t={}){return e instanceof a.Tensor&&(e=e.tolist()),e.map((e=>this.decode(e,t)))}decode(e,t={}){if(e instanceof a.Tensor&&(e=m(e)),!Array.isArray(e)||0===e.length||!(0,o.isIntegralNumber)(e[0]))throw Error("token_ids must be a non-empty array of integers.");return this.decode_single(e,t)}decode_single(e,{skip_special_tokens:t=!1,clean_up_tokenization_spaces:s=null}){let r=this.model.convert_ids_to_tokens(e);t&&(r=r.filter((e=>!this.special_tokens.includes(e))));let o=this.decoder?this.decoder(r):r.join(" ");return this.decoder&&this.decoder.end_of_word_suffix&&(o=o.replaceAll(this.decoder.end_of_word_suffix," "),t&&(o=o.trim())),(s??this.clean_up_tokenization_spaces)&&(o=h(o)),o}get_chat_template({chat_template:e=null,tools:t=null}={}){if(this.chat_template&&"object"==typeof this.chat_template){const s=this.chat_template;if(null!==e&&Object.hasOwn(s,e))e=s[e];else if(null===e)if(null!==t&&"tool_use"in s)e=s.tool_use;else{if(!("default"in s))throw Error(`This model has multiple chat templates with no default specified! Please either pass a chat template or the name of the template you wish to use to the 'chat_template' argument. Available template names are ${Object.keys(s).sort()}.`);e=s.default}}else if(null===e){if(!this.chat_template)throw Error("Cannot use apply_chat_template() because tokenizer.chat_template is not set and no template argument was passed! For information about writing templates and setting the tokenizer.chat_template attribute, please see the documentation at https://huggingface.co/docs/transformers/main/en/chat_templating");e=this.chat_template}return e}apply_chat_template(e,{tools:t=null,documents:s=null,chat_template:r=null,add_generation_prompt:o=!1,tokenize:n=!0,padding:i=!1,truncation:a=!1,max_length:l=null,return_tensor:d=!0,return_dict:u=!1,tokenizer_kwargs:_={},...p}={}){if("string"!=typeof(r=this.get_chat_template({chat_template:r,tools:t})))throw Error("chat_template must be a string, but got "+typeof r);let m=this._compiled_template_cache.get(r);void 0===m&&(m=new c.Template(r),this._compiled_template_cache.set(r,m));const h=Object.create(null);for(const e of Me){const t=this.getToken(e);t&&(h[e]=t)}const g=m.render({messages:e,add_generation_prompt:o,tools:t,documents:s,...h,...p});if(n){const e=this._call(g,{add_special_tokens:!1,padding:i,truncation:a,max_length:l,return_tensor:d,..._});return u?e:e.input_ids}return g}}class ye extends ke{return_token_type_ids=!0}class ve extends ke{return_token_type_ids=!0}class Te extends ke{return_token_type_ids=!0}class Pe extends ke{return_token_type_ids=!0}class Fe extends ke{return_token_type_ids=!0}class Ce extends ke{return_token_type_ids=!0}class Se extends ke{return_token_type_ids=!0}class Ee extends ke{return_token_type_ids=!0}class Ae extends ke{return_token_type_ids=!0}class Le extends ke{}class Ie extends ke{}class ze extends ke{return_token_type_ids=!0;constructor(e,t){super(e,t),console.warn('WARNING: `XLMTokenizer` is not yet supported by Hugging Face\'s "fast" tokenizers library. Therefore, you may experience slightly inaccurate results.')}}class je extends ke{return_token_type_ids=!0}class De extends ke{}class Oe extends ke{}class Ne extends ke{}class Ve extends ke{constructor(e,t){super(e,t),this.languageRegex=/^[a-z]{2}_[A-Z]{2}$/,this.language_codes=this.special_tokens.filter((e=>this.languageRegex.test(e))),this.lang_to_token=e=>e}_build_translation_inputs(e,t,s){return et(this,e,t,s)}}class Be extends Ve{}class Ge extends ke{}class Re extends ke{}const $e="▁";class qe extends ke{padding_side="left";constructor(e,t){super(e,t),this.legacy=t.legacy??!0,this.legacy||(this.normalizer=null,this.pre_tokenizer=new _e({replacement:$e,add_prefix_space:!0,prepend_scheme:"first"}))}_encode_text(e){if(null===e)return null;if(this.legacy||0===e.length)return super._encode_text(e);let t=super._encode_text($e+e.replaceAll($e," "));return t.length>1&&t[0]===$e&&this.special_tokens.includes(t[1])&&(t=t.slice(1)),t}}class We extends ke{}class Ue extends ke{}class Qe extends ke{}class Xe extends ke{}class He extends ke{}class Je extends ke{}class Ye extends ke{}class Ke extends ke{}class Ze extends ke{}function et(e,t,s,r){if(!("language_codes"in e)||!Array.isArray(e.language_codes))throw new Error("Tokenizer must have `language_codes` attribute set and it should be an array of language ids.");if(!("languageRegex"in e&&e.languageRegex instanceof RegExp))throw new Error("Tokenizer must have `languageRegex` attribute set and it should be a regular expression.");if(!("lang_to_token"in e)||"function"!=typeof e.lang_to_token)throw new Error("Tokenizer must have `lang_to_token` attribute set and it should be a function.");const o=r.src_lang,n=r.tgt_lang;if(!e.language_codes.includes(n))throw new Error(`Target language code "${n}" is not valid. Must be one of: {${e.language_codes.join(", ")}}`);if(void 0!==o){if(!e.language_codes.includes(o))throw new Error(`Source language code "${o}" is not valid. Must be one of: {${e.language_codes.join(", ")}}`);for(const t of e.post_processor.config.single)if("SpecialToken"in t&&e.languageRegex.test(t.SpecialToken.id)){t.SpecialToken.id=e.lang_to_token(o);break}}return r.forced_bos_token_id=e.model.convert_tokens_to_ids([e.lang_to_token(n)])[0],e._call(t,s)}class tt extends ke{constructor(e,t){super(e,t),this.languageRegex=/^[a-z]{3}_[A-Z][a-z]{3}$/,this.language_codes=this.special_tokens.filter((e=>this.languageRegex.test(e))),this.lang_to_token=e=>e}_build_translation_inputs(e,t,s){return et(this,e,t,s)}}class st extends ke{constructor(e,t){super(e,t),this.languageRegex=/^__[a-z]{2,3}__$/,this.language_codes=this.special_tokens.filter((e=>this.languageRegex.test(e))).map((e=>e.slice(2,-2))),this.lang_to_token=e=>`__${e}__`}_build_translation_inputs(e,t,s){return et(this,e,t,s)}}class rt extends ke{get timestamp_begin(){return this.model.convert_tokens_to_ids(["<|notimestamps|>"])[0]+1}_decode_asr(e,{return_timestamps:t=!1,return_language:s=!1,time_precision:r=null,force_full_sequences:o=!0}={}){if(null===r)throw Error("Must specify time_precision");let n=null;const a="word"===t;function l(){return{language:n,timestamp:[null,null],text:""}}const c=[];let u=l(),_=0;const p=this.timestamp_begin,m=p+1500;let h=[],g=[],f=!1,w=null;const x=new Set(this.all_special_ids);for(const s of e){const e=s.tokens,o=a?s.token_timestamps:null;let b=null,k=p;if("stride"in s){const[t,o,n]=s.stride;if(_-=o,w=t-n,o&&(k=o/r+p),n)for(let t=e.length-1;t>=0;--t){const s=Number(e[t]);if(s>=p){if(null!==b&&(s-p)*r<w)break;b=s}}}let y=[],v=[];for(let s=0;s<e.length;++s){const w=Number(e[s]);if(x.has(w)){const e=this.decode([w]),s=d.WHISPER_LANGUAGE_MAPPING.get(e.slice(2,-2));if(void 0!==s){if(null!==n&&s!==n&&!t){h.push(y);const e=this.findLongestCommonSequence(h)[0],t=this.decode(e);u.text=t,c.push(u),h=[],y=[],u=l()}n=u.language=s}}else if(w>=p&&w<=m){const e=(w-p)*r+_,t=(0,i.round)(e,2);if(null!==b&&w>=b)f=!0;else if(f||h.length>0&&w<k)f=!1;else if(null===u.timestamp[0])u.timestamp[0]=t;else if(t===u.timestamp[0]);else{u.timestamp[1]=t,h.push(y),a&&g.push(v);const[e,s]=this.findLongestCommonSequence(h,g),r=this.decode(e);u.text=r,a&&(u.words=this.collateWordTimestamps(e,s,n)),c.push(u),h=[],y=[],g=[],v=[],u=l()}}else if(y.push(w),a){let e,t=(0,i.round)(o[s]+_,2);if(s+1<o.length){e=(0,i.round)(o[s+1]+_,2);const n=this.decode([w]);M.test(n)&&(e=(0,i.round)(Math.min(t+r,e),2))}else e=null;v.push([t,e])}}if("stride"in s){const[e,t,r]=s.stride;_+=e-r}y.length>0?(h.push(y),a&&g.push(v)):h.every((e=>0===e.length))&&(u=l(),h=[],y=[],g=[],v=[])}if(h.length>0){if(o&&t)throw new Error("Whisper did not predict an ending timestamp, which can happen if audio is cut off in the middle of a word. Also make sure WhisperTimeStampLogitsProcessor was used during generation.");const[e,s]=this.findLongestCommonSequence(h,g),r=this.decode(e);u.text=r,a&&(u.words=this.collateWordTimestamps(e,s,n)),c.push(u)}let b=Object.create(null);const k=c.map((e=>e.text)).join("");if(t||s){for(let e=0;e<c.length;++e){const r=c[e];t||delete r.timestamp,s||delete r.language}if(a){const e=[];for(const t of c)for(const s of t.words)e.push(s);b={chunks:e}}else b={chunks:c}}return[k,b]}findLongestCommonSequence(e,t=null){let s=e[0],r=s.length,o=[];const n=Array.isArray(t)&&t.length>0;let i=n?[]:null,a=n?t[0]:null;for(let l=1;l<e.length;++l){const c=e[l];let d=0,u=[r,r,0,0];const _=c.length;for(let e=1;e<r+_;++e){const o=Math.max(0,r-e),i=Math.min(r,r+_-e),p=s.slice(o,i),m=Math.max(0,e-r),h=Math.min(_,e),g=c.slice(m,h);if(p.length!==g.length)throw new Error("There is a bug within whisper `decode_asr` function, please report it. Dropping to prevent bad inference.");let f;f=n?p.filter(((e,s)=>e===g[s]&&a[o+s]<=t[l][m+s])).length:p.filter(((e,t)=>e===g[t])).length;const w=f/e+e/1e4;f>1&&w>d&&(d=w,u=[o,i,m,h])}const[p,m,h,g]=u,f=Math.floor((m+p)/2),w=Math.floor((g+h)/2);o.push(...s.slice(0,f)),s=c.slice(w),r=s.length,n&&(i.push(...a.slice(0,f)),a=t[l].slice(w))}return o.push(...s),n?(i.push(...a),[o,i]):[o,[]]}collateWordTimestamps(e,t,s){const[r,o,n]=this.combineTokensIntoWords(e,s),i=[];for(let e=0;e<r.length;++e){const s=n[e];i.push({text:r[e],timestamp:[t[s.at(0)][0],t[s.at(-1)][1]]})}return i}combineTokensIntoWords(e,t,s="\"'“¡¿([{-",r="\"'.。,，!！?？:：”)]}、"){let o,n,i;return["chinese","japanese","thai","lao","myanmar"].includes(t=t??"english")?[o,n,i]=this.splitTokensOnUnicode(e):[o,n,i]=this.splitTokensOnSpaces(e),this.mergePunctuations(o,n,i,s,r)}decode(e,t){let s;return t?.decode_with_timestamps?(e instanceof a.Tensor&&(e=m(e)),s=this.decodeWithTimestamps(e,t)):s=super.decode(e,t),s}decodeWithTimestamps(e,t){const s=t?.time_precision??.02,r=Array.from(this.all_special_ids).at(-1)+1;let o=[[]];for(let t of e)if(t=Number(t),t>=r){const e=((t-r)*s).toFixed(2);o.push(`<|${e}|>`),o.push([])}else o[o.length-1].push(t);return o=o.map((e=>"string"==typeof e?e:super.decode(e,t))),o.join("")}splitTokensOnUnicode(e){const t=this.decode(e,{decode_with_timestamps:!0}),s=[],r=[],o=[];let n=[],i=[],a=0;for(let l=0;l<e.length;++l){const c=e[l];n.push(c),i.push(l);const d=this.decode(n,{decode_with_timestamps:!0});d.includes("�")&&"�"!==t[a+d.indexOf("�")]||(s.push(d),r.push(n),o.push(i),n=[],i=[],a+=d.length)}return[s,r,o]}splitTokensOnSpaces(e){const[t,s,r]=this.splitTokensOnUnicode(e),o=[],n=[],i=[],a=new RegExp(`^[${w}]$`,"gu");for(let e=0;e<t.length;++e){const l=t[e],c=s[e],d=r[e],u=c[0]>=this.model.tokens_to_ids.get("<|endoftext|>"),_=l.startsWith(" "),p=l.trim(),m=a.test(p);if(u||_||m||0===o.length)o.push(l),n.push(c),i.push(d);else{const e=o.length-1;o[e]+=l,n[e].push(...c),i[e].push(...d)}}return[o,n,i]}mergePunctuations(e,t,s,r,n){const i=structuredClone(e),a=structuredClone(t),l=structuredClone(s);let c=i.length-2,d=i.length-1;for(;c>=0;)i[c].startsWith(" ")&&r.includes(i[c].trim())?(i[d]=i[c]+i[d],a[d]=(0,o.mergeArrays)(a[c],a[d]),l[d]=(0,o.mergeArrays)(l[c],l[d]),i[c]="",a[c]=[],l[c]=[]):d=c,--c;for(c=0,d=1;d<i.length;)!i[c].endsWith(" ")&&n.includes(i[d])?(i[c]+=i[d],a[c]=(0,o.mergeArrays)(a[c],a[d]),l[c]=(0,o.mergeArrays)(l[c],l[d]),i[d]="",a[d]=[],l[d]=[]):c=d,++d;return[i.filter((e=>e)),a.filter((e=>e.length>0)),l.filter((e=>e.length>0))]}}class ot extends ke{}class nt extends ke{}class it extends ke{}class at extends ke{constructor(e,t){super(e,t),this.languageRegex=/^(>>\w+<<)\s*/g,this.supported_language_codes=this.model.vocab.filter((e=>this.languageRegex.test(e))),console.warn('WARNING: `MarianTokenizer` is not yet supported by Hugging Face\'s "fast" tokenizers library. Therefore, you may experience slightly inaccurate results.')}_encode_text(e){if(null===e)return null;const[t,...s]=e.trim().split(this.languageRegex);if(0===s.length)return super._encode_text(t);if(2===s.length){const[e,t]=s;return this.supported_language_codes.includes(e)||console.warn(`Unsupported language code "${e}" detected, which may lead to unexpected behavior. Should be one of: ${JSON.stringify(this.supported_language_codes)}`),(0,o.mergeArrays)([e],super._encode_text(t))}}}class lt extends ke{}class ct extends ke{}class dt extends ke{}class ut extends ke{}class _t extends ke{}class pt extends ke{constructor(e,t){super(e,t),this.decoder=new ue({})}}class mt extends ke{}class ht extends ke{}class gt extends ke{}class ft{static TOKENIZER_CLASS_MAPPING={T5Tokenizer:De,DistilBertTokenizer:Le,CamembertTokenizer:Ie,DebertaTokenizer:Fe,DebertaV2Tokenizer:Ce,BertTokenizer:ye,HerbertTokenizer:Se,ConvBertTokenizer:Ee,RoFormerTokenizer:Ae,XLMTokenizer:ze,ElectraTokenizer:je,MobileBertTokenizer:Te,SqueezeBertTokenizer:Pe,AlbertTokenizer:ve,GPT2Tokenizer:Oe,BartTokenizer:Ne,MBartTokenizer:Ve,MBart50Tokenizer:Be,RobertaTokenizer:Ge,WhisperTokenizer:rt,CodeGenTokenizer:ot,CLIPTokenizer:nt,SiglipTokenizer:it,MarianTokenizer:at,BloomTokenizer:Re,NllbTokenizer:tt,M2M100Tokenizer:st,LlamaTokenizer:qe,CodeLlamaTokenizer:We,XLMRobertaTokenizer:Ue,MPNetTokenizer:Qe,FalconTokenizer:Xe,GPTNeoXTokenizer:He,EsmTokenizer:Je,Wav2Vec2CTCTokenizer:lt,BlenderbotTokenizer:ct,BlenderbotSmallTokenizer:dt,SpeechT5Tokenizer:ut,NougatTokenizer:_t,VitsTokenizer:pt,Qwen2Tokenizer:Ye,GemmaTokenizer:Ke,Grok1Tokenizer:Ze,CohereTokenizer:mt,MgpstrTokenizer:ht,Ernie4_5_Tokenizer:gt,PreTrainedTokenizer:ke};static async from_pretrained(e,{progress_callback:t=null,config:s=null,cache_dir:r=null,local_files_only:o=!1,revision:n="main",legacy:i=null}={}){const[a,l]=await u(e,{progress_callback:t,config:s,cache_dir:r,local_files_only:o,revision:n,legacy:i}),c=l.tokenizer_class?.replace(/Fast$/,"")??"PreTrainedTokenizer";let d=this.TOKENIZER_CLASS_MAPPING[c];return d||(console.warn(`Unknown tokenizer class "${c}", attempting to construct from base class.`),d=ke),new d(a,l)}}},"./src/utils/audio.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{RawAudio:()=>k,hamming:()=>_,hanning:()=>u,mel_filter_bank:()=>f,read_audio:()=>c,spectrogram:()=>M,window_function:()=>x});var r=s("./src/utils/hub.js"),o=s("./src/utils/maths.js"),n=s("./src/utils/core.js"),i=s("./src/env.js"),a=s("./src/utils/tensor.js"),l=s("node:fs");async function c(e,t){if("undefined"==typeof AudioContext)throw Error("Unable to load audio from path/URL since `AudioContext` is not available in your environment. Instead, audio data should be passed directly to the pipeline/processor. For more information and some example code, see https://huggingface.co/docs/transformers.js/guides/node-audio-processing.");const s=await(await(0,r.getFile)(e)).arrayBuffer(),o=new AudioContext({sampleRate:t});void 0===t&&console.warn(`No sampling rate provided, using default of ${o.sampleRate}Hz.`);const n=await o.decodeAudioData(s);let i;if(2===n.numberOfChannels){const e=Math.sqrt(2),t=n.getChannelData(0),s=n.getChannelData(1);i=new Float32Array(t.length);for(let r=0;r<n.length;++r)i[r]=e*(t[r]+s[r])/2}else i=n.getChannelData(0);return i}function d(e,t){if(e<1)return new Float64Array;if(1===e)return new Float64Array([1]);const s=1-t,r=2*Math.PI/(e-1),o=new Float64Array(e);for(let n=0;n<e;++n)o[n]=t-s*Math.cos(n*r);return o}function u(e){return d(e,.5)}function _(e){return d(e,.54)}const p={htk:e=>2595*Math.log10(1+e/700),kaldi:e=>1127*Math.log(1+e/700),slaney:(e,t=1e3,s=15,r=27/Math.log(6.4))=>e>=t?s+Math.log(e/t)*r:3*e/200};function m(e,t="htk"){const s=p[t];if(!s)throw new Error('mel_scale should be one of "htk", "slaney" or "kaldi".');return"number"==typeof e?s(e):e.map((e=>s(e)))}const h={htk:e=>700*(10**(e/2595)-1),kaldi:e=>700*(Math.exp(e/1127)-1),slaney:(e,t=1e3,s=15,r=Math.log(6.4)/27)=>e>=s?t*Math.exp(r*(e-s)):200*e/3};function g(e,t,s){const r=(t-e)/(s-1);return Float64Array.from({length:s},((t,s)=>e+r*s))}function f(e,t,s,r,o,n=null,i="htk",a=!1){if(null!==n&&"slaney"!==n)throw new Error('norm must be one of null or "slaney"');if(e<2)throw new Error(`Require num_frequency_bins: ${e} >= 2`);if(s>r)throw new Error(`Require min_frequency: ${s} <= max_frequency: ${r}`);const l=g(m(s,i),m(r,i),t+2);let c,d=function(e,t="htk"){const s=h[t];if(!s)throw new Error('mel_scale should be one of "htk", "slaney" or "kaldi".');return"number"==typeof e?s(e):e.map((e=>s(e)))}(l,i);if(a){const t=o/(2*(e-1));c=m(Float64Array.from({length:e},((e,s)=>s*t)),i),d=l}else c=g(0,Math.floor(o/2),e);const u=function(e,t){const s=Float64Array.from({length:t.length-1},((e,s)=>t[s+1]-t[s])),r=Array.from({length:e.length},(()=>new Array(t.length)));for(let s=0;s<e.length;++s){const o=r[s];for(let r=0;r<t.length;++r)o[r]=t[r]-e[s]}const o=t.length-2,n=Array.from({length:o},(()=>new Array(e.length)));for(let t=0;t<e.length;++t){const e=r[t];for(let r=0;r<o;++r){const o=-e[r]/s[r],i=e[r+2]/s[r+1];n[r][t]=Math.max(0,Math.min(o,i))}}return n}(c,d);if(null!==n&&"slaney"===n)for(let s=0;s<t;++s){const t=u[s],r=2/(d[s+2]-d[s]);for(let s=0;s<e;++s)t[s]*=r}return u}function w(e,t,s,r,n){if(s<=0)throw new Error("reference must be greater than zero");if(r<=0)throw new Error("min_value must be greater than zero");s=Math.max(r,s);const i=Math.log10(s);for(let s=0;s<e.length;++s)e[s]=t*Math.log10(Math.max(r,e[s])-i);if(null!==n){if(n<=0)throw new Error("db_range must be greater than zero");const t=(0,o.max)(e)[0]-n;for(let s=0;s<e.length;++s)e[s]=Math.max(e[s],t)}return e}async function M(e,t,s,r,{fft_length:i=null,power:l=1,center:c=!0,pad_mode:d="reflect",onesided:u=!0,preemphasis:_=null,preemphasis_htk_flavor:p=!0,mel_filters:m=null,mel_floor:h=1e-10,log_mel:g=null,reference:f=1,min_value:M=1e-10,db_range:x=null,remove_dc_offset:b=null,min_num_frames:k=null,max_num_frames:y=null,do_pad:v=!0,transpose:T=!1}={}){const P=t.length;if(null===i&&(i=s),s>i)throw Error(`frame_length (${s}) may not be larger than fft_length (${i})`);if(P!==s)throw new Error(`Length of the window (${P}) must equal frame_length (${s})`);if(r<=0)throw new Error("hop_length must be greater than zero");if(null===l&&null!==m)throw new Error("You have provided `mel_filters` but `power` is `None`. Mel spectrogram computation is not yet supported for complex-valued spectrogram. Specify `power` to fix this issue.");if(!p)throw new Error("`preemphasis_htk_flavor=false` is not currently supported.");if(c){if("reflect"!==d)throw new Error(`pad_mode="${d}" not implemented yet.`);const t=Math.floor((i-1)/2)+1;e=function(e,t,s){const r=new e.constructor(e.length+t+s),o=e.length-1;for(let s=0;s<e.length;++s)r[t+s]=e[s];for(let s=1;s<=t;++s)r[t-s]=e[(0,n.calculateReflectOffset)(s,o)];for(let i=1;i<=s;++i)r[o+t+i]=e[(0,n.calculateReflectOffset)(o-i,o)];return r}(e,t,t)}let F=Math.floor(1+Math.floor((e.length-s)/r));null!==k&&F<k&&(F=k);const C=u?Math.floor(i/2)+1:i;let S=F,E=F;null!==y&&(y>F?v&&(E=y):E=S=y);const A=new o.FFT(i),L=new Float64Array(i),I=new Float64Array(A.outputBufferSize),z=new Float32Array(C*E);for(let o=0;o<S;++o){const n=o*r,i=Math.min(e.length-n,s);i!==s&&L.fill(0,0,s);for(let t=0;t<i;++t)L[t]=e[n+t];if(b){let e=0;for(let t=0;t<i;++t)e+=L[t];const t=e/i;for(let e=0;e<i;++e)L[e]-=t}if(null!==_){for(let e=i-1;e>=1;--e)L[e]-=_*L[e-1];L[0]*=1-_}for(let e=0;e<t.length;++e)L[e]*=t[e];A.realTransform(I,L);for(let e=0;e<C;++e){const t=e<<1;z[e*E+o]=I[t]**2+I[t+1]**2}}if(null!==l&&2!==l){const e=l/2;for(let t=0;t<z.length;++t)z[t]**=e}const j=m.length;let D=await(0,a.matmul)(new a.Tensor("float32",m.flat(),[j,C]),new a.Tensor("float32",z,[C,E]));T&&(D=D.transpose(1,0));const O=D.data;for(let e=0;e<O.length;++e)O[e]=Math.max(h,O[e]);if(null!==l&&null!==g){const e=Math.min(O.length,S*j);switch(g){case"log":for(let t=0;t<e;++t)O[t]=Math.log(O[t]);break;case"log10":for(let t=0;t<e;++t)O[t]=Math.log10(O[t]);break;case"dB":if(1===l)!function(e,t=1,s=1e-5,r=null){w(e,20,t,s,r)}(O,f,M,x);else{if(2!==l)throw new Error(`Cannot use log_mel option '${g}' with power ${l}`);!function(e,t=1,s=1e-10,r=null){w(e,10,t,s,r)}(O,f,M,x)}break;default:throw new Error(`log_mel must be one of null, 'log', 'log10' or 'dB'. Got '${g}'`)}}return D}function x(e,t,{periodic:s=!0,frame_length:r=null,center:o=!0}={}){const n=s?e+1:e;let i;switch(t){case"boxcar":i=new Float64Array(n).fill(1);break;case"hann":case"hann_window":i=u(n);break;case"hamming":i=_(n);break;case"povey":i=u(n).map((e=>Math.pow(e,.85)));break;default:throw new Error(`Unknown window type ${t}.`)}if(s&&(i=i.subarray(0,e)),null===r)return i;if(e>r)throw new Error(`Length of the window (${e}) may not be larger than frame_length (${r})`);return i}function b(e,t,s){for(let r=0;r<s.length;++r)e.setUint8(t+r,s.charCodeAt(r))}class k{constructor(e,t){this.audio=e,this.sampling_rate=t}toWav(){return function(e,t){let s=44;const r=new ArrayBuffer(s+4*e.length),o=new DataView(r);b(o,0,"RIFF"),o.setUint32(4,36+4*e.length,!0),b(o,8,"WAVE"),b(o,12,"fmt "),o.setUint32(16,16,!0),o.setUint16(20,3,!0),o.setUint16(22,1,!0),o.setUint32(24,t,!0),o.setUint32(28,4*t,!0),o.setUint16(32,4,!0),o.setUint16(34,32,!0),b(o,36,"data"),o.setUint32(40,4*e.length,!0);for(let t=0;t<e.length;++t,s+=4)o.setFloat32(s,e[t],!0);return r}(this.audio,this.sampling_rate)}toBlob(){const e=this.toWav();return new Blob([e],{type:"audio/wav"})}async save(e){let t;if(i.apis.IS_BROWSER_ENV){if(i.apis.IS_WEBWORKER_ENV)throw new Error("Unable to save a file from a Web Worker.");t=n.saveBlob}else{if(!i.apis.IS_FS_AVAILABLE)throw new Error("Unable to save because filesystem is disabled in this environment.");t=async(e,t)=>{let s=await t.arrayBuffer();l.writeFileSync(e,Buffer.from(s))}}await t(e,this.toBlob())}}},"./src/utils/constants.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{CHAT_TEMPLATE_NAME:()=>l,CONFIG_NAME:()=>o,FEATURE_EXTRACTOR_NAME:()=>n,GENERATION_CONFIG_NAME:()=>c,GITHUB_ISSUE_URL:()=>r,IMAGE_PROCESSOR_NAME:()=>i,PROCESSOR_NAME:()=>a});const r="https://github.com/huggingface/transformers.js/issues/new/choose",o="config.json",n="preprocessor_config.json",i=n,a="processor_config.json",l="chat_template.jinja",c="generation_config.json"},"./src/utils/core.js":(e,t,s)=>{"use strict";function r(e,t){e&&e(t)}function o(e){return Object.fromEntries(Object.entries(e).map((([e,t])=>[t,e])))}function n(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function i(e){return"TypedArray"===e?.prototype?.__proto__?.constructor?.name}function a(e){return Number.isInteger(e)||"bigint"==typeof e}function l(e){return null==e||-1===e}function c(e){const t=[];let s=e;for(;Array.isArray(s);)t.push(s.length),s=s[0];return t}function d(e,t,s=void 0){const r=e[t];if(void 0!==r)return delete e[t],r;if(void 0===s)throw Error(`Key ${t} does not exist in object.`);return s}function u(...e){return Array.prototype.concat.apply([],e)}function _(...e){return e.reduce(((e,t)=>e.flatMap((e=>t.map((t=>[e,t]))))))}function p(e,t){return Math.abs((e+t)%(2*t)-t)}function m(e,t){const s=URL.createObjectURL(t),r=document.createElement("a");r.href=s,r.download=e,r.click(),r.remove(),URL.revokeObjectURL(s)}function h(e,t){return Object.assign({},...t.map((t=>{if(void 0!==e[t])return{[t]:e[t]}})))}function g(e){let t=0;for(const s of e)++t;return t}function f(e,t){let s=0;for(const r of e)r===t&&++s;return s}s.r(t),s.d(t,{calculateDimensions:()=>c,calculateReflectOffset:()=>p,count:()=>f,dispatchCallback:()=>r,escapeRegExp:()=>n,isIntegralNumber:()=>a,isNullishDimension:()=>l,isTypedArray:()=>i,len:()=>g,mergeArrays:()=>u,pick:()=>h,pop:()=>d,product:()=>_,reverseDictionary:()=>o,saveBlob:()=>m})},"./src/utils/data-structures.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{CharTrie:()=>o,DictionarySplitter:()=>l,LRUCache:()=>c,PriorityQueue:()=>r,TokenLattice:()=>i});class r{constructor(e=(e,t)=>e>t,t=1/0){this._heap=[],this._comparator=e,this._maxSize=t}get size(){return this._heap.length}isEmpty(){return 0===this.size}peek(){return this._heap[0]}push(...e){return this.extend(e)}extend(e){for(const t of e)if(this.size<this._maxSize)this._heap.push(t),this._siftUp();else{const e=this._smallest();this._comparator(t,this._heap[e])&&(this._heap[e]=t,this._siftUpFrom(e))}return this.size}pop(){const e=this.peek(),t=this.size-1;return t>0&&this._swap(0,t),this._heap.pop(),this._siftDown(),e}replace(e){const t=this.peek();return this._heap[0]=e,this._siftDown(),t}_parent(e){return(e+1>>>1)-1}_left(e){return 1+(e<<1)}_right(e){return e+1<<1}_greater(e,t){return this._comparator(this._heap[e],this._heap[t])}_swap(e,t){const s=this._heap[e];this._heap[e]=this._heap[t],this._heap[t]=s}_siftUp(){this._siftUpFrom(this.size-1)}_siftUpFrom(e){for(;e>0&&this._greater(e,this._parent(e));)this._swap(e,this._parent(e)),e=this._parent(e)}_siftDown(){let e=0;for(;this._left(e)<this.size&&this._greater(this._left(e),e)||this._right(e)<this.size&&this._greater(this._right(e),e);){const t=this._right(e)<this.size&&this._greater(this._right(e),this._left(e))?this._right(e):this._left(e);this._swap(e,t),e=t}}_smallest(){return 2**Math.floor(Math.log2(this.size))-1}}class o{constructor(){this.root=n.default()}extend(e){for(const t of e)this.push(t)}push(e){let t=this.root;for(const s of e){let e=t.children.get(s);void 0===e&&(e=n.default(),t.children.set(s,e)),t=e}t.isLeaf=!0}*commonPrefixSearch(e){let t=this.root;if(void 0===t)return;let s="";for(const r of e){if(s+=r,t=t.children.get(r),void 0===t)return;t.isLeaf&&(yield s)}}}class n{constructor(e,t){this.isLeaf=e,this.children=t}static default(){return new n(!1,new Map)}}class i{constructor(e,t,s){this.chars=Array.from(e),this.len=this.chars.length,this.bosTokenId=t,this.eosTokenId=s,this.nodes=[],this.beginNodes=Array.from({length:this.len+1},(()=>[])),this.endNodes=Array.from({length:this.len+1},(()=>[]));const r=new a(this.bosTokenId,0,0,0,0),o=new a(this.eosTokenId,1,this.len,0,0);this.nodes.push(r.clone()),this.nodes.push(o.clone()),this.beginNodes[this.len].push(o),this.endNodes[0].push(r)}insert(e,t,s,r){const o=this.nodes.length,n=new a(r,o,e,t,s);this.beginNodes[e].push(n),this.endNodes[e+t].push(n),this.nodes.push(n)}viterbi(){const e=this.len;let t=0;for(;t<=e;){if(0==this.beginNodes[t].length)return[];for(let e of this.beginNodes[t]){e.prev=null;let s=0,r=null;for(let o of this.endNodes[t]){const t=o.backtraceScore+e.score;(null===r||t>s)&&(r=o.clone(),s=t)}if(null===r)return[];e.prev=r,e.backtraceScore=s}++t}const s=[],r=this.beginNodes[e][0].prev;if(null===r)return[];let o=r.clone();for(;null!==o.prev;){s.push(o.clone());const e=o.clone();o=e.prev.clone()}return s.reverse(),s}piece(e){return this.chars.slice(e.pos,e.pos+e.length).join("")}tokens(){return this.viterbi().map((e=>this.piece(e)))}tokenIds(){return this.viterbi().map((e=>e.tokenId))}}class a{constructor(e,t,s,r,o){this.tokenId=e,this.nodeId=t,this.pos=s,this.length=r,this.score=o,this.prev=null,this.backtraceScore=0}clone(){const e=new a(this.tokenId,this.nodeId,this.pos,this.length,this.score);return e.prev=this.prev,e.backtraceScore=this.backtraceScore,e}}class l{constructor(e){this.trie=this._buildTrie(e)}_buildTrie(e){const t=Object.create(null);for(const s of e){let e=t;for(let t=0;t<s.length;++t)e=e[s[t]]??=Object.create(null);e.end=s}return t}split(e){const t=[],s=e.length;let r=0,o=0;for(;o<s;){let n=this.trie,i=null,a=o;for(;a<s&&(n=n[e[a]]);)n.end&&(i=n.end),++a;i?(o>r&&t.push(e.slice(r,o)),t.push(i),o+=i.length,r=o):++o}return r<s&&t.push(e.slice(r)),t}}class c{constructor(e){this.capacity=e,this.cache=new Map}get(e){if(!this.cache.has(e))return;const t=this.cache.get(e);return this.cache.delete(e),this.cache.set(e,t),t}put(e,t){this.cache.has(e)&&this.cache.delete(e),this.cache.set(e,t),this.cache.size>this.capacity&&this.cache.delete(this.cache.keys().next().value)}clear(){this.cache.clear()}}},"./src/utils/devices.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{DEVICE_TYPES:()=>r});const r=Object.freeze({auto:"auto",gpu:"gpu",cpu:"cpu",wasm:"wasm",webgpu:"webgpu",cuda:"cuda",dml:"dml",webnn:"webnn","webnn-npu":"webnn-npu","webnn-gpu":"webnn-gpu","webnn-cpu":"webnn-cpu"})},"./src/utils/dtypes.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{DATA_TYPES:()=>i,DEFAULT_DEVICE_DTYPE_MAPPING:()=>a,DEFAULT_DTYPE_SUFFIX_MAPPING:()=>l,isWebGpuFp16Supported:()=>n});var r=s("./src/env.js"),o=s("./src/utils/devices.js");const n=function(){let e;return async function(){if(void 0===e)if(r.apis.IS_WEBGPU_AVAILABLE)try{const t=await navigator.gpu.requestAdapter();e=t.features.has("shader-f16")}catch(t){e=!1}else e=!1;return e}}(),i=Object.freeze({auto:"auto",fp32:"fp32",fp16:"fp16",q8:"q8",int8:"int8",uint8:"uint8",q4:"q4",bnb4:"bnb4",q4f16:"q4f16"}),a=Object.freeze({[o.DEVICE_TYPES.wasm]:i.q8}),l=Object.freeze({[i.fp32]:"",[i.fp16]:"_fp16",[i.int8]:"_int8",[i.uint8]:"_uint8",[i.q8]:"_quantized",[i.q4]:"_q4",[i.q4f16]:"_q4f16",[i.bnb4]:"_bnb4"})},"./src/utils/generic.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{Callable:()=>r});const r=class{constructor(){let e=function(...t){return e._call(...t)};return Object.setPrototypeOf(e,new.target.prototype)}_call(...e){throw Error("Must implement _call method in subclass")}}},"./src/utils/hub.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{MAX_EXTERNAL_DATA_CHUNKS:()=>a,getFile:()=>_,getModelFile:()=>h,getModelJSON:()=>f,getModelText:()=>g});var r=s("node:fs"),o=s("node:path"),n=s("./src/env.js"),i=s("./src/utils/core.js");const a=100,l={txt:"text/plain",html:"text/html",css:"text/css",js:"text/javascript",json:"application/json",png:"image/png",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif"};class c{constructor(e){if(this.filePath=e,this.headers=new Headers,this.exists=r.existsSync(e),this.exists){this.status=200,this.statusText="OK";let t=r.statSync(e);this.headers.set("content-length",t.size.toString()),this.updateContentType();const s=r.createReadStream(e);this.body=new ReadableStream({start(e){s.on("data",(t=>e.enqueue(t))),s.on("end",(()=>e.close())),s.on("error",(t=>e.error(t)))},cancel(){s.destroy()}})}else this.status=404,this.statusText="Not Found",this.body=null}updateContentType(){const e=this.filePath.toString().split(".").pop().toLowerCase();this.headers.set("content-type",l[e]??"application/octet-stream")}clone(){let e=new c(this.filePath);return e.exists=this.exists,e.status=this.status,e.statusText=this.statusText,e.headers=new Headers(this.headers),e}async arrayBuffer(){return(await r.promises.readFile(this.filePath)).buffer}async blob(){const e=await r.promises.readFile(this.filePath);return new Blob([e],{type:this.headers.get("content-type")})}async text(){return await r.promises.readFile(this.filePath,"utf8")}async json(){return JSON.parse(await this.text())}}function d(e,t=null,s=null){let r;try{r=new URL(e)}catch(e){return!1}return!(t&&!t.includes(r.protocol))&&!(s&&!s.includes(r.hostname))}const u=/^(\b[\w\-.]+\b\/)?\b[\w\-.]{1,96}\b$/;async function _(e){if(n.env.useFS&&!d(e,["http:","https:","blob:"]))return new c(e instanceof URL?"file:"===e.protocol?e.pathname:e.toString():e);if("undefined"!=typeof process&&"node"===process?.release?.name){const t=!!process.env?.TESTING_REMOTELY,s=n.env.version,r=new Headers;r.set("User-Agent",`transformers.js/${s}; is_ci/${t};`);if(d(e,["http:","https:"],["huggingface.co","hf.co"])){const e=process.env?.HF_TOKEN??process.env?.HF_ACCESS_TOKEN;e&&r.set("Authorization",`Bearer ${e}`)}return fetch(e,{headers:r})}return fetch(e)}const p={400:"Bad request error occurred while trying to load file",401:"Unauthorized access to file",403:"Forbidden access to file",404:"Could not locate file",408:"Request timeout error occurred while trying to load file",500:"Internal server error error occurred while trying to load file",502:"Bad gateway error occurred while trying to load file",503:"Service unavailable error occurred while trying to load file",504:"Gateway timeout error occurred while trying to load file"};class m{constructor(e){this.path=e}async match(e){let t=o.join(this.path,e),s=new c(t);return s.exists?s:void 0}async put(e,t,s=void 0){let n=o.join(this.path,e);try{const e=t.headers.get("Content-Length"),i=parseInt(e??"0");let a=0;await r.promises.mkdir(o.dirname(n),{recursive:!0});const l=r.createWriteStream(n),c=t.body.getReader();for(;;){const{done:e,value:t}=await c.read();if(e)break;await new Promise(((e,s)=>{l.write(t,(t=>{t?s(t):e()}))})),a+=t.length;const r=i?a/i*100:0;s?.({progress:r,loaded:a,total:i})}l.close()}catch(e){try{await r.promises.unlink(n)}catch{}throw e}}}async function h(e,t,s=!0,r={},o=!1){if(!n.env.allowLocalModels){if(r.local_files_only)throw Error("Invalid configuration detected: local models are disabled (`env.allowLocalModels=false`) but you have requested to only use local models (`local_files_only=true`).");if(!n.env.allowRemoteModels)throw Error("Invalid configuration detected: both local and remote models are disabled. Fix by setting `env.allowLocalModels` or `env.allowRemoteModels` to `true`.")}let a;if((0,i.dispatchCallback)(r.progress_callback,{status:"initiate",name:e,file:t}),!a&&n.env.useCustomCache){if(!n.env.customCache)throw Error("`env.useCustomCache=true`, but `env.customCache` is not defined.");if(!n.env.customCache.match||!n.env.customCache.put)throw new Error("`env.customCache` must be an object which implements the `match` and `put` functions of the Web Cache API. For more information, see https://developer.mozilla.org/en-US/docs/Web/API/Cache");a=n.env.customCache}if(!a&&n.env.useBrowserCache){if("undefined"==typeof caches)throw Error("Browser cache is not available in this environment.");try{a=await caches.open("transformers-cache")}catch(e){console.warn("An error occurred while opening the browser cache:",e)}}if(!a&&n.env.useFSCache){if(!n.apis.IS_FS_AVAILABLE)throw Error("File System Cache is not available in this environment.");a=new m(r.cache_dir??n.env.cacheDir)}const l=r.revision??"main",h=w(e,t),g=(f=e,!(!u.test(f)||f.includes("..")||f.includes("--")||f.endsWith(".git")||f.endsWith(".ipynb")));var f;const M=g?w(n.env.localModelPath,h):h,x=w(n.env.remoteHost,n.env.remotePathTemplate.replaceAll("{model}",e).replaceAll("{revision}",encodeURIComponent(l)),t);let b;const k=a instanceof m?"main"===l?h:w(e,l,t):x;let y,v=!1;a&&(y=await async function(e,...t){for(let s of t)try{let t=await e.match(s);if(t)return t}catch(e){continue}}(a,M,k));const T=void 0!==y;if(void 0===y){if(n.env.allowLocalModels){if(d(h,["http:","https:"])){if(r.local_files_only)throw new Error(`\`local_files_only=true\`, but attempted to load a remote file from: ${h}.`);if(!n.env.allowRemoteModels)throw new Error(`\`env.allowRemoteModels=false\`, but attempted to load a remote file from: ${h}.`)}else try{y=await _(M),b=M}catch(e){console.warn(`Unable to load from local path "${M}": "${e}"`)}}if(void 0===y||404===y.status){if(r.local_files_only||!n.env.allowRemoteModels){if(s)throw Error(`\`local_files_only=true\` or \`env.allowRemoteModels=false\` and file was not found locally at "${M}".`);return null}if(!g)throw Error(`Local file missing at "${M}" and download aborted due to invalid model ID "${e}".`);if(y=await _(x),200!==y.status)return function(e,t,s){if(!s)return null;const r=p[e]??`Error (${e}) occurred while trying to load file`;throw Error(`${r}: "${t}".`)}(y.status,x,s);b=k}v=a&&"undefined"!=typeof Response&&y instanceof Response&&200===y.status}let P;if((0,i.dispatchCallback)(r.progress_callback,{status:"download",name:e,file:t}),!n.apis.IS_NODE_ENV||!o){let s;r.progress_callback?T&&"undefined"!=typeof navigator&&/firefox/i.test(navigator.userAgent)?(s=new Uint8Array(await y.arrayBuffer()),(0,i.dispatchCallback)(r.progress_callback,{status:"progress",name:e,file:t,progress:100,loaded:s.length,total:s.length})):s=await async function(e,t){const s=e.headers.get("Content-Length");null===s&&console.warn("Unable to determine content-length from response headers. Will expand buffer when needed.");let r=parseInt(s??"0"),o=new Uint8Array(r),n=0;const i=e.body.getReader();async function a(){const{done:e,value:s}=await i.read();if(e)return;const l=n+s.length;if(l>r){r=l;const e=new Uint8Array(r);e.set(o),o=e}o.set(s,n),n=l;return t({progress:n/r*100,loaded:n,total:r}),a()}return await a(),o}(y,(s=>{(0,i.dispatchCallback)(r.progress_callback,{status:"progress",name:e,file:t,...s})})):s=new Uint8Array(await y.arrayBuffer()),P=s}if(v&&b&&void 0===await a.match(b)&&(P?await a.put(b,new Response(P,{headers:y.headers})).catch((e=>{console.warn(`Unable to add response to browser cache: ${e}.`)})):await a.put(b,y,r.progress_callback)),(0,i.dispatchCallback)(r.progress_callback,{status:"done",name:e,file:t}),P){if(!n.apis.IS_NODE_ENV&&o)throw new Error("Cannot return path in a browser environment.");return P}if(y instanceof c)return y.filePath;const F=await(a?.match(b));if(F instanceof c)return F.filePath;if(F instanceof Response)return new Uint8Array(await F.arrayBuffer());if("string"==typeof F)return F;throw new Error("Unable to get model file path or buffer.")}async function g(e,t,s=!0,r={}){const o=await h(e,t,s,r,!1);if(null===o)return null;return new TextDecoder("utf-8").decode(o)}async function f(e,t,s=!0,r={}){const o=await g(e,t,s,r);return null===o?{}:JSON.parse(o)}function w(...e){return(e=e.map(((t,s)=>(s&&(t=t.replace(new RegExp("^/"),"")),s!==e.length-1&&(t=t.replace(new RegExp("/$"),"")),t)))).join("/")}},"./src/utils/image.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{RawImage:()=>m,load_image:()=>h});var r=s("./src/utils/core.js"),o=s("./src/utils/hub.js"),n=s("./src/env.js"),i=s("./src/utils/tensor.js"),a=s("sharp");let l,c,d;const u=n.apis.IS_BROWSER_ENV||n.apis.IS_WEBWORKER_ENV;if(u)l=(e,t)=>{if(!self.OffscreenCanvas)throw new Error("OffscreenCanvas not supported by this browser.");return new self.OffscreenCanvas(e,t)},d=self.createImageBitmap,c=self.ImageData;else{if(!a)throw new Error("Unable to load image processing library.");d=async e=>{const t=(await e.metadata()).channels,{data:s,info:r}=await e.rotate().raw().toBuffer({resolveWithObject:!0}),o=new m(new Uint8ClampedArray(s),r.width,r.height,r.channels);return void 0!==t&&t!==r.channels&&o.convert(t),o}}const _={0:"nearest",1:"lanczos",2:"bilinear",3:"bicubic",4:"box",5:"hamming"},p=new Map([["png","image/png"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["gif","image/gif"]]);class m{constructor(e,t,s,r){this.data=e,this.width=t,this.height=s,this.channels=r}get size(){return[this.width,this.height]}static async read(e){if(e instanceof m)return e;if("string"==typeof e||e instanceof URL)return await this.fromURL(e);if(e instanceof Blob)return await this.fromBlob(e);if("undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement||"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas)return this.fromCanvas(e);throw new Error("Unsupported input type: "+typeof e)}static fromCanvas(e){if(!u)throw new Error("fromCanvas() is only supported in browser environments.");const t=e.getContext("2d").getImageData(0,0,e.width,e.height).data;return new m(t,e.width,e.height,4)}static async fromURL(e){const t=await(0,o.getFile)(e);if(200!==t.status)throw new Error(`Unable to read image from "${e}" (${t.status} ${t.statusText})`);const s=await t.blob();return this.fromBlob(s)}static async fromBlob(e){if(u){const t=await d(e),s=l(t.width,t.height).getContext("2d");return s.drawImage(t,0,0),new this(s.getImageData(0,0,t.width,t.height).data,t.width,t.height,4)}{const t=a(await e.arrayBuffer());return await d(t)}}static fromTensor(e,t="CHW"){if(3!==e.dims.length)throw new Error(`Tensor should have 3 dimensions, but has ${e.dims.length} dimensions.`);if("CHW"===t)e=e.transpose(1,2,0);else if("HWC"!==t)throw new Error(`Unsupported channel format: ${t}`);if(!(e.data instanceof Uint8ClampedArray||e.data instanceof Uint8Array))throw new Error(`Unsupported tensor type: ${e.type}`);switch(e.dims[2]){case 1:case 2:case 3:case 4:return new m(e.data,e.dims[1],e.dims[0],e.dims[2]);default:throw new Error(`Unsupported number of channels: ${e.dims[2]}`)}}grayscale(){if(1===this.channels)return this;const e=new Uint8ClampedArray(this.width*this.height*1);switch(this.channels){case 3:case 4:for(let t=0,s=0;t<this.data.length;t+=this.channels){const r=this.data[t],o=this.data[t+1],n=this.data[t+2];e[s++]=Math.round(.2989*r+.587*o+.114*n)}break;default:throw new Error(`Conversion failed due to unsupported number of channels: ${this.channels}`)}return this._update(e,this.width,this.height,1)}rgb(){if(3===this.channels)return this;const e=new Uint8ClampedArray(this.width*this.height*3);switch(this.channels){case 1:for(let t=0,s=0;t<this.data.length;++t)e[s++]=this.data[t],e[s++]=this.data[t],e[s++]=this.data[t];break;case 4:for(let t=0,s=0;t<this.data.length;t+=4)e[s++]=this.data[t],e[s++]=this.data[t+1],e[s++]=this.data[t+2];break;default:throw new Error(`Conversion failed due to unsupported number of channels: ${this.channels}`)}return this._update(e,this.width,this.height,3)}rgba(){if(4===this.channels)return this;const e=new Uint8ClampedArray(this.width*this.height*4);switch(this.channels){case 1:for(let t=0,s=0;t<this.data.length;++t)e[s++]=this.data[t],e[s++]=this.data[t],e[s++]=this.data[t],e[s++]=255;break;case 3:for(let t=0,s=0;t<this.data.length;t+=3)e[s++]=this.data[t],e[s++]=this.data[t+1],e[s++]=this.data[t+2],e[s++]=255;break;default:throw new Error(`Conversion failed due to unsupported number of channels: ${this.channels}`)}return this._update(e,this.width,this.height,4)}putAlpha(e){if(e.width!==this.width||e.height!==this.height)throw new Error(`Expected mask size to be ${this.width}x${this.height}, but got ${e.width}x${e.height}`);if(1!==e.channels)throw new Error(`Expected mask to have 1 channel, but got ${e.channels}`);const t=this.data,s=e.data,r=this.width*this.height;if(3===this.channels){const e=new Uint8ClampedArray(4*r);for(let o=0,n=0,i=0;o<r;++o)e[i++]=t[n++],e[i++]=t[n++],e[i++]=t[n++],e[i++]=s[o];return this._update(e,this.width,this.height,4)}if(4===this.channels){for(let e=0;e<r;++e)t[4*e+3]=s[e];return this}throw new Error(`Expected image to have 3 or 4 channels, but got ${this.channels}`)}async resize(e,t,{resample:s=2}={}){if(this.width===e&&this.height===t)return this;let o=_[s]??s;const n=(0,r.isNullishDimension)(e),i=(0,r.isNullishDimension)(t);if(n&&i)return this;if(n?e=t/this.height*this.width:i&&(t=e/this.width*this.height),u){const s=this.channels,r=this.toCanvas(),o=l(e,t).getContext("2d");o.drawImage(r,0,0,e,t);return new m(o.getImageData(0,0,e,t).data,e,t,4).convert(s)}{let s=this.toSharp();switch(o){case"box":case"hamming":"box"!==o&&"hamming"!==o||(console.warn(`Resampling method ${o} is not yet supported. Using bilinear instead.`),o="bilinear");case"nearest":case"bilinear":case"bicubic":s=s.affine([e/this.width,0,0,t/this.height],{interpolator:o});break;case"lanczos":s=s.resize({width:e,height:t,fit:"fill",kernel:"lanczos3"});break;default:throw new Error(`Resampling method ${o} is not supported.`)}return await d(s)}}async pad([e,t,s,r]){if(e=Math.max(e,0),t=Math.max(t,0),s=Math.max(s,0),r=Math.max(r,0),0===e&&0===t&&0===s&&0===r)return this;if(u){const o=this.channels,n=this.toCanvas(),i=this.width+e+t,a=this.height+s+r,c=l(i,a).getContext("2d");c.drawImage(n,0,0,this.width,this.height,e,s,this.width,this.height);return new m(c.getImageData(0,0,i,a).data,i,a,4).convert(o)}{const o=this.toSharp().extend({left:e,right:t,top:s,bottom:r});return await d(o)}}async crop([e,t,s,r]){if(e=Math.max(e,0),t=Math.max(t,0),s=Math.min(s,this.width-1),r=Math.min(r,this.height-1),0===e&&0===t&&s===this.width-1&&r===this.height-1)return this;const o=s-e+1,n=r-t+1;if(u){const s=this.channels,r=this.toCanvas(),i=l(o,n).getContext("2d");i.drawImage(r,e,t,o,n,0,0,o,n);return new m(i.getImageData(0,0,o,n).data,o,n,4).convert(s)}{const s=this.toSharp().extract({left:e,top:t,width:o,height:n});return await d(s)}}async center_crop(e,t){if(this.width===e&&this.height===t)return this;const s=(this.width-e)/2,r=(this.height-t)/2;if(u){const o=this.channels,n=this.toCanvas(),i=l(e,t).getContext("2d");let a=0,c=0,d=0,u=0;s>=0?a=s:d=-s,r>=0?c=r:u=-r,i.drawImage(n,a,c,e,t,d,u,e,t);return new m(i.getImageData(0,0,e,t).data,e,t,4).convert(o)}{let o=this.toSharp();if(s>=0&&r>=0)o=o.extract({left:Math.floor(s),top:Math.floor(r),width:e,height:t});else if(s<=0&&r<=0){const n=Math.floor(-r),i=Math.floor(-s);o=o.extend({top:n,left:i,right:e-this.width-i,bottom:t-this.height-n})}else{let n=[0,0],i=0;r<0?(n[0]=Math.floor(-r),n[1]=t-this.height-n[0]):i=Math.floor(r);let a=[0,0],l=0;s<0?(a[0]=Math.floor(-s),a[1]=e-this.width-a[0]):l=Math.floor(s),o=o.extend({top:n[0],bottom:n[1],left:a[0],right:a[1]}).extract({left:l,top:i,width:e,height:t})}return await d(o)}}async toBlob(e="image/png",t=1){if(!u)throw new Error("toBlob() is only supported in browser environments.");const s=this.toCanvas();return await s.convertToBlob({type:e,quality:t})}toTensor(e="CHW"){let t=new i.Tensor("uint8",new Uint8Array(this.data),[this.height,this.width,this.channels]);if("HWC"===e);else{if("CHW"!==e)throw new Error(`Unsupported channel format: ${e}`);t=t.permute(2,0,1)}return t}toCanvas(){if(!u)throw new Error("toCanvas() is only supported in browser environments.");const e=this.clone().rgba(),t=l(e.width,e.height),s=new c(e.data,e.width,e.height);return t.getContext("2d").putImageData(s,0,0),t}split(){const{data:e,width:t,height:s,channels:r}=this,o=e.constructor,n=e.length/r,i=Array.from({length:r},(()=>new o(n)));for(let t=0;t<n;++t){const s=r*t;for(let o=0;o<r;++o)i[o][t]=e[s+o]}return i.map((e=>new m(e,t,s,1)))}_update(e,t,s,r=null){return this.data=e,this.width=t,this.height=s,null!==r&&(this.channels=r),this}clone(){return new m(this.data.slice(),this.width,this.height,this.channels)}convert(e){if(this.channels===e)return this;switch(e){case 1:this.grayscale();break;case 3:this.rgb();break;case 4:this.rgba();break;default:throw new Error(`Conversion failed due to unsupported number of channels: ${this.channels}`)}return this}async save(e){if(!u){if(n.apis.IS_FS_AVAILABLE){const t=this.toSharp();return await t.toFile(e)}throw new Error("Unable to save the image because filesystem is disabled in this environment.")}{if(n.apis.IS_WEBWORKER_ENV)throw new Error("Unable to save an image from a Web Worker.");const t=e.split(".").pop().toLowerCase(),s=p.get(t)??"image/png",o=await this.toBlob(s);(0,r.saveBlob)(e,o)}}toSharp(){if(u)throw new Error("toSharp() is only supported in server-side environments.");return a(this.data,{raw:{width:this.width,height:this.height,channels:this.channels}})}}const h=m.read.bind(m)},"./src/utils/maths.js":(e,t,s)=>{"use strict";function r(e,[t,s,r],[o,n],i="bilinear",a=!1){const l=n/r,c=o/s,d=new e.constructor(o*n*t),u=s*r,_=o*n;for(let i=0;i<o;++i)for(let o=0;o<n;++o){const a=i*n+o,p=(o+.5)/l-.5,m=(i+.5)/c-.5;let h=Math.floor(p),g=Math.floor(m);const f=Math.min(h+1,r-1),w=Math.min(g+1,s-1);h=Math.max(h,0),g=Math.max(g,0);const M=p-h,x=m-g,b=(1-M)*(1-x),k=M*(1-x),y=(1-M)*x,v=M*x,T=g*r,P=w*r,F=T+h,C=T+f,S=P+h,E=P+f;for(let s=0;s<t;++s){const t=s*u;d[s*_+a]=b*e[t+F]+k*e[t+C]+y*e[t+S]+v*e[t+E]}}return d}function o(e,t,s){const r=new Array(s.length),o=new Array(s.length);for(let e=s.length-1,n=1;e>=0;--e)o[e]=n,r[e]=t[s[e]],n*=r[e];const n=s.map(((e,t)=>o[s.indexOf(t)])),i=new e.constructor(e.length);for(let s=0;s<e.length;++s){let r=0;for(let e=t.length-1,o=s;e>=0;--e)r+=o%t[e]*n[e],o=Math.floor(o/t[e]);i[r]=e[s]}return[i,r]}function n(e){const t=u(e)[0],s=e.map((e=>Math.exp(e-t))),r=s.reduce(((e,t)=>e+t),0);return s.map((e=>e/r))}function i(e){const t=u(e)[0];let s=0;for(let r=0;r<e.length;++r)s+=Math.exp(e[r]-t);const r=Math.log(s);return e.map((e=>e-t-r))}function a(e,t){let s=0;for(let r=0;r<e.length;++r)s+=e[r]*t[r];return s}function l(e,t){return a(e,t)/(c(e)*c(t))}function c(e){return Math.sqrt(e.reduce(((e,t)=>e+t*t),0))}function d(e){if(0===e.length)throw Error("Array must not be empty");let t=e[0],s=0;for(let r=1;r<e.length;++r)e[r]<t&&(t=e[r],s=r);return[t,s]}function u(e){if(0===e.length)throw Error("Array must not be empty");let t=e[0],s=0;for(let r=1;r<e.length;++r)e[r]>t&&(t=e[r],s=r);return[t,s]}function _(e){return e>0&&!(e&e-1)}s.r(t),s.d(t,{FFT:()=>h,bankers_round:()=>w,cos_sim:()=>l,dot:()=>a,dynamic_time_warping:()=>M,interpolate_data:()=>r,log_softmax:()=>i,magnitude:()=>c,max:()=>u,medianFilter:()=>g,min:()=>d,permute_data:()=>o,round:()=>f,softmax:()=>n});class p{constructor(e){if(this.size=0|e,this.size<=1||!_(this.size))throw new Error("FFT size must be a power of two larger than 1");this._csize=e<<1,this.table=new Float64Array(2*this.size);for(let e=0;e<this.table.length;e+=2){const t=Math.PI*e/this.size;this.table[e]=Math.cos(t),this.table[e+1]=-Math.sin(t)}let t=0;for(let e=1;this.size>e;e<<=1)++t;this._width=t%2==0?t-1:t,this._bitrev=new Int32Array(1<<this._width);for(let e=0;e<this._bitrev.length;++e){this._bitrev[e]=0;for(let t=0;t<this._width;t+=2){const s=this._width-t-2;this._bitrev[e]|=(e>>>t&3)<<s}}}createComplexArray(){return new Float64Array(this._csize)}fromComplexArray(e,t){const s=t||new Array(e.length>>>1);for(let t=0;t<e.length;t+=2)s[t>>>1]=e[t];return s}toComplexArray(e,t){const s=t||this.createComplexArray();for(let t=0;t<s.length;t+=2)s[t]=e[t>>>1],s[t+1]=0;return s}transform(e,t){if(e===t)throw new Error("Input and output buffers must be different");this._transform4(e,t,1)}realTransform(e,t){if(e===t)throw new Error("Input and output buffers must be different");this._realTransform4(e,t,1)}inverseTransform(e,t){if(e===t)throw new Error("Input and output buffers must be different");this._transform4(e,t,-1);for(let t=0;t<e.length;++t)e[t]/=this.size}_transform4(e,t,s){const r=this._csize;let o,n,i=1<<this._width,a=r/i<<1;const l=this._bitrev;if(4===a)for(o=0,n=0;o<r;o+=a,++n){const s=l[n];this._singleTransform2(t,e,o,s,i)}else for(o=0,n=0;o<r;o+=a,++n){const r=l[n];this._singleTransform4(t,e,o,r,i,s)}const c=this.table;for(i>>=2;i>=2;i>>=2){a=r/i<<1;const t=a>>>2;for(o=0;o<r;o+=a){const r=o+t-1;for(let n=o,a=0;n<r;n+=2,a+=i){const r=n,o=r+t,i=o+t,l=i+t,d=e[r],u=e[r+1],_=e[o],p=e[o+1],m=e[i],h=e[i+1],g=e[l],f=e[l+1],w=c[a],M=s*c[a+1],x=_*w-p*M,b=_*M+p*w,k=c[2*a],y=s*c[2*a+1],v=m*k-h*y,T=m*y+h*k,P=c[3*a],F=s*c[3*a+1],C=g*P-f*F,S=g*F+f*P,E=d+v,A=u+T,L=d-v,I=u-T,z=x+C,j=b+S,D=s*(x-C),O=s*(b-S);e[r]=E+z,e[r+1]=A+j,e[o]=L+O,e[o+1]=I-D,e[i]=E-z,e[i+1]=A-j,e[l]=L-O,e[l+1]=I+D}}}}_singleTransform2(e,t,s,r,o){const n=e[r],i=e[r+1],a=e[r+o],l=e[r+o+1];t[s]=n+a,t[s+1]=i+l,t[s+2]=n-a,t[s+3]=i-l}_singleTransform4(e,t,s,r,o,n){const i=2*o,a=3*o,l=e[r],c=e[r+1],d=e[r+o],u=e[r+o+1],_=e[r+i],p=e[r+i+1],m=e[r+a],h=e[r+a+1],g=l+_,f=c+p,w=l-_,M=c-p,x=d+m,b=u+h,k=n*(d-m),y=n*(u-h);t[s]=g+x,t[s+1]=f+b,t[s+2]=w+y,t[s+3]=M-k,t[s+4]=g-x,t[s+5]=f-b,t[s+6]=w-y,t[s+7]=M+k}_realTransform4(e,t,s){const r=this._csize;let o,n,i=1<<this._width,a=r/i<<1;const l=this._bitrev;if(4===a)for(o=0,n=0;o<r;o+=a,++n){const s=l[n];this._singleRealTransform2(t,e,o,s>>>1,i>>>1)}else for(o=0,n=0;o<r;o+=a,++n){const r=l[n];this._singleRealTransform4(t,e,o,r>>>1,i>>>1,s)}const c=this.table;for(i>>=2;i>=2;i>>=2){a=r/i<<1;const t=a>>>1,n=t>>>1,l=n>>>1;for(o=0;o<r;o+=a)for(let r=0,a=0;r<=l;r+=2,a+=i){const i=o+r,d=i+n,u=d+n,_=u+n,p=e[i],m=e[i+1],h=e[d],g=e[d+1],f=e[u],w=e[u+1],M=e[_],x=e[_+1],b=p,k=m,y=c[a],v=s*c[a+1],T=h*y-g*v,P=h*v+g*y,F=c[2*a],C=s*c[2*a+1],S=f*F-w*C,E=f*C+w*F,A=c[3*a],L=s*c[3*a+1],I=M*A-x*L,z=M*L+x*A,j=b+S,D=k+E,O=b-S,N=k-E,V=T+I,B=P+z,G=s*(T-I),R=s*(P-z);if(e[i]=j+V,e[i+1]=D+B,e[d]=O+R,e[d+1]=N-G,0===r){e[u]=j-V,e[u+1]=D-B;continue}if(r===l)continue;const $=o+n-r,q=o+t-r;e[$]=O-s*R,e[$+1]=-N-s*G,e[q]=j-s*V,e[q+1]=s*B-D}}const d=r>>>1;for(let t=2;t<d;t+=2)e[r-t]=e[t],e[r-t+1]=-e[t+1]}_singleRealTransform2(e,t,s,r,o){const n=e[r],i=e[r+o];t[s]=n+i,t[s+1]=0,t[s+2]=n-i,t[s+3]=0}_singleRealTransform4(e,t,s,r,o,n){const i=2*o,a=3*o,l=e[r],c=e[r+o],d=e[r+i],u=e[r+a],_=l+d,p=l-d,m=c+u,h=n*(c-u);t[s]=_+m,t[s+1]=0,t[s+2]=p,t[s+3]=-h,t[s+4]=_-m,t[s+5]=0,t[s+6]=p,t[s+7]=h}}class m{constructor(e){const t=2*(e-1),s=2*(2*e-1),r=2**Math.ceil(Math.log2(s));this.bufferSize=r,this._a=t;const o=new Float64Array(s),n=new Float64Array(r);this._chirpBuffer=new Float64Array(r),this._buffer1=new Float64Array(r),this._buffer2=new Float64Array(r),this._outBuffer1=new Float64Array(r),this._outBuffer2=new Float64Array(r);const i=-2*Math.PI/e,a=Math.cos(i),l=Math.sin(i);for(let t=0;t<s>>1;++t){const s=(t+1-e)**2/2,r=Math.sqrt(a**2+l**2)**s,i=s*Math.atan2(l,a),c=2*t;o[c]=r*Math.cos(i),o[c+1]=r*Math.sin(i),n[c]=o[c],n[c+1]=-o[c+1]}this._slicedChirpBuffer=o.subarray(t,s),this._f=new p(r>>1),this._f.transform(this._chirpBuffer,n)}_transform(e,t,s){const r=this._buffer1,o=this._buffer2,n=this._outBuffer1,i=this._outBuffer2,a=this._chirpBuffer,l=this._slicedChirpBuffer,c=this._a;if(s)for(let e=0;e<l.length;e+=2){const s=e+1,o=t[e>>1];r[e]=o*l[e],r[s]=o*l[s]}else for(let e=0;e<l.length;e+=2){const s=e+1;r[e]=t[e]*l[e]-t[s]*l[s],r[s]=t[e]*l[s]+t[s]*l[e]}this._f.transform(n,r);for(let e=0;e<a.length;e+=2){const t=e+1;o[e]=n[e]*a[e]-n[t]*a[t],o[t]=n[e]*a[t]+n[t]*a[e]}this._f.inverseTransform(i,o);for(let t=0;t<i.length;t+=2){const s=i[t+c],r=i[t+c+1],o=l[t],n=l[t+1];e[t]=s*o-r*n,e[t+1]=s*n+r*o}}transform(e,t){this._transform(e,t,!1)}realTransform(e,t){this._transform(e,t,!0)}}class h{constructor(e){this.fft_length=e,this.isPowerOfTwo=_(e),this.isPowerOfTwo?(this.fft=new p(e),this.outputBufferSize=2*e):(this.fft=new m(e),this.outputBufferSize=this.fft.bufferSize)}realTransform(e,t){this.fft.realTransform(e,t)}transform(e,t){this.fft.transform(e,t)}}function g(e,t){if(t%2==0||t<=0)throw new Error("Window size must be a positive odd number");const s=new e.constructor(e.length),r=new e.constructor(t),o=Math.floor(t/2);for(let t=0;t<e.length;++t){let n=0;for(let s=-o;s<=o;++s){let o=t+s;o<0?o=Math.abs(o):o>=e.length&&(o=2*(e.length-1)-o),r[n++]=e[o]}r.sort(),s[t]=r[o]}return s}function f(e,t){const s=Math.pow(10,t);return Math.round(e*s)/s}function w(e){const t=Math.round(e);return Math.abs(e)%1==.5?t%2==0?t:t-1:t}function M(e){const t=e.length,s=e[0].length,r=[t+1,s+1],o=Array.from({length:r[0]},(()=>Array(r[1]).fill(1/0)));o[0][0]=0;const n=Array.from({length:r[0]},(()=>Array(r[1]).fill(-1)));for(let t=1;t<r[1];++t)for(let s=1;s<r[0];++s){const r=o[s-1][t-1],i=o[s-1][t],a=o[s][t-1];let l,c;r<i&&r<a?(l=r,c=0):i<r&&i<a?(l=i,c=1):(l=a,c=2),o[s][t]=e[s-1][t-1]+l,n[s][t]=c}for(let e=0;e<r[1];++e)n[0][e]=2;for(let e=0;e<r[0];++e)n[e][0]=1;let i=t,a=s,l=[],c=[];for(;i>0||a>0;)switch(l.push(i-1),c.push(a-1),n[i][a]){case 0:--i,--a;break;case 1:--i;break;case 2:--a;break;default:throw new Error(`Internal error in dynamic time warping. Unexpected trace[${i}, ${a}]. Please file a bug report.`)}return l.reverse(),c.reverse(),[l,c]}},"./src/utils/tensor.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{DataTypeMap:()=>i,Tensor:()=>a,cat:()=>b,full:()=>F,full_like:()=>C,interpolate:()=>c,interpolate_4d:()=>d,layer_norm:()=>f,matmul:()=>u,mean:()=>T,mean_pooling:()=>g,ones:()=>S,ones_like:()=>E,permute:()=>l,quantize_embeddings:()=>z,rand:()=>I,rfft:()=>_,slice:()=>h,stack:()=>k,std_mean:()=>v,topk:()=>p,zeros:()=>A,zeros_like:()=>L});var r=s("./src/utils/maths.js"),o=s("./src/backends/onnx.js"),n=s("./src/ops/registry.js");const i=Object.freeze({float32:Float32Array,float16:"undefined"!=typeof Float16Array?Float16Array:Uint16Array,float64:Float64Array,string:Array,int8:Int8Array,uint8:Uint8Array,int16:Int16Array,uint16:Uint16Array,int32:Int32Array,uint32:Uint32Array,int64:BigInt64Array,uint64:BigUint64Array,bool:Uint8Array,uint4:Uint8Array,int4:Int8Array});class a{get dims(){return this.ort_tensor.dims}set dims(e){this.ort_tensor.dims=e}get type(){return this.ort_tensor.type}get data(){return this.ort_tensor.data}get size(){return this.ort_tensor.size}get location(){return this.ort_tensor.location}ort_tensor;constructor(...e){return(0,o.isONNXTensor)(e[0])?this.ort_tensor=e[0]:this.ort_tensor=new o.Tensor(e[0],e[1],e[2]),new Proxy(this,{get:(e,t)=>{if("string"==typeof t){let s=Number(t);if(Number.isInteger(s))return e._getitem(s)}return e[t]},set:(e,t,s)=>e[t]=s})}dispose(){this.ort_tensor.dispose()}*[Symbol.iterator](){const[e,...t]=this.dims;if(t.length>0){const s=t.reduce(((e,t)=>e*t));for(let r=0;r<e;++r)yield this._subarray(r,s,t)}else yield*this.data}_getitem(e){const[t,...s]=this.dims;if(e=x(e,t),s.length>0){const t=s.reduce(((e,t)=>e*t));return this._subarray(e,t,s)}return new a(this.type,[this.data[e]],s)}indexOf(e){const t=this.data;for(let s=0;s<t.length;++s)if(t[s]==e)return s;return-1}_subarray(e,t,s){const r=e*t,o=(e+1)*t,n="subarray"in this.data?this.data.subarray(r,o):this.data.slice(r,o);return new a(this.type,n,s)}item(){const e=this.data;if(1!==e.length)throw new Error(`a Tensor with ${e.length} elements cannot be converted to Scalar`);return e[0]}tolist(){return function(e,t){const s=e.length,r=t.reduce(((e,t)=>e*t));if(s!==r)throw Error(`cannot reshape array of size ${s} into shape (${t})`);let o=e;for(let e=t.length-1;e>=0;e--)o=o.reduce(((s,r)=>{let o=s[s.length-1];return o.length<t[e]?o.push(r):s.push([r]),s}),[[]]);return o[0]}(this.data,this.dims)}sigmoid(){return this.clone().sigmoid_()}sigmoid_(){const e=this.data;for(let t=0;t<e.length;++t)e[t]=1/(1+Math.exp(-e[t]));return this}map(e){return this.clone().map_(e)}map_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]=e(t[s],s,t);return this}mul(e){return this.clone().mul_(e)}mul_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]*=e;return this}div(e){return this.clone().div_(e)}div_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]/=e;return this}add(e){return this.clone().add_(e)}add_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]+=e;return this}sub(e){return this.clone().sub_(e)}sub_(e){const t=this.data;for(let s=0;s<t.length;++s)t[s]-=e;return this}clone(){return new a(this.type,this.data.slice(),this.dims.slice())}slice(...e){const t=[],s=[];for(let r=0;r<this.dims.length;++r){let o=e[r];if(null==o)s.push([0,this.dims[r]]),t.push(this.dims[r]);else if("number"==typeof o)o=x(o,this.dims[r],r),s.push([o,o+1]);else{if(!Array.isArray(o)||2!==o.length)throw new Error(`Invalid slice: ${o}`);{let[e,n]=o;if(e=null===e?0:x(e,this.dims[r],r,!1),n=null===n?this.dims[r]:x(n,this.dims[r],r,!1),e>n)throw new Error(`Invalid slice: ${o}`);const i=[Math.max(e,0),Math.min(n,this.dims[r])];s.push(i),t.push(i[1]-i[0])}}}const r=s.map((([e,t])=>t-e)),o=r.reduce(((e,t)=>e*t)),n=this.data,i=new n.constructor(o),l=this.stride();for(let e=0;e<o;++e){let t=0;for(let o=r.length-1,n=e;o>=0;--o){const e=r[o];t+=(n%e+s[o][0])*l[o],n=Math.floor(n/e)}i[e]=n[t]}return new a(this.type,i,t)}permute(...e){return l(this,e)}transpose(...e){return this.permute(...e)}sum(e=null,t=!1){return this.norm(1,e,t)}norm(e="fro",t=null,s=!1){if("fro"===e)e=2;else if("string"==typeof e)throw Error(`Unsupported norm: ${e}`);const r=this.data,o=(t,s)=>t+s**e;if(null===t){const t=r.reduce(o,0)**(1/e);return new a(this.type,[t],[])}const[n,i,l]=y(o,this,t,s);if(1!==e)for(let t=0;t<i.length;++t)i[t]=i[t]**(1/e);return new a(n,i,l)}normalize_(e=2,t=1){t=x(t,this.dims.length);const s=this.norm(e,t,!0),r=this.data,o=s.data;for(let e=0;e<r.length;++e){let s=0;for(let r=this.dims.length-1,o=e,n=1;r>=0;--r){const e=this.dims[r];if(r!==t){s+=o%e*n,n*=this.dims[r]}o=Math.floor(o/e)}r[e]/=o[s]}return this}normalize(e=2,t=1){return this.clone().normalize_(e,t)}stride(){return function(e){const t=new Array(e.length);for(let s=e.length-1,r=1;s>=0;--s)t[s]=r,r*=e[s];return t}(this.dims)}squeeze(e=null){return new a(this.type,this.data,w(this.dims,e))}squeeze_(e=null){return this.dims=w(this.dims,e),this}unsqueeze(e=null){return new a(this.type,this.data,M(this.dims,e))}unsqueeze_(e=null){return this.dims=M(this.dims,e),this}flatten_(e=0,t=-1){t=(t+this.dims.length)%this.dims.length;let s=this.dims.slice(0,e),r=this.dims.slice(e,t+1),o=this.dims.slice(t+1);return this.dims=[...s,r.reduce(((e,t)=>e*t),1),...o],this}flatten(e=0,t=-1){return this.clone().flatten_(e,t)}view(...e){let t=-1;for(let s=0;s<e.length;++s)if(-1===e[s]){if(-1!==t)throw new Error("Only one dimension can be inferred");t=s}const s=this.data;if(-1!==t){const r=e.reduce(((e,s,r)=>r!==t?e*s:e),1);e[t]=s.length/r}return new a(this.type,s,e)}neg_(){const e=this.data;for(let t=0;t<e.length;++t)e[t]=-e[t];return this}neg(){return this.clone().neg_()}gt(e){const t=new Uint8Array(this.data.length),s=this.data;for(let r=0;r<s.length;++r)t[r]=s[r]>e?1:0;return new a("bool",t,this.dims)}lt(e){const t=new Uint8Array(this.data.length),s=this.data;for(let r=0;r<s.length;++r)t[r]=s[r]<e?1:0;return new a("bool",t,this.dims)}clamp_(e,t){const s=this.data;for(let r=0;r<s.length;++r)s[r]=Math.min(Math.max(s[r],e),t);return this}clamp(e,t){return this.clone().clamp_(e,t)}round_(){const e=this.data;for(let t=0;t<e.length;++t)e[t]=Math.round(e[t]);return this}round(){return this.clone().round_()}mean(e=null,t=!1){return T(this,e,t)}min(e=null,t=!1){if(null===e){const e=(0,r.min)(this.data)[0];return new a(this.type,[e],[])}const[s,o,n]=y(((e,t)=>Math.min(e,t)),this,e,t,1/0);return new a(s,o,n)}max(e=null,t=!1){if(null===e){const e=(0,r.max)(this.data)[0];return new a(this.type,[e],[])}const[s,o,n]=y(((e,t)=>Math.max(e,t)),this,e,t,-1/0);return new a(s,o,n)}argmin(e=null,t=!1){if(null!==e)throw new Error("`dim !== null` not yet implemented.");const s=(0,r.min)(this.data)[1];return new a("int64",[BigInt(s)],[])}argmax(e=null,t=!1){if(null!==e)throw new Error("`dim !== null` not yet implemented.");const s=(0,r.max)(this.data)[1];return new a("int64",[BigInt(s)],[])}to(e){if(this.type===e)return this;if(!i.hasOwnProperty(e))throw new Error(`Unsupported type: ${e}`);let t;const s=["int64","uint64"].includes(this.type),r=["int64","uint64"].includes(e);return s&&!r?t=Number:!s&&r&&(t=BigInt),new a(e,i[e].from(this.data,t),this.dims)}}function l(e,t){const[s,o]=(0,r.permute_data)(e.data,e.dims,t);return new a(e.type,s,o)}function c(e,[t,s],o="bilinear",n=!1){const i=e.dims.at(-3)??1,l=e.dims.at(-2),c=e.dims.at(-1);let d=(0,r.interpolate_data)(e.data,[i,l,c],[t,s],o,n);return new a(e.type,d,[i,t,s])}async function d(e,{size:t=null,mode:s="bilinear"}={}){if(4!==e.dims.length)throw new Error("`interpolate_4d` currently only supports 4D input.");if(!t)throw new Error("`interpolate_4d` requires a `size` argument.");let r,o;if(2===t.length)r=[...e.dims.slice(0,2),...t];else if(3===t.length)r=[e.dims[0],...t];else{if(4!==t.length)throw new Error("`size` must be of length 2, 3, or 4.");r=t}if("nearest"===s)o=await n.TensorOpRegistry.nearest_interpolate_4d;else if("bilinear"===s)o=await n.TensorOpRegistry.bilinear_interpolate_4d;else{if("bicubic"!==s)throw new Error(`Unsupported mode: ${s}`);o=await n.TensorOpRegistry.bicubic_interpolate_4d}const i=new a("int64",new BigInt64Array(r.map(BigInt)),[r.length]);return await o({x:e,s:i})}async function u(e,t){const s=await n.TensorOpRegistry.matmul;return await s({a:e,b:t})}async function _(e,t){const s=await n.TensorOpRegistry.rfft;return await s({x:e,a:t})}async function p(e,t){const s=await n.TensorOpRegistry.top_k;return t=null==t?e.dims.at(-1):Math.min(t,e.dims.at(-1)),await s({x:e,k:new a("int64",[BigInt(t)],[1])})}const m=e=>new a("int64",e,[e.length]);async function h(e,t,s,r,o){const i=await n.TensorOpRegistry.slice;return await i({x:e,s:m(t),e:m(s),a:m(r),t:m(o??new Array(r.length).fill(1))})}function g(e,t){const s=e.data,r=t.data,o=[e.dims[0],e.dims[2]],n=new s.constructor(o[0]*o[1]),[i,l,c]=e.dims;let d=0;for(let e=0;e<i;++e){const t=e*c*l;for(let o=0;o<c;++o){let i=0,a=0;const u=e*l,_=t+o;for(let e=0;e<l;++e){const t=Number(r[u+e]);a+=t,i+=s[_+e*c]*t}const p=i/a;n[d++]=p}}return new a(e.type,n,o)}function f(e,t,{eps:s=1e-5}={}){if(2!==e.dims.length)throw new Error("`layer_norm` currently only supports 2D input.");const[r,o]=e.dims;if(1!==t.length&&t[0]!==o)throw new Error("`normalized_shape` must be a 1D array with shape `[input.dims[1]]`.");const[n,i]=v(e,1,0,!0),l=n.data,c=i.data,d=e.data,u=new d.constructor(d.length);for(let e=0;e<r;++e){const t=e*o;for(let r=0;r<o;++r){const o=t+r;u[o]=(d[o]-c[e])/(l[e]+s)}}return new a(e.type,u,e.dims)}function w(e,t){return e=e.slice(),null===t?e=e.filter((e=>1!==e)):"number"==typeof t?1===e[t]&&e.splice(t,1):Array.isArray(t)&&(e=e.filter(((e,s)=>1!==e||!t.includes(s)))),e}function M(e,t){return t=x(t,e.length+1),(e=e.slice()).splice(t,0,1),e}function x(e,t,s=null,r=!0){if(e<-t||e>=t){if(r)throw new Error(`IndexError: index ${e} is out of bounds for dimension${null===s?"":" "+s} with size ${t}`);return e<-t?0:t}return e<0&&(e=(e%t+t)%t),e}function b(e,t=0){t=x(t,e[0].dims.length);const s=e[0].dims.slice();s[t]=e.reduce(((e,s)=>e+s.dims[t]),0);const r=s.reduce(((e,t)=>e*t),1),o=new e[0].data.constructor(r),n=e[0].type;if(0===t){let t=0;for(const s of e){const e=s.data;o.set(e,t),t+=e.length}}else{let r=0;for(let n=0;n<e.length;++n){const{data:i,dims:a}=e[n];for(let e=0;e<i.length;++e){let n=0;for(let o=a.length-1,i=e,l=1;o>=0;--o){const e=a[o];let c=i%e;o===t&&(c+=r),n+=c*l,l*=s[o],i=Math.floor(i/e)}o[n]=i[e]}r+=a[t]}}return new a(n,o,s)}function k(e,t=0){return b(e.map((e=>e.unsqueeze(t))),t)}function y(e,t,s=null,r=!1,o=null){const n=t.data,i=t.dims;s=x(s,i.length);const a=i.slice();a[s]=1;const l=new n.constructor(n.length/i[s]);null!==o&&l.fill(o);for(let t=0;t<n.length;++t){let r=0;for(let e=i.length-1,o=t,n=1;e>=0;--e){const t=i[e];if(e!==s){r+=o%t*n,n*=a[e]}o=Math.floor(o/t)}l[r]=e(l[r],n[t],t,r)}return r||a.splice(s,1),[t.type,l,a]}function v(e,t=null,s=1,r=!1){const o=e.data,n=e.dims;if(null===t){const t=o.reduce(((e,t)=>e+t),0)/o.length,r=Math.sqrt(o.reduce(((e,s)=>e+(s-t)**2),0)/(o.length-s)),n=new a(e.type,[t],[]);return[new a(e.type,[r],[]),n]}const i=T(e,t=x(t,n.length),r),l=i.data,[c,d,u]=y(((e,t,s,r)=>e+(t-l[r])**2),e,t,r);for(let e=0;e<d.length;++e)d[e]=Math.sqrt(d[e]/(n[t]-s));return[new a(c,d,u),i]}function T(e,t=null,s=!1){const r=e.dims,o=e.data;if(null===t){const t=o.reduce(((e,t)=>e+t),0);return new a(e.type,[t/o.length],[])}t=x(t,r.length);const[n,i,l]=y(((e,t)=>e+t),e,t,s);if(1!==r[t])for(let e=0;e<i.length;++e)i[e]/=r[t];return new a(n,i,l)}function P(e,t,s,r){const o=e.reduce(((e,t)=>e*t),1);return new a(s,new r(o).fill(t),e)}function F(e,t){let s,r;if("number"==typeof t)s="float32",r=Float32Array;else if("bigint"==typeof t)s="int64",r=BigInt64Array;else{if("boolean"!=typeof t)throw new Error("Unsupported data type: "+typeof t);s="bool",r=Uint8Array}return P(e,t,s,r)}function C(e,t){return F(e.dims,t)}function S(e){return P(e,1n,"int64",BigInt64Array)}function E(e){return S(e.dims)}function A(e){return P(e,0n,"int64",BigInt64Array)}function L(e){return A(e.dims)}function I(e){const t=e.reduce(((e,t)=>e*t),1);return new a("float32",Float32Array.from({length:t},(()=>Math.random())),e)}function z(e,t){if(2!==e.dims.length)throw new Error("The tensor must have 2 dimensions");if(e.dims.at(-1)%8!=0)throw new Error("The last dimension of the tensor must be a multiple of 8");if(!["binary","ubinary"].includes(t))throw new Error("The precision must be either 'binary' or 'ubinary'");const s="binary"===t,r=s?"int8":"uint8",o=s?Int8Array:Uint8Array,n=e.data,i=new o(n.length/8);for(let e=0;e<n.length;++e){const t=n[e]>0?1:0,r=Math.floor(e/8),o=e%8;i[r]|=t<<7-o,s&&0===o&&(i[r]-=128)}return new a(r,i,[e.dims[0],e.dims[1]/8])}},"./src/utils/video.js":(e,t,s)=>{"use strict";s.r(t),s.d(t,{RawVideo:()=>i,RawVideoFrame:()=>n,load_video:()=>a});var r=s("./src/utils/image.js"),o=s("./src/env.js");class n{constructor(e,t){this.image=e,this.timestamp=t}}class i{constructor(e,t){e.length>0&&e[0]instanceof r.RawImage&&(e=e.map(((s,r)=>new n(s,(r+1)/(e.length+1)*t)))),this.frames=e,this.duration=t}get width(){return this.frames[0].image.width}get height(){return this.frames[0].image.height}get fps(){return this.frames.length/this.duration}}async function a(e,{num_frames:t=null,fps:s=null}={}){if(!o.apis.IS_BROWSER_ENV)throw new Error("`load_video` is currently only supported in browser environments.");if(null==t&&null==s)throw new Error("Either num_frames or fps must be provided.");const a=[],l=document.createElement("video");if(l.crossOrigin="anonymous",l.muted=!0,"string"==typeof e)l.src=e;else if(e instanceof Blob)l.src=URL.createObjectURL(e);else{if(!(e instanceof HTMLVideoElement))throw new Error("Invalid URL or video element provided.");l.src=e.src}if(await new Promise((e=>l.onloadedmetadata=e)),l.seekable.start(0)===l.seekable.end(0)){const e=await fetch(l.src),t=await e.blob();l.src=URL.createObjectURL(t),await new Promise((e=>l.onloadedmetadata=e))}const c=l.duration;let d,u;null!=t?(d=t,u=1===t?0:c/(t-1)):(u=1/s,d=Math.floor(c/u));let _=[];for(let e=0;e<d;++e)_.push(1===t?c/2:e*u);const p=document.createElement("canvas");p.width=l.videoWidth,p.height=l.videoHeight;const m=p.getContext("2d",{willReadFrequently:!0});for(const e of _){l.currentTime=e,await new Promise((e=>{l.onseeked=e})),m.drawImage(l,0,0,p.width,p.height);const t=m.getImageData(0,0,p.width,p.height),s=new r.RawImage(t.data,p.width,p.height,4),o=new n(s,e);a.push(o)}return l.remove(),new i(a,c)}}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var n=r[e]={exports:{}};return s[e](n,n.exports,o),n.exports}t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,o.t=function(s,r){if(1&r&&(s=this(s)),8&r)return s;if("object"==typeof s&&s){if(4&r&&s.__esModule)return s;if(16&r&&"function"==typeof s.then)return s}var n=Object.create(null);o.r(n);var i={};e=e||[null,t({}),t([]),t(t)];for(var a=2&r&&s;"object"==typeof a&&!~e.indexOf(a);a=t(a))Object.getOwnPropertyNames(a).forEach((e=>i[e]=()=>s[e]));return i.default=()=>s,o.d(n,i),n},o.d=(e,t)=>{for(var s in t)o.o(t,s)&&!o.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";o.r(n),o.d(n,{ASTFeatureExtractor:()=>p.ASTFeatureExtractor,ASTForAudioClassification:()=>s.ASTForAudioClassification,ASTModel:()=>s.ASTModel,ASTPreTrainedModel:()=>s.ASTPreTrainedModel,AlbertForMaskedLM:()=>s.AlbertForMaskedLM,AlbertForQuestionAnswering:()=>s.AlbertForQuestionAnswering,AlbertForSequenceClassification:()=>s.AlbertForSequenceClassification,AlbertModel:()=>s.AlbertModel,AlbertPreTrainedModel:()=>s.AlbertPreTrainedModel,AlbertTokenizer:()=>r.AlbertTokenizer,AudioClassificationPipeline:()=>t.AudioClassificationPipeline,AutoConfig:()=>i.AutoConfig,AutoFeatureExtractor:()=>m.AutoFeatureExtractor,AutoImageProcessor:()=>f.AutoImageProcessor,AutoModel:()=>s.AutoModel,AutoModelForAudioClassification:()=>s.AutoModelForAudioClassification,AutoModelForAudioFrameClassification:()=>s.AutoModelForAudioFrameClassification,AutoModelForAudioTextToText:()=>s.AutoModelForAudioTextToText,AutoModelForCTC:()=>s.AutoModelForCTC,AutoModelForCausalLM:()=>s.AutoModelForCausalLM,AutoModelForDepthEstimation:()=>s.AutoModelForDepthEstimation,AutoModelForDocumentQuestionAnswering:()=>s.AutoModelForDocumentQuestionAnswering,AutoModelForImageClassification:()=>s.AutoModelForImageClassification,AutoModelForImageFeatureExtraction:()=>s.AutoModelForImageFeatureExtraction,AutoModelForImageMatting:()=>s.AutoModelForImageMatting,AutoModelForImageSegmentation:()=>s.AutoModelForImageSegmentation,AutoModelForImageTextToText:()=>s.AutoModelForImageTextToText,AutoModelForImageToImage:()=>s.AutoModelForImageToImage,AutoModelForMaskGeneration:()=>s.AutoModelForMaskGeneration,AutoModelForMaskedLM:()=>s.AutoModelForMaskedLM,AutoModelForNormalEstimation:()=>s.AutoModelForNormalEstimation,AutoModelForObjectDetection:()=>s.AutoModelForObjectDetection,AutoModelForPoseEstimation:()=>s.AutoModelForPoseEstimation,AutoModelForQuestionAnswering:()=>s.AutoModelForQuestionAnswering,AutoModelForSemanticSegmentation:()=>s.AutoModelForSemanticSegmentation,AutoModelForSeq2SeqLM:()=>s.AutoModelForSeq2SeqLM,AutoModelForSequenceClassification:()=>s.AutoModelForSequenceClassification,AutoModelForSpeechSeq2Seq:()=>s.AutoModelForSpeechSeq2Seq,AutoModelForTextToSpectrogram:()=>s.AutoModelForTextToSpectrogram,AutoModelForTextToWaveform:()=>s.AutoModelForTextToWaveform,AutoModelForTokenClassification:()=>s.AutoModelForTokenClassification,AutoModelForUniversalSegmentation:()=>s.AutoModelForUniversalSegmentation,AutoModelForVision2Seq:()=>s.AutoModelForVision2Seq,AutoModelForXVector:()=>s.AutoModelForXVector,AutoModelForZeroShotObjectDetection:()=>s.AutoModelForZeroShotObjectDetection,AutoProcessor:()=>x.AutoProcessor,AutoTokenizer:()=>r.AutoTokenizer,AutomaticSpeechRecognitionPipeline:()=>t.AutomaticSpeechRecognitionPipeline,BackgroundRemovalPipeline:()=>t.BackgroundRemovalPipeline,BartForConditionalGeneration:()=>s.BartForConditionalGeneration,BartForSequenceClassification:()=>s.BartForSequenceClassification,BartModel:()=>s.BartModel,BartPretrainedModel:()=>s.BartPretrainedModel,BartTokenizer:()=>r.BartTokenizer,BaseModelOutput:()=>s.BaseModelOutput,BaseStreamer:()=>b.BaseStreamer,BeitFeatureExtractor:()=>g.BeitFeatureExtractor,BeitForImageClassification:()=>s.BeitForImageClassification,BeitModel:()=>s.BeitModel,BeitPreTrainedModel:()=>s.BeitPreTrainedModel,BertForMaskedLM:()=>s.BertForMaskedLM,BertForQuestionAnswering:()=>s.BertForQuestionAnswering,BertForSequenceClassification:()=>s.BertForSequenceClassification,BertForTokenClassification:()=>s.BertForTokenClassification,BertModel:()=>s.BertModel,BertPreTrainedModel:()=>s.BertPreTrainedModel,BertTokenizer:()=>r.BertTokenizer,BitImageProcessor:()=>g.BitImageProcessor,BlenderbotForConditionalGeneration:()=>s.BlenderbotForConditionalGeneration,BlenderbotModel:()=>s.BlenderbotModel,BlenderbotPreTrainedModel:()=>s.BlenderbotPreTrainedModel,BlenderbotSmallForConditionalGeneration:()=>s.BlenderbotSmallForConditionalGeneration,BlenderbotSmallModel:()=>s.BlenderbotSmallModel,BlenderbotSmallPreTrainedModel:()=>s.BlenderbotSmallPreTrainedModel,BlenderbotSmallTokenizer:()=>r.BlenderbotSmallTokenizer,BlenderbotTokenizer:()=>r.BlenderbotTokenizer,BloomForCausalLM:()=>s.BloomForCausalLM,BloomModel:()=>s.BloomModel,BloomPreTrainedModel:()=>s.BloomPreTrainedModel,BloomTokenizer:()=>r.BloomTokenizer,CLIPFeatureExtractor:()=>g.CLIPFeatureExtractor,CLIPImageProcessor:()=>g.CLIPImageProcessor,CLIPModel:()=>s.CLIPModel,CLIPPreTrainedModel:()=>s.CLIPPreTrainedModel,CLIPSegForImageSegmentation:()=>s.CLIPSegForImageSegmentation,CLIPSegModel:()=>s.CLIPSegModel,CLIPSegPreTrainedModel:()=>s.CLIPSegPreTrainedModel,CLIPTextModel:()=>s.CLIPTextModel,CLIPTextModelWithProjection:()=>s.CLIPTextModelWithProjection,CLIPTokenizer:()=>r.CLIPTokenizer,CLIPVisionModel:()=>s.CLIPVisionModel,CLIPVisionModelWithProjection:()=>s.CLIPVisionModelWithProjection,CamembertForMaskedLM:()=>s.CamembertForMaskedLM,CamembertForQuestionAnswering:()=>s.CamembertForQuestionAnswering,CamembertForSequenceClassification:()=>s.CamembertForSequenceClassification,CamembertForTokenClassification:()=>s.CamembertForTokenClassification,CamembertModel:()=>s.CamembertModel,CamembertPreTrainedModel:()=>s.CamembertPreTrainedModel,CamembertTokenizer:()=>r.CamembertTokenizer,CausalLMOutput:()=>s.CausalLMOutput,CausalLMOutputWithPast:()=>s.CausalLMOutputWithPast,ChineseCLIPFeatureExtractor:()=>g.ChineseCLIPFeatureExtractor,ChineseCLIPModel:()=>s.ChineseCLIPModel,ChineseCLIPPreTrainedModel:()=>s.ChineseCLIPPreTrainedModel,ClapAudioModelWithProjection:()=>s.ClapAudioModelWithProjection,ClapFeatureExtractor:()=>p.ClapFeatureExtractor,ClapModel:()=>s.ClapModel,ClapPreTrainedModel:()=>s.ClapPreTrainedModel,ClapTextModelWithProjection:()=>s.ClapTextModelWithProjection,ClassifierFreeGuidanceLogitsProcessor:()=>y.ClassifierFreeGuidanceLogitsProcessor,CodeGenForCausalLM:()=>s.CodeGenForCausalLM,CodeGenModel:()=>s.CodeGenModel,CodeGenPreTrainedModel:()=>s.CodeGenPreTrainedModel,CodeGenTokenizer:()=>r.CodeGenTokenizer,CodeLlamaTokenizer:()=>r.CodeLlamaTokenizer,CohereForCausalLM:()=>s.CohereForCausalLM,CohereModel:()=>s.CohereModel,CoherePreTrainedModel:()=>s.CoherePreTrainedModel,CohereTokenizer:()=>r.CohereTokenizer,ConvBertForMaskedLM:()=>s.ConvBertForMaskedLM,ConvBertForQuestionAnswering:()=>s.ConvBertForQuestionAnswering,ConvBertForSequenceClassification:()=>s.ConvBertForSequenceClassification,ConvBertForTokenClassification:()=>s.ConvBertForTokenClassification,ConvBertModel:()=>s.ConvBertModel,ConvBertPreTrainedModel:()=>s.ConvBertPreTrainedModel,ConvBertTokenizer:()=>r.ConvBertTokenizer,ConvNextFeatureExtractor:()=>g.ConvNextFeatureExtractor,ConvNextForImageClassification:()=>s.ConvNextForImageClassification,ConvNextImageProcessor:()=>g.ConvNextImageProcessor,ConvNextModel:()=>s.ConvNextModel,ConvNextPreTrainedModel:()=>s.ConvNextPreTrainedModel,ConvNextV2ForImageClassification:()=>s.ConvNextV2ForImageClassification,ConvNextV2Model:()=>s.ConvNextV2Model,ConvNextV2PreTrainedModel:()=>s.ConvNextV2PreTrainedModel,DFineForObjectDetection:()=>s.DFineForObjectDetection,DFineModel:()=>s.DFineModel,DFinePreTrainedModel:()=>s.DFinePreTrainedModel,DPTFeatureExtractor:()=>g.DPTFeatureExtractor,DPTForDepthEstimation:()=>s.DPTForDepthEstimation,DPTImageProcessor:()=>g.DPTImageProcessor,DPTModel:()=>s.DPTModel,DPTPreTrainedModel:()=>s.DPTPreTrainedModel,DacDecoderModel:()=>s.DacDecoderModel,DacDecoderOutput:()=>s.DacDecoderOutput,DacEncoderModel:()=>s.DacEncoderModel,DacEncoderOutput:()=>s.DacEncoderOutput,DacFeatureExtractor:()=>p.DacFeatureExtractor,DacModel:()=>s.DacModel,DacPreTrainedModel:()=>s.DacPreTrainedModel,DataTypeMap:()=>d.DataTypeMap,DebertaForMaskedLM:()=>s.DebertaForMaskedLM,DebertaForQuestionAnswering:()=>s.DebertaForQuestionAnswering,DebertaForSequenceClassification:()=>s.DebertaForSequenceClassification,DebertaForTokenClassification:()=>s.DebertaForTokenClassification,DebertaModel:()=>s.DebertaModel,DebertaPreTrainedModel:()=>s.DebertaPreTrainedModel,DebertaTokenizer:()=>r.DebertaTokenizer,DebertaV2ForMaskedLM:()=>s.DebertaV2ForMaskedLM,DebertaV2ForQuestionAnswering:()=>s.DebertaV2ForQuestionAnswering,DebertaV2ForSequenceClassification:()=>s.DebertaV2ForSequenceClassification,DebertaV2ForTokenClassification:()=>s.DebertaV2ForTokenClassification,DebertaV2Model:()=>s.DebertaV2Model,DebertaV2PreTrainedModel:()=>s.DebertaV2PreTrainedModel,DebertaV2Tokenizer:()=>r.DebertaV2Tokenizer,DecisionTransformerModel:()=>s.DecisionTransformerModel,DecisionTransformerPreTrainedModel:()=>s.DecisionTransformerPreTrainedModel,DeiTFeatureExtractor:()=>g.DeiTFeatureExtractor,DeiTForImageClassification:()=>s.DeiTForImageClassification,DeiTImageProcessor:()=>g.DeiTImageProcessor,DeiTModel:()=>s.DeiTModel,DeiTPreTrainedModel:()=>s.DeiTPreTrainedModel,DepthAnythingForDepthEstimation:()=>s.DepthAnythingForDepthEstimation,DepthAnythingPreTrainedModel:()=>s.DepthAnythingPreTrainedModel,DepthEstimationPipeline:()=>t.DepthEstimationPipeline,DepthProForDepthEstimation:()=>s.DepthProForDepthEstimation,DepthProPreTrainedModel:()=>s.DepthProPreTrainedModel,DetrFeatureExtractor:()=>g.DetrFeatureExtractor,DetrForObjectDetection:()=>s.DetrForObjectDetection,DetrForSegmentation:()=>s.DetrForSegmentation,DetrImageProcessor:()=>g.DetrImageProcessor,DetrModel:()=>s.DetrModel,DetrObjectDetectionOutput:()=>s.DetrObjectDetectionOutput,DetrPreTrainedModel:()=>s.DetrPreTrainedModel,DetrSegmentationOutput:()=>s.DetrSegmentationOutput,Dinov2ForImageClassification:()=>s.Dinov2ForImageClassification,Dinov2Model:()=>s.Dinov2Model,Dinov2PreTrainedModel:()=>s.Dinov2PreTrainedModel,Dinov2WithRegistersForImageClassification:()=>s.Dinov2WithRegistersForImageClassification,Dinov2WithRegistersModel:()=>s.Dinov2WithRegistersModel,Dinov2WithRegistersPreTrainedModel:()=>s.Dinov2WithRegistersPreTrainedModel,DistilBertForMaskedLM:()=>s.DistilBertForMaskedLM,DistilBertForQuestionAnswering:()=>s.DistilBertForQuestionAnswering,DistilBertForSequenceClassification:()=>s.DistilBertForSequenceClassification,DistilBertForTokenClassification:()=>s.DistilBertForTokenClassification,DistilBertModel:()=>s.DistilBertModel,DistilBertPreTrainedModel:()=>s.DistilBertPreTrainedModel,DistilBertTokenizer:()=>r.DistilBertTokenizer,DocumentQuestionAnsweringPipeline:()=>t.DocumentQuestionAnsweringPipeline,DonutFeatureExtractor:()=>g.DonutFeatureExtractor,DonutImageProcessor:()=>g.DonutImageProcessor,DonutSwinModel:()=>s.DonutSwinModel,DonutSwinPreTrainedModel:()=>s.DonutSwinPreTrainedModel,EfficientNetForImageClassification:()=>s.EfficientNetForImageClassification,EfficientNetImageProcessor:()=>g.EfficientNetImageProcessor,EfficientNetModel:()=>s.EfficientNetModel,EfficientNetPreTrainedModel:()=>s.EfficientNetPreTrainedModel,ElectraForMaskedLM:()=>s.ElectraForMaskedLM,ElectraForQuestionAnswering:()=>s.ElectraForQuestionAnswering,ElectraForSequenceClassification:()=>s.ElectraForSequenceClassification,ElectraForTokenClassification:()=>s.ElectraForTokenClassification,ElectraModel:()=>s.ElectraModel,ElectraPreTrainedModel:()=>s.ElectraPreTrainedModel,ElectraTokenizer:()=>r.ElectraTokenizer,EncodecFeatureExtractor:()=>p.EncodecFeatureExtractor,EosTokenCriteria:()=>k.EosTokenCriteria,Ernie4_5_ForCausalLM:()=>s.Ernie4_5_ForCausalLM,Ernie4_5_Model:()=>s.Ernie4_5_Model,Ernie4_5_PretrainedModel:()=>s.Ernie4_5_PretrainedModel,Ernie4_5_Tokenizer:()=>r.Ernie4_5_Tokenizer,EsmForMaskedLM:()=>s.EsmForMaskedLM,EsmForSequenceClassification:()=>s.EsmForSequenceClassification,EsmForTokenClassification:()=>s.EsmForTokenClassification,EsmModel:()=>s.EsmModel,EsmPreTrainedModel:()=>s.EsmPreTrainedModel,EsmTokenizer:()=>r.EsmTokenizer,ExaoneForCausalLM:()=>s.ExaoneForCausalLM,ExaoneModel:()=>s.ExaoneModel,ExaonePreTrainedModel:()=>s.ExaonePreTrainedModel,FFT:()=>u.FFT,FalconForCausalLM:()=>s.FalconForCausalLM,FalconModel:()=>s.FalconModel,FalconPreTrainedModel:()=>s.FalconPreTrainedModel,FalconTokenizer:()=>r.FalconTokenizer,FastViTForImageClassification:()=>s.FastViTForImageClassification,FastViTModel:()=>s.FastViTModel,FastViTPreTrainedModel:()=>s.FastViTPreTrainedModel,FeatureExtractionPipeline:()=>t.FeatureExtractionPipeline,FeatureExtractor:()=>_.FeatureExtractor,FillMaskPipeline:()=>t.FillMaskPipeline,Florence2ForConditionalGeneration:()=>s.Florence2ForConditionalGeneration,Florence2PreTrainedModel:()=>s.Florence2PreTrainedModel,Florence2Processor:()=>M.Florence2Processor,ForcedBOSTokenLogitsProcessor:()=>y.ForcedBOSTokenLogitsProcessor,ForcedEOSTokenLogitsProcessor:()=>y.ForcedEOSTokenLogitsProcessor,GLPNFeatureExtractor:()=>g.GLPNFeatureExtractor,GLPNForDepthEstimation:()=>s.GLPNForDepthEstimation,GLPNModel:()=>s.GLPNModel,GLPNPreTrainedModel:()=>s.GLPNPreTrainedModel,GPT2LMHeadModel:()=>s.GPT2LMHeadModel,GPT2Model:()=>s.GPT2Model,GPT2PreTrainedModel:()=>s.GPT2PreTrainedModel,GPT2Tokenizer:()=>r.GPT2Tokenizer,GPTBigCodeForCausalLM:()=>s.GPTBigCodeForCausalLM,GPTBigCodeModel:()=>s.GPTBigCodeModel,GPTBigCodePreTrainedModel:()=>s.GPTBigCodePreTrainedModel,GPTJForCausalLM:()=>s.GPTJForCausalLM,GPTJModel:()=>s.GPTJModel,GPTJPreTrainedModel:()=>s.GPTJPreTrainedModel,GPTNeoForCausalLM:()=>s.GPTNeoForCausalLM,GPTNeoModel:()=>s.GPTNeoModel,GPTNeoPreTrainedModel:()=>s.GPTNeoPreTrainedModel,GPTNeoXForCausalLM:()=>s.GPTNeoXForCausalLM,GPTNeoXModel:()=>s.GPTNeoXModel,GPTNeoXPreTrainedModel:()=>s.GPTNeoXPreTrainedModel,GPTNeoXTokenizer:()=>r.GPTNeoXTokenizer,Gemma2ForCausalLM:()=>s.Gemma2ForCausalLM,Gemma2Model:()=>s.Gemma2Model,Gemma2PreTrainedModel:()=>s.Gemma2PreTrainedModel,Gemma3ForCausalLM:()=>s.Gemma3ForCausalLM,Gemma3Model:()=>s.Gemma3Model,Gemma3PreTrainedModel:()=>s.Gemma3PreTrainedModel,Gemma3nAudioFeatureExtractor:()=>p.Gemma3nAudioFeatureExtractor,Gemma3nForConditionalGeneration:()=>s.Gemma3nForConditionalGeneration,Gemma3nPreTrainedModel:()=>s.Gemma3nPreTrainedModel,Gemma3nProcessor:()=>M.Gemma3nProcessor,GemmaForCausalLM:()=>s.GemmaForCausalLM,GemmaModel:()=>s.GemmaModel,GemmaPreTrainedModel:()=>s.GemmaPreTrainedModel,GemmaTokenizer:()=>r.GemmaTokenizer,GlmForCausalLM:()=>s.GlmForCausalLM,GlmModel:()=>s.GlmModel,GlmPreTrainedModel:()=>s.GlmPreTrainedModel,GraniteForCausalLM:()=>s.GraniteForCausalLM,GraniteModel:()=>s.GraniteModel,GranitePreTrainedModel:()=>s.GranitePreTrainedModel,Grok1Tokenizer:()=>r.Grok1Tokenizer,GroundingDinoForObjectDetection:()=>s.GroundingDinoForObjectDetection,GroundingDinoImageProcessor:()=>g.GroundingDinoImageProcessor,GroundingDinoPreTrainedModel:()=>s.GroundingDinoPreTrainedModel,GroundingDinoProcessor:()=>M.GroundingDinoProcessor,GroupViTModel:()=>s.GroupViTModel,GroupViTPreTrainedModel:()=>s.GroupViTPreTrainedModel,HeliumForCausalLM:()=>s.HeliumForCausalLM,HeliumModel:()=>s.HeliumModel,HeliumPreTrainedModel:()=>s.HeliumPreTrainedModel,HerbertTokenizer:()=>r.HerbertTokenizer,HieraForImageClassification:()=>s.HieraForImageClassification,HieraModel:()=>s.HieraModel,HieraPreTrainedModel:()=>s.HieraPreTrainedModel,HubertForCTC:()=>s.HubertForCTC,HubertForSequenceClassification:()=>s.HubertForSequenceClassification,HubertModel:()=>s.HubertModel,HubertPreTrainedModel:()=>s.HubertPreTrainedModel,IJepaForImageClassification:()=>s.IJepaForImageClassification,IJepaModel:()=>s.IJepaModel,IJepaPreTrainedModel:()=>s.IJepaPreTrainedModel,Idefics3ForConditionalGeneration:()=>s.Idefics3ForConditionalGeneration,Idefics3ImageProcessor:()=>g.Idefics3ImageProcessor,Idefics3PreTrainedModel:()=>s.Idefics3PreTrainedModel,Idefics3Processor:()=>M.Idefics3Processor,ImageClassificationPipeline:()=>t.ImageClassificationPipeline,ImageFeatureExtractionPipeline:()=>t.ImageFeatureExtractionPipeline,ImageFeatureExtractor:()=>p.ImageFeatureExtractor,ImageMattingOutput:()=>s.ImageMattingOutput,ImageProcessor:()=>h.ImageProcessor,ImageSegmentationPipeline:()=>t.ImageSegmentationPipeline,ImageToImagePipeline:()=>t.ImageToImagePipeline,ImageToTextPipeline:()=>t.ImageToTextPipeline,InterruptableStoppingCriteria:()=>k.InterruptableStoppingCriteria,JAISLMHeadModel:()=>s.JAISLMHeadModel,JAISModel:()=>s.JAISModel,JAISPreTrainedModel:()=>s.JAISPreTrainedModel,JinaCLIPImageProcessor:()=>g.JinaCLIPImageProcessor,JinaCLIPModel:()=>s.JinaCLIPModel,JinaCLIPPreTrainedModel:()=>s.JinaCLIPPreTrainedModel,JinaCLIPProcessor:()=>M.JinaCLIPProcessor,JinaCLIPTextModel:()=>s.JinaCLIPTextModel,JinaCLIPVisionModel:()=>s.JinaCLIPVisionModel,LiteWhisperForConditionalGeneration:()=>s.LiteWhisperForConditionalGeneration,LlamaForCausalLM:()=>s.LlamaForCausalLM,LlamaModel:()=>s.LlamaModel,LlamaPreTrainedModel:()=>s.LlamaPreTrainedModel,LlamaTokenizer:()=>r.LlamaTokenizer,LlavaForConditionalGeneration:()=>s.LlavaForConditionalGeneration,LlavaOnevisionForConditionalGeneration:()=>s.LlavaOnevisionForConditionalGeneration,LlavaOnevisionImageProcessor:()=>g.LlavaOnevisionImageProcessor,LlavaPreTrainedModel:()=>s.LlavaPreTrainedModel,LlavaProcessor:()=>M.LlavaProcessor,LlavaQwen2ForCausalLM:()=>s.LlavaQwen2ForCausalLM,LogitsProcessor:()=>y.LogitsProcessor,LogitsProcessorList:()=>y.LogitsProcessorList,LogitsWarper:()=>y.LogitsWarper,LongT5ForConditionalGeneration:()=>s.LongT5ForConditionalGeneration,LongT5Model:()=>s.LongT5Model,LongT5PreTrainedModel:()=>s.LongT5PreTrainedModel,M2M100ForConditionalGeneration:()=>s.M2M100ForConditionalGeneration,M2M100Model:()=>s.M2M100Model,M2M100PreTrainedModel:()=>s.M2M100PreTrainedModel,M2M100Tokenizer:()=>r.M2M100Tokenizer,MBart50Tokenizer:()=>r.MBart50Tokenizer,MBartForCausalLM:()=>s.MBartForCausalLM,MBartForConditionalGeneration:()=>s.MBartForConditionalGeneration,MBartForSequenceClassification:()=>s.MBartForSequenceClassification,MBartModel:()=>s.MBartModel,MBartPreTrainedModel:()=>s.MBartPreTrainedModel,MBartTokenizer:()=>r.MBartTokenizer,MPNetForMaskedLM:()=>s.MPNetForMaskedLM,MPNetForQuestionAnswering:()=>s.MPNetForQuestionAnswering,MPNetForSequenceClassification:()=>s.MPNetForSequenceClassification,MPNetForTokenClassification:()=>s.MPNetForTokenClassification,MPNetModel:()=>s.MPNetModel,MPNetPreTrainedModel:()=>s.MPNetPreTrainedModel,MPNetTokenizer:()=>r.MPNetTokenizer,MT5ForConditionalGeneration:()=>s.MT5ForConditionalGeneration,MT5Model:()=>s.MT5Model,MT5PreTrainedModel:()=>s.MT5PreTrainedModel,MarianMTModel:()=>s.MarianMTModel,MarianModel:()=>s.MarianModel,MarianPreTrainedModel:()=>s.MarianPreTrainedModel,MarianTokenizer:()=>r.MarianTokenizer,Mask2FormerImageProcessor:()=>g.Mask2FormerImageProcessor,MaskFormerFeatureExtractor:()=>g.MaskFormerFeatureExtractor,MaskFormerForInstanceSegmentation:()=>s.MaskFormerForInstanceSegmentation,MaskFormerImageProcessor:()=>g.MaskFormerImageProcessor,MaskFormerModel:()=>s.MaskFormerModel,MaskFormerPreTrainedModel:()=>s.MaskFormerPreTrainedModel,MaskedLMOutput:()=>s.MaskedLMOutput,MaxLengthCriteria:()=>k.MaxLengthCriteria,Metric3DForDepthEstimation:()=>s.Metric3DForDepthEstimation,Metric3DPreTrainedModel:()=>s.Metric3DPreTrainedModel,Metric3Dv2ForDepthEstimation:()=>s.Metric3Dv2ForDepthEstimation,Metric3Dv2PreTrainedModel:()=>s.Metric3Dv2PreTrainedModel,MgpstrForSceneTextRecognition:()=>s.MgpstrForSceneTextRecognition,MgpstrModelOutput:()=>s.MgpstrModelOutput,MgpstrPreTrainedModel:()=>s.MgpstrPreTrainedModel,MgpstrProcessor:()=>M.MgpstrProcessor,MgpstrTokenizer:()=>r.MgpstrTokenizer,MimiDecoderModel:()=>s.MimiDecoderModel,MimiDecoderOutput:()=>s.MimiDecoderOutput,MimiEncoderModel:()=>s.MimiEncoderModel,MimiEncoderOutput:()=>s.MimiEncoderOutput,MimiModel:()=>s.MimiModel,MimiPreTrainedModel:()=>s.MimiPreTrainedModel,MinLengthLogitsProcessor:()=>y.MinLengthLogitsProcessor,MinNewTokensLengthLogitsProcessor:()=>y.MinNewTokensLengthLogitsProcessor,MistralForCausalLM:()=>s.MistralForCausalLM,MistralModel:()=>s.MistralModel,MistralPreTrainedModel:()=>s.MistralPreTrainedModel,MobileBertForMaskedLM:()=>s.MobileBertForMaskedLM,MobileBertForQuestionAnswering:()=>s.MobileBertForQuestionAnswering,MobileBertForSequenceClassification:()=>s.MobileBertForSequenceClassification,MobileBertModel:()=>s.MobileBertModel,MobileBertPreTrainedModel:()=>s.MobileBertPreTrainedModel,MobileBertTokenizer:()=>r.MobileBertTokenizer,MobileLLMForCausalLM:()=>s.MobileLLMForCausalLM,MobileLLMModel:()=>s.MobileLLMModel,MobileLLMPreTrainedModel:()=>s.MobileLLMPreTrainedModel,MobileNetV1FeatureExtractor:()=>g.MobileNetV1FeatureExtractor,MobileNetV1ForImageClassification:()=>s.MobileNetV1ForImageClassification,MobileNetV1ForSemanticSegmentation:()=>s.MobileNetV1ForSemanticSegmentation,MobileNetV1ImageProcessor:()=>g.MobileNetV1ImageProcessor,MobileNetV1Model:()=>s.MobileNetV1Model,MobileNetV1PreTrainedModel:()=>s.MobileNetV1PreTrainedModel,MobileNetV2FeatureExtractor:()=>g.MobileNetV2FeatureExtractor,MobileNetV2ForImageClassification:()=>s.MobileNetV2ForImageClassification,MobileNetV2ForSemanticSegmentation:()=>s.MobileNetV2ForSemanticSegmentation,MobileNetV2ImageProcessor:()=>g.MobileNetV2ImageProcessor,MobileNetV2Model:()=>s.MobileNetV2Model,MobileNetV2PreTrainedModel:()=>s.MobileNetV2PreTrainedModel,MobileNetV3FeatureExtractor:()=>g.MobileNetV3FeatureExtractor,MobileNetV3ForImageClassification:()=>s.MobileNetV3ForImageClassification,MobileNetV3ForSemanticSegmentation:()=>s.MobileNetV3ForSemanticSegmentation,MobileNetV3ImageProcessor:()=>g.MobileNetV3ImageProcessor,MobileNetV3Model:()=>s.MobileNetV3Model,MobileNetV3PreTrainedModel:()=>s.MobileNetV3PreTrainedModel,MobileNetV4FeatureExtractor:()=>g.MobileNetV4FeatureExtractor,MobileNetV4ForImageClassification:()=>s.MobileNetV4ForImageClassification,MobileNetV4ForSemanticSegmentation:()=>s.MobileNetV4ForSemanticSegmentation,MobileNetV4ImageProcessor:()=>g.MobileNetV4ImageProcessor,MobileNetV4Model:()=>s.MobileNetV4Model,MobileNetV4PreTrainedModel:()=>s.MobileNetV4PreTrainedModel,MobileViTFeatureExtractor:()=>g.MobileViTFeatureExtractor,MobileViTForImageClassification:()=>s.MobileViTForImageClassification,MobileViTImageProcessor:()=>g.MobileViTImageProcessor,MobileViTModel:()=>s.MobileViTModel,MobileViTPreTrainedModel:()=>s.MobileViTPreTrainedModel,MobileViTV2ForImageClassification:()=>s.MobileViTV2ForImageClassification,MobileViTV2Model:()=>s.MobileViTV2Model,MobileViTV2PreTrainedModel:()=>s.MobileViTV2PreTrainedModel,ModelOutput:()=>s.ModelOutput,ModernBertForMaskedLM:()=>s.ModernBertForMaskedLM,ModernBertForSequenceClassification:()=>s.ModernBertForSequenceClassification,ModernBertForTokenClassification:()=>s.ModernBertForTokenClassification,ModernBertModel:()=>s.ModernBertModel,ModernBertPreTrainedModel:()=>s.ModernBertPreTrainedModel,Moondream1ForConditionalGeneration:()=>s.Moondream1ForConditionalGeneration,MoonshineFeatureExtractor:()=>p.MoonshineFeatureExtractor,MoonshineForConditionalGeneration:()=>s.MoonshineForConditionalGeneration,MoonshineModel:()=>s.MoonshineModel,MoonshinePreTrainedModel:()=>s.MoonshinePreTrainedModel,MoonshineProcessor:()=>M.MoonshineProcessor,MptForCausalLM:()=>s.MptForCausalLM,MptModel:()=>s.MptModel,MptPreTrainedModel:()=>s.MptPreTrainedModel,MultiModalityCausalLM:()=>s.MultiModalityCausalLM,MultiModalityPreTrainedModel:()=>s.MultiModalityPreTrainedModel,MusicgenForCausalLM:()=>s.MusicgenForCausalLM,MusicgenForConditionalGeneration:()=>s.MusicgenForConditionalGeneration,MusicgenModel:()=>s.MusicgenModel,MusicgenPreTrainedModel:()=>s.MusicgenPreTrainedModel,NeoBertForMaskedLM:()=>s.NeoBertForMaskedLM,NeoBertForQuestionAnswering:()=>s.NeoBertForQuestionAnswering,NeoBertForSequenceClassification:()=>s.NeoBertForSequenceClassification,NeoBertForTokenClassification:()=>s.NeoBertForTokenClassification,NeoBertModel:()=>s.NeoBertModel,NeoBertPreTrainedModel:()=>s.NeoBertPreTrainedModel,NllbTokenizer:()=>r.NllbTokenizer,NoBadWordsLogitsProcessor:()=>y.NoBadWordsLogitsProcessor,NoRepeatNGramLogitsProcessor:()=>y.NoRepeatNGramLogitsProcessor,NomicBertModel:()=>s.NomicBertModel,NomicBertPreTrainedModel:()=>s.NomicBertPreTrainedModel,NougatImageProcessor:()=>g.NougatImageProcessor,NougatTokenizer:()=>r.NougatTokenizer,OPTForCausalLM:()=>s.OPTForCausalLM,OPTModel:()=>s.OPTModel,OPTPreTrainedModel:()=>s.OPTPreTrainedModel,ObjectDetectionPipeline:()=>t.ObjectDetectionPipeline,Olmo2ForCausalLM:()=>s.Olmo2ForCausalLM,Olmo2Model:()=>s.Olmo2Model,Olmo2PreTrainedModel:()=>s.Olmo2PreTrainedModel,OlmoForCausalLM:()=>s.OlmoForCausalLM,OlmoModel:()=>s.OlmoModel,OlmoPreTrainedModel:()=>s.OlmoPreTrainedModel,OpenELMForCausalLM:()=>s.OpenELMForCausalLM,OpenELMModel:()=>s.OpenELMModel,OpenELMPreTrainedModel:()=>s.OpenELMPreTrainedModel,OwlViTFeatureExtractor:()=>g.OwlViTFeatureExtractor,OwlViTForObjectDetection:()=>s.OwlViTForObjectDetection,OwlViTImageProcessor:()=>g.OwlViTImageProcessor,OwlViTModel:()=>s.OwlViTModel,OwlViTPreTrainedModel:()=>s.OwlViTPreTrainedModel,OwlViTProcessor:()=>M.OwlViTProcessor,Owlv2ForObjectDetection:()=>s.Owlv2ForObjectDetection,Owlv2ImageProcessor:()=>g.Owlv2ImageProcessor,Owlv2Model:()=>s.Owlv2Model,Owlv2PreTrainedModel:()=>s.Owlv2PreTrainedModel,PaliGemmaForConditionalGeneration:()=>s.PaliGemmaForConditionalGeneration,PaliGemmaPreTrainedModel:()=>s.PaliGemmaPreTrainedModel,PaliGemmaProcessor:()=>M.PaliGemmaProcessor,PatchTSMixerForPrediction:()=>s.PatchTSMixerForPrediction,PatchTSMixerModel:()=>s.PatchTSMixerModel,PatchTSMixerPreTrainedModel:()=>s.PatchTSMixerPreTrainedModel,PatchTSTForPrediction:()=>s.PatchTSTForPrediction,PatchTSTModel:()=>s.PatchTSTModel,PatchTSTPreTrainedModel:()=>s.PatchTSTPreTrainedModel,Phi3ForCausalLM:()=>s.Phi3ForCausalLM,Phi3Model:()=>s.Phi3Model,Phi3PreTrainedModel:()=>s.Phi3PreTrainedModel,Phi3VForCausalLM:()=>s.Phi3VForCausalLM,Phi3VImageProcessor:()=>g.Phi3VImageProcessor,Phi3VPreTrainedModel:()=>s.Phi3VPreTrainedModel,Phi3VProcessor:()=>M.Phi3VProcessor,PhiForCausalLM:()=>s.PhiForCausalLM,PhiModel:()=>s.PhiModel,PhiPreTrainedModel:()=>s.PhiPreTrainedModel,Pipeline:()=>t.Pipeline,PreTrainedModel:()=>s.PreTrainedModel,PreTrainedTokenizer:()=>r.PreTrainedTokenizer,PretrainedConfig:()=>i.PretrainedConfig,PretrainedMixin:()=>s.PretrainedMixin,Processor:()=>w.Processor,PvtForImageClassification:()=>s.PvtForImageClassification,PvtImageProcessor:()=>g.PvtImageProcessor,PvtModel:()=>s.PvtModel,PvtPreTrainedModel:()=>s.PvtPreTrainedModel,PyAnnoteFeatureExtractor:()=>p.PyAnnoteFeatureExtractor,PyAnnoteForAudioFrameClassification:()=>s.PyAnnoteForAudioFrameClassification,PyAnnoteModel:()=>s.PyAnnoteModel,PyAnnotePreTrainedModel:()=>s.PyAnnotePreTrainedModel,PyAnnoteProcessor:()=>M.PyAnnoteProcessor,QuestionAnsweringModelOutput:()=>s.QuestionAnsweringModelOutput,QuestionAnsweringPipeline:()=>t.QuestionAnsweringPipeline,Qwen2ForCausalLM:()=>s.Qwen2ForCausalLM,Qwen2Model:()=>s.Qwen2Model,Qwen2PreTrainedModel:()=>s.Qwen2PreTrainedModel,Qwen2Tokenizer:()=>r.Qwen2Tokenizer,Qwen2VLForConditionalGeneration:()=>s.Qwen2VLForConditionalGeneration,Qwen2VLImageProcessor:()=>g.Qwen2VLImageProcessor,Qwen2VLPreTrainedModel:()=>s.Qwen2VLPreTrainedModel,Qwen2VLProcessor:()=>M.Qwen2VLProcessor,Qwen3ForCausalLM:()=>s.Qwen3ForCausalLM,Qwen3Model:()=>s.Qwen3Model,Qwen3PreTrainedModel:()=>s.Qwen3PreTrainedModel,RFDetrForObjectDetection:()=>s.RFDetrForObjectDetection,RFDetrModel:()=>s.RFDetrModel,RFDetrObjectDetectionOutput:()=>s.RFDetrObjectDetectionOutput,RFDetrPreTrainedModel:()=>s.RFDetrPreTrainedModel,RTDetrForObjectDetection:()=>s.RTDetrForObjectDetection,RTDetrImageProcessor:()=>g.RTDetrImageProcessor,RTDetrModel:()=>s.RTDetrModel,RTDetrObjectDetectionOutput:()=>s.RTDetrObjectDetectionOutput,RTDetrPreTrainedModel:()=>s.RTDetrPreTrainedModel,RTDetrV2ForObjectDetection:()=>s.RTDetrV2ForObjectDetection,RTDetrV2Model:()=>s.RTDetrV2Model,RTDetrV2ObjectDetectionOutput:()=>s.RTDetrV2ObjectDetectionOutput,RTDetrV2PreTrainedModel:()=>s.RTDetrV2PreTrainedModel,RawAudio:()=>a.RawAudio,RawImage:()=>l.RawImage,RawVideo:()=>c.RawVideo,RawVideoFrame:()=>c.RawVideoFrame,RepetitionPenaltyLogitsProcessor:()=>y.RepetitionPenaltyLogitsProcessor,ResNetForImageClassification:()=>s.ResNetForImageClassification,ResNetModel:()=>s.ResNetModel,ResNetPreTrainedModel:()=>s.ResNetPreTrainedModel,RoFormerForMaskedLM:()=>s.RoFormerForMaskedLM,RoFormerForQuestionAnswering:()=>s.RoFormerForQuestionAnswering,RoFormerForSequenceClassification:()=>s.RoFormerForSequenceClassification,RoFormerForTokenClassification:()=>s.RoFormerForTokenClassification,RoFormerModel:()=>s.RoFormerModel,RoFormerPreTrainedModel:()=>s.RoFormerPreTrainedModel,RoFormerTokenizer:()=>r.RoFormerTokenizer,RobertaForMaskedLM:()=>s.RobertaForMaskedLM,RobertaForQuestionAnswering:()=>s.RobertaForQuestionAnswering,RobertaForSequenceClassification:()=>s.RobertaForSequenceClassification,RobertaForTokenClassification:()=>s.RobertaForTokenClassification,RobertaModel:()=>s.RobertaModel,RobertaPreTrainedModel:()=>s.RobertaPreTrainedModel,RobertaTokenizer:()=>r.RobertaTokenizer,SamImageProcessor:()=>g.SamImageProcessor,SamImageSegmentationOutput:()=>s.SamImageSegmentationOutput,SamModel:()=>s.SamModel,SamPreTrainedModel:()=>s.SamPreTrainedModel,SamProcessor:()=>M.SamProcessor,SapiensForDepthEstimation:()=>s.SapiensForDepthEstimation,SapiensForNormalEstimation:()=>s.SapiensForNormalEstimation,SapiensForSemanticSegmentation:()=>s.SapiensForSemanticSegmentation,SapiensPreTrainedModel:()=>s.SapiensPreTrainedModel,SeamlessM4TFeatureExtractor:()=>p.SeamlessM4TFeatureExtractor,SegformerFeatureExtractor:()=>g.SegformerFeatureExtractor,SegformerForImageClassification:()=>s.SegformerForImageClassification,SegformerForSemanticSegmentation:()=>s.SegformerForSemanticSegmentation,SegformerImageProcessor:()=>g.SegformerImageProcessor,SegformerModel:()=>s.SegformerModel,SegformerPreTrainedModel:()=>s.SegformerPreTrainedModel,Seq2SeqLMOutput:()=>s.Seq2SeqLMOutput,SequenceClassifierOutput:()=>s.SequenceClassifierOutput,SiglipImageProcessor:()=>g.SiglipImageProcessor,SiglipModel:()=>s.SiglipModel,SiglipPreTrainedModel:()=>s.SiglipPreTrainedModel,SiglipTextModel:()=>s.SiglipTextModel,SiglipTokenizer:()=>r.SiglipTokenizer,SiglipVisionModel:()=>s.SiglipVisionModel,SmolLM3ForCausalLM:()=>s.SmolLM3ForCausalLM,SmolLM3Model:()=>s.SmolLM3Model,SmolLM3PreTrainedModel:()=>s.SmolLM3PreTrainedModel,SmolVLMForConditionalGeneration:()=>s.SmolVLMForConditionalGeneration,SmolVLMImageProcessor:()=>g.SmolVLMImageProcessor,SmolVLMProcessor:()=>M.SmolVLMProcessor,SnacDecoderModel:()=>s.SnacDecoderModel,SnacEncoderModel:()=>s.SnacEncoderModel,SnacFeatureExtractor:()=>p.SnacFeatureExtractor,SnacModel:()=>s.SnacModel,SnacPreTrainedModel:()=>s.SnacPreTrainedModel,SpeechT5FeatureExtractor:()=>p.SpeechT5FeatureExtractor,SpeechT5ForSpeechToText:()=>s.SpeechT5ForSpeechToText,SpeechT5ForTextToSpeech:()=>s.SpeechT5ForTextToSpeech,SpeechT5HifiGan:()=>s.SpeechT5HifiGan,SpeechT5Model:()=>s.SpeechT5Model,SpeechT5PreTrainedModel:()=>s.SpeechT5PreTrainedModel,SpeechT5Processor:()=>M.SpeechT5Processor,SpeechT5Tokenizer:()=>r.SpeechT5Tokenizer,SqueezeBertForMaskedLM:()=>s.SqueezeBertForMaskedLM,SqueezeBertForQuestionAnswering:()=>s.SqueezeBertForQuestionAnswering,SqueezeBertForSequenceClassification:()=>s.SqueezeBertForSequenceClassification,SqueezeBertModel:()=>s.SqueezeBertModel,SqueezeBertPreTrainedModel:()=>s.SqueezeBertPreTrainedModel,SqueezeBertTokenizer:()=>r.SqueezeBertTokenizer,StableLmForCausalLM:()=>s.StableLmForCausalLM,StableLmModel:()=>s.StableLmModel,StableLmPreTrainedModel:()=>s.StableLmPreTrainedModel,Starcoder2ForCausalLM:()=>s.Starcoder2ForCausalLM,Starcoder2Model:()=>s.Starcoder2Model,Starcoder2PreTrainedModel:()=>s.Starcoder2PreTrainedModel,StoppingCriteria:()=>k.StoppingCriteria,StoppingCriteriaList:()=>k.StoppingCriteriaList,StyleTextToSpeech2Model:()=>s.StyleTextToSpeech2Model,StyleTextToSpeech2PreTrainedModel:()=>s.StyleTextToSpeech2PreTrainedModel,SummarizationPipeline:()=>t.SummarizationPipeline,SuppressTokensAtBeginLogitsProcessor:()=>y.SuppressTokensAtBeginLogitsProcessor,Swin2SRForImageSuperResolution:()=>s.Swin2SRForImageSuperResolution,Swin2SRImageProcessor:()=>g.Swin2SRImageProcessor,Swin2SRModel:()=>s.Swin2SRModel,Swin2SRPreTrainedModel:()=>s.Swin2SRPreTrainedModel,SwinForImageClassification:()=>s.SwinForImageClassification,SwinForSemanticSegmentation:()=>s.SwinForSemanticSegmentation,SwinModel:()=>s.SwinModel,SwinPreTrainedModel:()=>s.SwinPreTrainedModel,T5ForConditionalGeneration:()=>s.T5ForConditionalGeneration,T5Model:()=>s.T5Model,T5PreTrainedModel:()=>s.T5PreTrainedModel,T5Tokenizer:()=>r.T5Tokenizer,TableTransformerForObjectDetection:()=>s.TableTransformerForObjectDetection,TableTransformerModel:()=>s.TableTransformerModel,TableTransformerObjectDetectionOutput:()=>s.TableTransformerObjectDetectionOutput,TableTransformerPreTrainedModel:()=>s.TableTransformerPreTrainedModel,TemperatureLogitsWarper:()=>y.TemperatureLogitsWarper,Tensor:()=>d.Tensor,Text2TextGenerationPipeline:()=>t.Text2TextGenerationPipeline,TextClassificationPipeline:()=>t.TextClassificationPipeline,TextGenerationPipeline:()=>t.TextGenerationPipeline,TextStreamer:()=>b.TextStreamer,TextToAudioPipeline:()=>t.TextToAudioPipeline,TokenClassificationPipeline:()=>t.TokenClassificationPipeline,TokenClassifierOutput:()=>s.TokenClassifierOutput,TokenizerModel:()=>r.TokenizerModel,TopKLogitsWarper:()=>y.TopKLogitsWarper,TopPLogitsWarper:()=>y.TopPLogitsWarper,TrOCRForCausalLM:()=>s.TrOCRForCausalLM,TrOCRPreTrainedModel:()=>s.TrOCRPreTrainedModel,TranslationPipeline:()=>t.TranslationPipeline,UltravoxModel:()=>s.UltravoxModel,UltravoxPreTrainedModel:()=>s.UltravoxPreTrainedModel,UltravoxProcessor:()=>M.UltravoxProcessor,UniSpeechForCTC:()=>s.UniSpeechForCTC,UniSpeechForSequenceClassification:()=>s.UniSpeechForSequenceClassification,UniSpeechModel:()=>s.UniSpeechModel,UniSpeechPreTrainedModel:()=>s.UniSpeechPreTrainedModel,UniSpeechSatForAudioFrameClassification:()=>s.UniSpeechSatForAudioFrameClassification,UniSpeechSatForCTC:()=>s.UniSpeechSatForCTC,UniSpeechSatForSequenceClassification:()=>s.UniSpeechSatForSequenceClassification,UniSpeechSatModel:()=>s.UniSpeechSatModel,UniSpeechSatPreTrainedModel:()=>s.UniSpeechSatPreTrainedModel,VLChatProcessor:()=>M.VLChatProcessor,VLMImageProcessor:()=>g.VLMImageProcessor,ViTFeatureExtractor:()=>g.ViTFeatureExtractor,ViTForImageClassification:()=>s.ViTForImageClassification,ViTImageProcessor:()=>g.ViTImageProcessor,ViTMAEModel:()=>s.ViTMAEModel,ViTMAEPreTrainedModel:()=>s.ViTMAEPreTrainedModel,ViTMSNForImageClassification:()=>s.ViTMSNForImageClassification,ViTMSNModel:()=>s.ViTMSNModel,ViTMSNPreTrainedModel:()=>s.ViTMSNPreTrainedModel,ViTModel:()=>s.ViTModel,ViTPreTrainedModel:()=>s.ViTPreTrainedModel,VisionEncoderDecoderModel:()=>s.VisionEncoderDecoderModel,VitMatteForImageMatting:()=>s.VitMatteForImageMatting,VitMatteImageProcessor:()=>g.VitMatteImageProcessor,VitMattePreTrainedModel:()=>s.VitMattePreTrainedModel,VitPoseForPoseEstimation:()=>s.VitPoseForPoseEstimation,VitPoseImageProcessor:()=>g.VitPoseImageProcessor,VitPosePreTrainedModel:()=>s.VitPosePreTrainedModel,VitsModel:()=>s.VitsModel,VitsModelOutput:()=>s.VitsModelOutput,VitsPreTrainedModel:()=>s.VitsPreTrainedModel,VitsTokenizer:()=>r.VitsTokenizer,Wav2Vec2BertForCTC:()=>s.Wav2Vec2BertForCTC,Wav2Vec2BertForSequenceClassification:()=>s.Wav2Vec2BertForSequenceClassification,Wav2Vec2BertModel:()=>s.Wav2Vec2BertModel,Wav2Vec2BertPreTrainedModel:()=>s.Wav2Vec2BertPreTrainedModel,Wav2Vec2CTCTokenizer:()=>r.Wav2Vec2CTCTokenizer,Wav2Vec2FeatureExtractor:()=>p.Wav2Vec2FeatureExtractor,Wav2Vec2ForAudioFrameClassification:()=>s.Wav2Vec2ForAudioFrameClassification,Wav2Vec2ForCTC:()=>s.Wav2Vec2ForCTC,Wav2Vec2ForSequenceClassification:()=>s.Wav2Vec2ForSequenceClassification,Wav2Vec2Model:()=>s.Wav2Vec2Model,Wav2Vec2PreTrainedModel:()=>s.Wav2Vec2PreTrainedModel,Wav2Vec2Processor:()=>M.Wav2Vec2Processor,Wav2Vec2ProcessorWithLM:()=>M.Wav2Vec2ProcessorWithLM,WavLMForAudioFrameClassification:()=>s.WavLMForAudioFrameClassification,WavLMForCTC:()=>s.WavLMForCTC,WavLMForSequenceClassification:()=>s.WavLMForSequenceClassification,WavLMForXVector:()=>s.WavLMForXVector,WavLMModel:()=>s.WavLMModel,WavLMPreTrainedModel:()=>s.WavLMPreTrainedModel,WeSpeakerFeatureExtractor:()=>p.WeSpeakerFeatureExtractor,WeSpeakerResNetModel:()=>s.WeSpeakerResNetModel,WeSpeakerResNetPreTrainedModel:()=>s.WeSpeakerResNetPreTrainedModel,WhisperFeatureExtractor:()=>p.WhisperFeatureExtractor,WhisperForConditionalGeneration:()=>s.WhisperForConditionalGeneration,WhisperModel:()=>s.WhisperModel,WhisperPreTrainedModel:()=>s.WhisperPreTrainedModel,WhisperProcessor:()=>M.WhisperProcessor,WhisperTextStreamer:()=>b.WhisperTextStreamer,WhisperTimeStampLogitsProcessor:()=>y.WhisperTimeStampLogitsProcessor,WhisperTokenizer:()=>r.WhisperTokenizer,XLMForQuestionAnswering:()=>s.XLMForQuestionAnswering,XLMForSequenceClassification:()=>s.XLMForSequenceClassification,XLMForTokenClassification:()=>s.XLMForTokenClassification,XLMModel:()=>s.XLMModel,XLMPreTrainedModel:()=>s.XLMPreTrainedModel,XLMRobertaForMaskedLM:()=>s.XLMRobertaForMaskedLM,XLMRobertaForQuestionAnswering:()=>s.XLMRobertaForQuestionAnswering,XLMRobertaForSequenceClassification:()=>s.XLMRobertaForSequenceClassification,XLMRobertaForTokenClassification:()=>s.XLMRobertaForTokenClassification,XLMRobertaModel:()=>s.XLMRobertaModel,XLMRobertaPreTrainedModel:()=>s.XLMRobertaPreTrainedModel,XLMRobertaTokenizer:()=>r.XLMRobertaTokenizer,XLMTokenizer:()=>r.XLMTokenizer,XLMWithLMHeadModel:()=>s.XLMWithLMHeadModel,XVectorOutput:()=>s.XVectorOutput,YolosFeatureExtractor:()=>g.YolosFeatureExtractor,YolosForObjectDetection:()=>s.YolosForObjectDetection,YolosImageProcessor:()=>g.YolosImageProcessor,YolosModel:()=>s.YolosModel,YolosObjectDetectionOutput:()=>s.YolosObjectDetectionOutput,YolosPreTrainedModel:()=>s.YolosPreTrainedModel,ZeroShotAudioClassificationPipeline:()=>t.ZeroShotAudioClassificationPipeline,ZeroShotClassificationPipeline:()=>t.ZeroShotClassificationPipeline,ZeroShotImageClassificationPipeline:()=>t.ZeroShotImageClassificationPipeline,ZeroShotObjectDetectionPipeline:()=>t.ZeroShotObjectDetectionPipeline,bankers_round:()=>u.bankers_round,cat:()=>d.cat,cos_sim:()=>u.cos_sim,dot:()=>u.dot,dynamic_time_warping:()=>u.dynamic_time_warping,env:()=>e.env,full:()=>d.full,full_like:()=>d.full_like,getKeyValueShapes:()=>i.getKeyValueShapes,hamming:()=>a.hamming,hanning:()=>a.hanning,interpolate:()=>d.interpolate,interpolate_4d:()=>d.interpolate_4d,interpolate_data:()=>u.interpolate_data,is_chinese_char:()=>r.is_chinese_char,layer_norm:()=>d.layer_norm,load_image:()=>l.load_image,load_video:()=>c.load_video,log_softmax:()=>u.log_softmax,magnitude:()=>u.magnitude,matmul:()=>d.matmul,max:()=>u.max,mean:()=>d.mean,mean_pooling:()=>d.mean_pooling,medianFilter:()=>u.medianFilter,mel_filter_bank:()=>a.mel_filter_bank,min:()=>u.min,ones:()=>d.ones,ones_like:()=>d.ones_like,permute:()=>d.permute,permute_data:()=>u.permute_data,pipeline:()=>t.pipeline,quantize_embeddings:()=>d.quantize_embeddings,rand:()=>d.rand,read_audio:()=>a.read_audio,rfft:()=>d.rfft,round:()=>u.round,slice:()=>d.slice,softmax:()=>u.softmax,spectrogram:()=>a.spectrogram,stack:()=>d.stack,std_mean:()=>d.std_mean,topk:()=>d.topk,window_function:()=>a.window_function,zeros:()=>d.zeros,zeros_like:()=>d.zeros_like});var e=o("./src/env.js"),t=o("./src/pipelines.js"),s=o("./src/models.js"),r=o("./src/tokenizers.js"),i=o("./src/configs.js"),a=o("./src/utils/audio.js"),l=o("./src/utils/image.js"),c=o("./src/utils/video.js"),d=o("./src/utils/tensor.js"),u=o("./src/utils/maths.js"),_=o("./src/base/feature_extraction_utils.js"),p=o("./src/models/feature_extractors.js"),m=o("./src/models/auto/feature_extraction_auto.js"),h=o("./src/base/image_processors_utils.js"),g=o("./src/models/image_processors.js"),f=o("./src/models/auto/image_processing_auto.js"),w=o("./src/base/processing_utils.js"),M=o("./src/models/processors.js"),x=o("./src/models/auto/processing_auto.js"),b=o("./src/generation/streamers.js"),k=o("./src/generation/stopping_criteria.js"),y=o("./src/generation/logits_process.js")})();var i=exports;for(var a in n)i[a]=n[a];n.__esModule&&Object.defineProperty(i,"__esModule",{value:!0})})();
//# sourceMappingURL=transformers.node.min.cjs.map
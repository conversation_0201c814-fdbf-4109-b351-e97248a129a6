"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-unique-numbers";
exports.ids = ["vendor-chunks/fast-unique-numbers"];
exports.modules = {

/***/ "(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/add-unique-number.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/fast-unique-numbers/build/es2019/factories/add-unique-number.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAddUniqueNumber: () => (/* binding */ createAddUniqueNumber)\n/* harmony export */ });\nconst createAddUniqueNumber = (generateUniqueNumber) => {\n    return (set) => {\n        const number = generateUniqueNumber(set);\n        set.add(number);\n        return number;\n    };\n};\n//# sourceMappingURL=add-unique-number.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC11bmlxdWUtbnVtYmVycy9idWlsZC9lczIwMTkvZmFjdG9yaWVzL2FkZC11bmlxdWUtbnVtYmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGZhc3QtdW5pcXVlLW51bWJlcnNcXGJ1aWxkXFxlczIwMTlcXGZhY3Rvcmllc1xcYWRkLXVuaXF1ZS1udW1iZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGNyZWF0ZUFkZFVuaXF1ZU51bWJlciA9IChnZW5lcmF0ZVVuaXF1ZU51bWJlcikgPT4ge1xuICAgIHJldHVybiAoc2V0KSA9PiB7XG4gICAgICAgIGNvbnN0IG51bWJlciA9IGdlbmVyYXRlVW5pcXVlTnVtYmVyKHNldCk7XG4gICAgICAgIHNldC5hZGQobnVtYmVyKTtcbiAgICAgICAgcmV0dXJuIG51bWJlcjtcbiAgICB9O1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFkZC11bmlxdWUtbnVtYmVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/add-unique-number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/cache.js":
/*!**************************************************************************!*\
  !*** ./node_modules/fast-unique-numbers/build/es2019/factories/cache.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCache: () => (/* binding */ createCache)\n/* harmony export */ });\nconst createCache = (lastNumberWeakMap) => {\n    return (collection, nextNumber) => {\n        lastNumberWeakMap.set(collection, nextNumber);\n        return nextNumber;\n    };\n};\n//# sourceMappingURL=cache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC11bmlxdWUtbnVtYmVycy9idWlsZC9lczIwMTkvZmFjdG9yaWVzL2NhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxoYWNrb25fdHRzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxmYXN0LXVuaXF1ZS1udW1iZXJzXFxidWlsZFxcZXMyMDE5XFxmYWN0b3JpZXNcXGNhY2hlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBjcmVhdGVDYWNoZSA9IChsYXN0TnVtYmVyV2Vha01hcCkgPT4ge1xuICAgIHJldHVybiAoY29sbGVjdGlvbiwgbmV4dE51bWJlcikgPT4ge1xuICAgICAgICBsYXN0TnVtYmVyV2Vha01hcC5zZXQoY29sbGVjdGlvbiwgbmV4dE51bWJlcik7XG4gICAgICAgIHJldHVybiBuZXh0TnVtYmVyO1xuICAgIH07XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2FjaGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/cache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGenerateUniqueNumber: () => (/* binding */ createGenerateUniqueNumber)\n/* harmony export */ });\n/*\n * The value of the constant Number.MAX_SAFE_INTEGER equals (2 ** 53 - 1) but it\n * is fairly new.\n */\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER === undefined ? 9007199254740991 : Number.MAX_SAFE_INTEGER;\nconst TWO_TO_THE_POWER_OF_TWENTY_NINE = 536870912;\nconst TWO_TO_THE_POWER_OF_THIRTY = TWO_TO_THE_POWER_OF_TWENTY_NINE * 2;\nconst createGenerateUniqueNumber = (cache, lastNumberWeakMap) => {\n    return (collection) => {\n        const lastNumber = lastNumberWeakMap.get(collection);\n        /*\n         * Let's try the cheapest algorithm first. It might fail to produce a new\n         * number, but it is so cheap that it is okay to take the risk. Just\n         * increase the last number by one or reset it to 0 if we reached the upper\n         * bound of SMIs (which stands for small integers). When the last number is\n         * unknown it is assumed that the collection contains zero based consecutive\n         * numbers.\n         */\n        let nextNumber = lastNumber === undefined ? collection.size : lastNumber < TWO_TO_THE_POWER_OF_THIRTY ? lastNumber + 1 : 0;\n        if (!collection.has(nextNumber)) {\n            return cache(collection, nextNumber);\n        }\n        /*\n         * If there are less than half of 2 ** 30 numbers stored in the collection,\n         * the chance to generate a new random number in the range from 0 to 2 ** 30\n         * is at least 50%. It's benifitial to use only SMIs because they perform\n         * much better in any environment based on V8.\n         */\n        if (collection.size < TWO_TO_THE_POWER_OF_TWENTY_NINE) {\n            while (collection.has(nextNumber)) {\n                nextNumber = Math.floor(Math.random() * TWO_TO_THE_POWER_OF_THIRTY);\n            }\n            return cache(collection, nextNumber);\n        }\n        // Quickly check if there is a theoretical chance to generate a new number.\n        if (collection.size > MAX_SAFE_INTEGER) {\n            throw new Error('Congratulations, you created a collection of unique numbers which uses all available integers!');\n        }\n        // Otherwise use the full scale of safely usable integers.\n        while (collection.has(nextNumber)) {\n            nextNumber = Math.floor(Math.random() * MAX_SAFE_INTEGER);\n        }\n        return cache(collection, nextNumber);\n    };\n};\n//# sourceMappingURL=generate-unique-number.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC11bmlxdWUtbnVtYmVycy9idWlsZC9lczIwMTkvZmFjdG9yaWVzL2dlbmVyYXRlLXVuaXF1ZS1udW1iZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGZhc3QtdW5pcXVlLW51bWJlcnNcXGJ1aWxkXFxlczIwMTlcXGZhY3Rvcmllc1xcZ2VuZXJhdGUtdW5pcXVlLW51bWJlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICogVGhlIHZhbHVlIG9mIHRoZSBjb25zdGFudCBOdW1iZXIuTUFYX1NBRkVfSU5URUdFUiBlcXVhbHMgKDIgKiogNTMgLSAxKSBidXQgaXRcbiAqIGlzIGZhaXJseSBuZXcuXG4gKi9cbmNvbnN0IE1BWF9TQUZFX0lOVEVHRVIgPSBOdW1iZXIuTUFYX1NBRkVfSU5URUdFUiA9PT0gdW5kZWZpbmVkID8gOTAwNzE5OTI1NDc0MDk5MSA6IE51bWJlci5NQVhfU0FGRV9JTlRFR0VSO1xuY29uc3QgVFdPX1RPX1RIRV9QT1dFUl9PRl9UV0VOVFlfTklORSA9IDUzNjg3MDkxMjtcbmNvbnN0IFRXT19UT19USEVfUE9XRVJfT0ZfVEhJUlRZID0gVFdPX1RPX1RIRV9QT1dFUl9PRl9UV0VOVFlfTklORSAqIDI7XG5leHBvcnQgY29uc3QgY3JlYXRlR2VuZXJhdGVVbmlxdWVOdW1iZXIgPSAoY2FjaGUsIGxhc3ROdW1iZXJXZWFrTWFwKSA9PiB7XG4gICAgcmV0dXJuIChjb2xsZWN0aW9uKSA9PiB7XG4gICAgICAgIGNvbnN0IGxhc3ROdW1iZXIgPSBsYXN0TnVtYmVyV2Vha01hcC5nZXQoY29sbGVjdGlvbik7XG4gICAgICAgIC8qXG4gICAgICAgICAqIExldCdzIHRyeSB0aGUgY2hlYXBlc3QgYWxnb3JpdGhtIGZpcnN0LiBJdCBtaWdodCBmYWlsIHRvIHByb2R1Y2UgYSBuZXdcbiAgICAgICAgICogbnVtYmVyLCBidXQgaXQgaXMgc28gY2hlYXAgdGhhdCBpdCBpcyBva2F5IHRvIHRha2UgdGhlIHJpc2suIEp1c3RcbiAgICAgICAgICogaW5jcmVhc2UgdGhlIGxhc3QgbnVtYmVyIGJ5IG9uZSBvciByZXNldCBpdCB0byAwIGlmIHdlIHJlYWNoZWQgdGhlIHVwcGVyXG4gICAgICAgICAqIGJvdW5kIG9mIFNNSXMgKHdoaWNoIHN0YW5kcyBmb3Igc21hbGwgaW50ZWdlcnMpLiBXaGVuIHRoZSBsYXN0IG51bWJlciBpc1xuICAgICAgICAgKiB1bmtub3duIGl0IGlzIGFzc3VtZWQgdGhhdCB0aGUgY29sbGVjdGlvbiBjb250YWlucyB6ZXJvIGJhc2VkIGNvbnNlY3V0aXZlXG4gICAgICAgICAqIG51bWJlcnMuXG4gICAgICAgICAqL1xuICAgICAgICBsZXQgbmV4dE51bWJlciA9IGxhc3ROdW1iZXIgPT09IHVuZGVmaW5lZCA/IGNvbGxlY3Rpb24uc2l6ZSA6IGxhc3ROdW1iZXIgPCBUV09fVE9fVEhFX1BPV0VSX09GX1RISVJUWSA/IGxhc3ROdW1iZXIgKyAxIDogMDtcbiAgICAgICAgaWYgKCFjb2xsZWN0aW9uLmhhcyhuZXh0TnVtYmVyKSkge1xuICAgICAgICAgICAgcmV0dXJuIGNhY2hlKGNvbGxlY3Rpb24sIG5leHROdW1iZXIpO1xuICAgICAgICB9XG4gICAgICAgIC8qXG4gICAgICAgICAqIElmIHRoZXJlIGFyZSBsZXNzIHRoYW4gaGFsZiBvZiAyICoqIDMwIG51bWJlcnMgc3RvcmVkIGluIHRoZSBjb2xsZWN0aW9uLFxuICAgICAgICAgKiB0aGUgY2hhbmNlIHRvIGdlbmVyYXRlIGEgbmV3IHJhbmRvbSBudW1iZXIgaW4gdGhlIHJhbmdlIGZyb20gMCB0byAyICoqIDMwXG4gICAgICAgICAqIGlzIGF0IGxlYXN0IDUwJS4gSXQncyBiZW5pZml0aWFsIHRvIHVzZSBvbmx5IFNNSXMgYmVjYXVzZSB0aGV5IHBlcmZvcm1cbiAgICAgICAgICogbXVjaCBiZXR0ZXIgaW4gYW55IGVudmlyb25tZW50IGJhc2VkIG9uIFY4LlxuICAgICAgICAgKi9cbiAgICAgICAgaWYgKGNvbGxlY3Rpb24uc2l6ZSA8IFRXT19UT19USEVfUE9XRVJfT0ZfVFdFTlRZX05JTkUpIHtcbiAgICAgICAgICAgIHdoaWxlIChjb2xsZWN0aW9uLmhhcyhuZXh0TnVtYmVyKSkge1xuICAgICAgICAgICAgICAgIG5leHROdW1iZXIgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBUV09fVE9fVEhFX1BPV0VSX09GX1RISVJUWSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gY2FjaGUoY29sbGVjdGlvbiwgbmV4dE51bWJlcik7XG4gICAgICAgIH1cbiAgICAgICAgLy8gUXVpY2tseSBjaGVjayBpZiB0aGVyZSBpcyBhIHRoZW9yZXRpY2FsIGNoYW5jZSB0byBnZW5lcmF0ZSBhIG5ldyBudW1iZXIuXG4gICAgICAgIGlmIChjb2xsZWN0aW9uLnNpemUgPiBNQVhfU0FGRV9JTlRFR0VSKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NvbmdyYXR1bGF0aW9ucywgeW91IGNyZWF0ZWQgYSBjb2xsZWN0aW9uIG9mIHVuaXF1ZSBudW1iZXJzIHdoaWNoIHVzZXMgYWxsIGF2YWlsYWJsZSBpbnRlZ2VycyEnKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBPdGhlcndpc2UgdXNlIHRoZSBmdWxsIHNjYWxlIG9mIHNhZmVseSB1c2FibGUgaW50ZWdlcnMuXG4gICAgICAgIHdoaWxlIChjb2xsZWN0aW9uLmhhcyhuZXh0TnVtYmVyKSkge1xuICAgICAgICAgICAgbmV4dE51bWJlciA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIE1BWF9TQUZFX0lOVEVHRVIpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBjYWNoZShjb2xsZWN0aW9uLCBuZXh0TnVtYmVyKTtcbiAgICB9O1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdlbmVyYXRlLXVuaXF1ZS1udW1iZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fast-unique-numbers/build/es2019/module.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fast-unique-numbers/build/es2019/module.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addUniqueNumber: () => (/* binding */ addUniqueNumber),\n/* harmony export */   generateUniqueNumber: () => (/* binding */ generateUniqueNumber)\n/* harmony export */ });\n/* harmony import */ var _factories_add_unique_number__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./factories/add-unique-number */ \"(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/add-unique-number.js\");\n/* harmony import */ var _factories_cache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./factories/cache */ \"(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/cache.js\");\n/* harmony import */ var _factories_generate_unique_number__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./factories/generate-unique-number */ \"(ssr)/./node_modules/fast-unique-numbers/build/es2019/factories/generate-unique-number.js\");\n\n\n\nconst LAST_NUMBER_WEAK_MAP = new WeakMap();\nconst cache = (0,_factories_cache__WEBPACK_IMPORTED_MODULE_1__.createCache)(LAST_NUMBER_WEAK_MAP);\nconst generateUniqueNumber = (0,_factories_generate_unique_number__WEBPACK_IMPORTED_MODULE_2__.createGenerateUniqueNumber)(cache, LAST_NUMBER_WEAK_MAP);\nconst addUniqueNumber = (0,_factories_add_unique_number__WEBPACK_IMPORTED_MODULE_0__.createAddUniqueNumber)(generateUniqueNumber);\n\n//# sourceMappingURL=module.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFzdC11bmlxdWUtbnVtYmVycy9idWlsZC9lczIwMTkvbW9kdWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXNFO0FBQ3RCO0FBQ2dDO0FBQ2hGO0FBQ0EsY0FBYyw2REFBVztBQUN6Qiw2QkFBNkIsNkZBQTBCO0FBQ3ZELHdCQUF3QixtRkFBcUI7QUFDSTtBQUNqRCIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxoYWNrb25fdHRzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxmYXN0LXVuaXF1ZS1udW1iZXJzXFxidWlsZFxcZXMyMDE5XFxtb2R1bGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQWRkVW5pcXVlTnVtYmVyIH0gZnJvbSAnLi9mYWN0b3JpZXMvYWRkLXVuaXF1ZS1udW1iZXInO1xuaW1wb3J0IHsgY3JlYXRlQ2FjaGUgfSBmcm9tICcuL2ZhY3Rvcmllcy9jYWNoZSc7XG5pbXBvcnQgeyBjcmVhdGVHZW5lcmF0ZVVuaXF1ZU51bWJlciB9IGZyb20gJy4vZmFjdG9yaWVzL2dlbmVyYXRlLXVuaXF1ZS1udW1iZXInO1xuY29uc3QgTEFTVF9OVU1CRVJfV0VBS19NQVAgPSBuZXcgV2Vha01hcCgpO1xuY29uc3QgY2FjaGUgPSBjcmVhdGVDYWNoZShMQVNUX05VTUJFUl9XRUFLX01BUCk7XG5jb25zdCBnZW5lcmF0ZVVuaXF1ZU51bWJlciA9IGNyZWF0ZUdlbmVyYXRlVW5pcXVlTnVtYmVyKGNhY2hlLCBMQVNUX05VTUJFUl9XRUFLX01BUCk7XG5jb25zdCBhZGRVbmlxdWVOdW1iZXIgPSBjcmVhdGVBZGRVbmlxdWVOdW1iZXIoZ2VuZXJhdGVVbmlxdWVOdW1iZXIpO1xuZXhwb3J0IHsgYWRkVW5pcXVlTnVtYmVyLCBnZW5lcmF0ZVVuaXF1ZU51bWJlciB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bW9kdWxlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-unique-numbers/build/es2019/module.js\n");

/***/ })

};
;
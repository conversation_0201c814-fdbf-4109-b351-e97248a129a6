import { IReadOnlyMap } from './interfaces';
export declare class ReadOnlyMap<T, U> implements IReadOnlyMap<T, U> {
    private _map;
    constructor(parameters: [T, U][]);
    get size(): number;
    entries(): IterableIterator<[T, U]>;
    forEach(callback: (audioParam: U, name: T, map: ReadOnlyMap<T, U>) => void, thisArg?: any): void;
    get(name: T): undefined | U;
    has(name: T): boolean;
    keys(): IterableIterator<T>;
    values(): IterableIterator<U>;
}
//# sourceMappingURL=read-only-map.d.ts.map
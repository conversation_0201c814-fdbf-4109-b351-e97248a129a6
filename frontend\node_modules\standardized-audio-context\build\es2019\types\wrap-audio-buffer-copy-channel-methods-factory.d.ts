import { TConvertNumberToUnsignedLongFunction } from './convert-number-to-unsigned-long-function';
import { TIndexSizeErrorFactory } from './index-size-error-factory';
import { TWrapAudioBufferCopyChannelMethodsFunction } from './wrap-audio-buffer-copy-channel-methods-function';
export type TWrapAudioBufferCopyChannelMethodsFactory = (convertNumberToUnsignedLong: TConvertNumberToUnsignedLongFunction, createIndexSizeError: TIndexSizeErrorFactory) => TWrapAudioBufferCopyChannelMethodsFunction;
//# sourceMappingURL=wrap-audio-buffer-copy-channel-methods-factory.d.ts.map
import { TConvertNumberToUnsignedLongFunction } from './convert-number-to-unsigned-long-function';
import { TWrapAudioBufferCopyChannelMethodsOutOfBoundsFunction } from './wrap-audio-buffer-copy-channel-methods-out-of-bounds-function';
export type TWrapAudioBufferCopyChannelMethodsOutOfBoundsFactory = (convertNumberToUnsignedLong: TConvertNumberToUnsignedLongFunction) => TWrapAudioBufferCopyChannelMethodsOutOfBoundsFunction;
//# sourceMappingURL=wrap-audio-buffer-copy-channel-methods-out-of-bounds-factory.d.ts.map
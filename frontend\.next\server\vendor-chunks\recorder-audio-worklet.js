"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/recorder-audio-worklet";
exports.ids = ["vendor-chunks/recorder-audio-worklet"];
exports.modules = {

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/add-recorder-audio-worklet-module.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/factories/add-recorder-audio-worklet-module.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAddRecorderAudioWorkletModule: () => (/* binding */ createAddRecorderAudioWorkletModule)\n/* harmony export */ });\nconst createAddRecorderAudioWorkletModule = (blobConstructor, urlConstructor, worklet) => {\n    return async (addAudioWorkletModule) => {\n        const blob = new blobConstructor([worklet], { type: 'application/javascript; charset=utf-8' });\n        const url = urlConstructor.createObjectURL(blob);\n        try {\n            await addAudioWorkletModule(url);\n        }\n        finally {\n            urlConstructor.revokeObjectURL(url);\n        }\n    };\n};\n//# sourceMappingURL=add-recorder-audio-worklet-module.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvZmFjdG9yaWVzL2FkZC1yZWNvcmRlci1hdWRpby13b3JrbGV0LW1vZHVsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBLHNEQUFzRCwrQkFBK0IsZ0JBQWdCO0FBQ3JHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlY29yZGVyLWF1ZGlvLXdvcmtsZXRcXGJ1aWxkXFxlczIwMTlcXGZhY3Rvcmllc1xcYWRkLXJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbW9kdWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBjcmVhdGVBZGRSZWNvcmRlckF1ZGlvV29ya2xldE1vZHVsZSA9IChibG9iQ29uc3RydWN0b3IsIHVybENvbnN0cnVjdG9yLCB3b3JrbGV0KSA9PiB7XG4gICAgcmV0dXJuIGFzeW5jIChhZGRBdWRpb1dvcmtsZXRNb2R1bGUpID0+IHtcbiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBibG9iQ29uc3RydWN0b3IoW3dvcmtsZXRdLCB7IHR5cGU6ICdhcHBsaWNhdGlvbi9qYXZhc2NyaXB0OyBjaGFyc2V0PXV0Zi04JyB9KTtcbiAgICAgICAgY29uc3QgdXJsID0gdXJsQ29uc3RydWN0b3IuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgYXdhaXQgYWRkQXVkaW9Xb3JrbGV0TW9kdWxlKHVybCk7XG4gICAgICAgIH1cbiAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICB1cmxDb25zdHJ1Y3Rvci5yZXZva2VPYmplY3RVUkwodXJsKTtcbiAgICAgICAgfVxuICAgIH07XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWRkLXJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbW9kdWxlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/add-recorder-audio-worklet-module.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/listener.js":
/*!********************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/factories/listener.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createListener: () => (/* binding */ createListener)\n/* harmony export */ });\nconst createListener = (ongoingRequests) => {\n    return ({ data: message }) => {\n        const { id } = message;\n        if (id !== null) {\n            const ongoingRequest = ongoingRequests.get(id);\n            if (ongoingRequest !== undefined) {\n                const { reject, resolve } = ongoingRequest;\n                ongoingRequests.delete(id);\n                if (message.error === undefined) {\n                    resolve(message.result);\n                }\n                else {\n                    reject(new Error(message.error.message));\n                }\n            }\n        }\n    };\n};\n//# sourceMappingURL=listener.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvZmFjdG9yaWVzL2xpc3RlbmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQLGNBQWMsZUFBZTtBQUM3QixnQkFBZ0IsS0FBSztBQUNyQjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isa0JBQWtCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxoYWNrb25fdHRzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWNvcmRlci1hdWRpby13b3JrbGV0XFxidWlsZFxcZXMyMDE5XFxmYWN0b3JpZXNcXGxpc3RlbmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBjcmVhdGVMaXN0ZW5lciA9IChvbmdvaW5nUmVxdWVzdHMpID0+IHtcbiAgICByZXR1cm4gKHsgZGF0YTogbWVzc2FnZSB9KSA9PiB7XG4gICAgICAgIGNvbnN0IHsgaWQgfSA9IG1lc3NhZ2U7XG4gICAgICAgIGlmIChpZCAhPT0gbnVsbCkge1xuICAgICAgICAgICAgY29uc3Qgb25nb2luZ1JlcXVlc3QgPSBvbmdvaW5nUmVxdWVzdHMuZ2V0KGlkKTtcbiAgICAgICAgICAgIGlmIChvbmdvaW5nUmVxdWVzdCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyByZWplY3QsIHJlc29sdmUgfSA9IG9uZ29pbmdSZXF1ZXN0O1xuICAgICAgICAgICAgICAgIG9uZ29pbmdSZXF1ZXN0cy5kZWxldGUoaWQpO1xuICAgICAgICAgICAgICAgIGlmIChtZXNzYWdlLmVycm9yID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzb2x2ZShtZXNzYWdlLnJlc3VsdCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZWplY3QobmV3IEVycm9yKG1lc3NhZ2UuZXJyb3IubWVzc2FnZSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH07XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bGlzdGVuZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/listener.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/post-message-factory.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/factories/post-message-factory.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPostMessageFactory: () => (/* binding */ createPostMessageFactory)\n/* harmony export */ });\nconst createPostMessageFactory = (generateUniqueNumber) => {\n    return (ongoingRequests, port) => {\n        return (message, transferables = []) => {\n            return new Promise((resolve, reject) => {\n                const id = generateUniqueNumber(ongoingRequests);\n                ongoingRequests.set(id, { reject, resolve });\n                port.postMessage({ id, ...message }, transferables);\n            });\n        };\n    };\n};\n//# sourceMappingURL=post-message-factory.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvZmFjdG9yaWVzL3Bvc3QtbWVzc2FnZS1mYWN0b3J5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLGlCQUFpQjtBQUMzRCxtQ0FBbUMsZ0JBQWdCO0FBQ25ELGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxoYWNrb25fdHRzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWNvcmRlci1hdWRpby13b3JrbGV0XFxidWlsZFxcZXMyMDE5XFxmYWN0b3JpZXNcXHBvc3QtbWVzc2FnZS1mYWN0b3J5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBjcmVhdGVQb3N0TWVzc2FnZUZhY3RvcnkgPSAoZ2VuZXJhdGVVbmlxdWVOdW1iZXIpID0+IHtcbiAgICByZXR1cm4gKG9uZ29pbmdSZXF1ZXN0cywgcG9ydCkgPT4ge1xuICAgICAgICByZXR1cm4gKG1lc3NhZ2UsIHRyYW5zZmVyYWJsZXMgPSBbXSkgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBpZCA9IGdlbmVyYXRlVW5pcXVlTnVtYmVyKG9uZ29pbmdSZXF1ZXN0cyk7XG4gICAgICAgICAgICAgICAgb25nb2luZ1JlcXVlc3RzLnNldChpZCwgeyByZWplY3QsIHJlc29sdmUgfSk7XG4gICAgICAgICAgICAgICAgcG9ydC5wb3N0TWVzc2FnZSh7IGlkLCAuLi5tZXNzYWdlIH0sIHRyYW5zZmVyYWJsZXMpO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgfTtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wb3N0LW1lc3NhZ2UtZmFjdG9yeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/post-message-factory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/recorder-audio-worklet-node-factory.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/factories/recorder-audio-worklet-node-factory.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRecorderAudioWorkletNodeFactory: () => (/* binding */ createRecorderAudioWorkletNodeFactory)\n/* harmony export */ });\nconst createRecorderAudioWorkletNodeFactory = (createListener, createPostMessage, on, validateState) => {\n    return (audioWorkletNodeConstructor, context, options = {}) => {\n        const audioWorkletNode = new audioWorkletNodeConstructor(context, 'recorder-audio-worklet-processor', {\n            ...options,\n            channelCountMode: 'explicit',\n            numberOfInputs: 1,\n            numberOfOutputs: 0\n        });\n        const ongoingRequests = new Map();\n        const postMessage = createPostMessage(ongoingRequests, audioWorkletNode.port);\n        const unsubscribe = on(audioWorkletNode.port, 'message')(createListener(ongoingRequests));\n        audioWorkletNode.port.start();\n        let state = 'inactive';\n        Object.defineProperties(audioWorkletNode, {\n            pause: {\n                get() {\n                    return async () => {\n                        validateState(['recording'], state);\n                        state = 'paused';\n                        return postMessage({\n                            method: 'pause'\n                        });\n                    };\n                }\n            },\n            port: {\n                get() {\n                    throw new Error(\"The port of a RecorderAudioWorkletNode can't be accessed.\");\n                }\n            },\n            record: {\n                get() {\n                    return async (encoderPort) => {\n                        validateState(['inactive'], state);\n                        state = 'recording';\n                        return postMessage({\n                            method: 'record',\n                            params: { encoderPort }\n                        }, [encoderPort]);\n                    };\n                }\n            },\n            resume: {\n                get() {\n                    return async () => {\n                        validateState(['paused'], state);\n                        state = 'recording';\n                        return postMessage({\n                            method: 'resume'\n                        });\n                    };\n                }\n            },\n            stop: {\n                get() {\n                    return async () => {\n                        validateState(['paused', 'recording'], state);\n                        state = 'stopped';\n                        try {\n                            await postMessage({ method: 'stop' });\n                        }\n                        finally {\n                            unsubscribe();\n                        }\n                    };\n                }\n            }\n        });\n        return audioWorkletNode;\n    };\n};\n//# sourceMappingURL=recorder-audio-worklet-node-factory.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/recorder-audio-worklet-node-factory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/functions/validate-state.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/functions/validate-state.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateState: () => (/* binding */ validateState)\n/* harmony export */ });\nconst validateState = (expectedStates, currentState) => {\n    if (!expectedStates.includes(currentState)) {\n        throw new Error(`Expected the state to be ${expectedStates\n            .map((expectedState) => `\"${expectedState}\"`)\n            .join(' or ')} but it was \"${currentState}\".`);\n    }\n};\n//# sourceMappingURL=validate-state.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvZnVuY3Rpb25zL3ZhbGlkYXRlLXN0YXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0Esb0RBQW9EO0FBQ3BELHdDQUF3QyxjQUFjO0FBQ3RELDJCQUEyQixjQUFjLGFBQWE7QUFDdEQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlY29yZGVyLWF1ZGlvLXdvcmtsZXRcXGJ1aWxkXFxlczIwMTlcXGZ1bmN0aW9uc1xcdmFsaWRhdGUtc3RhdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZhbGlkYXRlU3RhdGUgPSAoZXhwZWN0ZWRTdGF0ZXMsIGN1cnJlbnRTdGF0ZSkgPT4ge1xuICAgIGlmICghZXhwZWN0ZWRTdGF0ZXMuaW5jbHVkZXMoY3VycmVudFN0YXRlKSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEV4cGVjdGVkIHRoZSBzdGF0ZSB0byBiZSAke2V4cGVjdGVkU3RhdGVzXG4gICAgICAgICAgICAubWFwKChleHBlY3RlZFN0YXRlKSA9PiBgXCIke2V4cGVjdGVkU3RhdGV9XCJgKVxuICAgICAgICAgICAgLmpvaW4oJyBvciAnKX0gYnV0IGl0IHdhcyBcIiR7Y3VycmVudFN0YXRlfVwiLmApO1xuICAgIH1cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD12YWxpZGF0ZS1zdGF0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/functions/validate-state.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/interfaces/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/interfaces/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _native_recorder_audio_worklet_node__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native-recorder-audio-worklet-node */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/interfaces/native-recorder-audio-worklet-node.js\");\n/* harmony import */ var _recorder_audio_worklet_node__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./recorder-audio-worklet-node */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/interfaces/recorder-audio-worklet-node.js\");\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvaW50ZXJmYWNlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7QUFBcUQ7QUFDUDtBQUM5QyIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxoYWNrb25fdHRzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxyZWNvcmRlci1hdWRpby13b3JrbGV0XFxidWlsZFxcZXMyMDE5XFxpbnRlcmZhY2VzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL25hdGl2ZS1yZWNvcmRlci1hdWRpby13b3JrbGV0LW5vZGUnO1xuZXhwb3J0ICogZnJvbSAnLi9yZWNvcmRlci1hdWRpby13b3JrbGV0LW5vZGUnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/interfaces/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/interfaces/native-recorder-audio-worklet-node.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/interfaces/native-recorder-audio-worklet-node.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=native-recorder-audio-worklet-node.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvaW50ZXJmYWNlcy9uYXRpdmUtcmVjb3JkZXItYXVkaW8td29ya2xldC1ub2RlLmpzIiwibWFwcGluZ3MiOiI7QUFBVTtBQUNWIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlY29yZGVyLWF1ZGlvLXdvcmtsZXRcXGJ1aWxkXFxlczIwMTlcXGludGVyZmFjZXNcXG5hdGl2ZS1yZWNvcmRlci1hdWRpby13b3JrbGV0LW5vZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF0aXZlLXJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbm9kZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/interfaces/native-recorder-audio-worklet-node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/interfaces/recorder-audio-worklet-node.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/interfaces/recorder-audio-worklet-node.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=recorder-audio-worklet-node.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvaW50ZXJmYWNlcy9yZWNvcmRlci1hdWRpby13b3JrbGV0LW5vZGUuanMiLCJtYXBwaW5ncyI6IjtBQUFVO0FBQ1YiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcaGFja29uX3R0c1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVjb3JkZXItYXVkaW8td29ya2xldFxcYnVpbGRcXGVzMjAxOVxcaW50ZXJmYWNlc1xccmVjb3JkZXItYXVkaW8td29ya2xldC1ub2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbm9kZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/interfaces/recorder-audio-worklet-node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/module.js":
/*!********************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/module.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addRecorderAudioWorkletModule: () => (/* binding */ addRecorderAudioWorkletModule),\n/* harmony export */   createRecorderAudioWorkletNode: () => (/* binding */ createRecorderAudioWorkletNode),\n/* harmony export */   isSupported: () => (/* reexport safe */ worker_factory__WEBPACK_IMPORTED_MODULE_2__.isSupported)\n/* harmony export */ });\n/* harmony import */ var fast_unique_numbers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fast-unique-numbers */ \"(ssr)/./node_modules/fast-unique-numbers/build/es2019/module.js\");\n/* harmony import */ var subscribable_things__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! subscribable-things */ \"(ssr)/./node_modules/subscribable-things/build/es2019/module.js\");\n/* harmony import */ var worker_factory__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! worker-factory */ \"(ssr)/./node_modules/worker-factory/build/es2019/module.js\");\n/* harmony import */ var _factories_add_recorder_audio_worklet_module__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./factories/add-recorder-audio-worklet-module */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/add-recorder-audio-worklet-module.js\");\n/* harmony import */ var _factories_listener__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./factories/listener */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/listener.js\");\n/* harmony import */ var _factories_post_message_factory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./factories/post-message-factory */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/post-message-factory.js\");\n/* harmony import */ var _factories_recorder_audio_worklet_node_factory__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./factories/recorder-audio-worklet-node-factory */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/factories/recorder-audio-worklet-node-factory.js\");\n/* harmony import */ var _functions_validate_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./functions/validate-state */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/functions/validate-state.js\");\n/* harmony import */ var _worklet_worklet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./worklet/worklet */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/worklet/worklet.js\");\n/* harmony import */ var _interfaces_index__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./interfaces/index */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/interfaces/index.js\");\n/* harmony import */ var _types_index__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./types/index */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/index.js\");\n\n\n\n\n\n\n\n\n\n/*\n * @todo Explicitly referencing the barrel file seems to be necessary when enabling the\n * isolatedModules compiler option.\n */\n\n\nconst addRecorderAudioWorkletModule = (0,_factories_add_recorder_audio_worklet_module__WEBPACK_IMPORTED_MODULE_3__.createAddRecorderAudioWorkletModule)(Blob, URL, _worklet_worklet__WEBPACK_IMPORTED_MODULE_8__.worklet);\nconst createRecorderAudioWorkletNode = (0,_factories_recorder_audio_worklet_node_factory__WEBPACK_IMPORTED_MODULE_6__.createRecorderAudioWorkletNodeFactory)(_factories_listener__WEBPACK_IMPORTED_MODULE_4__.createListener, (0,_factories_post_message_factory__WEBPACK_IMPORTED_MODULE_5__.createPostMessageFactory)(fast_unique_numbers__WEBPACK_IMPORTED_MODULE_0__.generateUniqueNumber), subscribable_things__WEBPACK_IMPORTED_MODULE_1__.on, _functions_validate_state__WEBPACK_IMPORTED_MODULE_7__.validateState);\n\n//# sourceMappingURL=module.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/module.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/any-recorder-audio-worklet-node-options.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/types/any-recorder-audio-worklet-node-options.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=any-recorder-audio-worklet-node-options.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvdHlwZXMvYW55LXJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbm9kZS1vcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7QUFBVTtBQUNWIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlY29yZGVyLWF1ZGlvLXdvcmtsZXRcXGJ1aWxkXFxlczIwMTlcXHR5cGVzXFxhbnktcmVjb3JkZXItYXVkaW8td29ya2xldC1ub2RlLW9wdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YW55LXJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbm9kZS1vcHRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/any-recorder-audio-worklet-node-options.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/types/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _any_recorder_audio_worklet_node_options__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./any-recorder-audio-worklet-node-options */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/any-recorder-audio-worklet-node-options.js\");\n/* harmony import */ var _native_recorder_audio_worklet_node_options__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./native-recorder-audio-worklet-node-options */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/native-recorder-audio-worklet-node-options.js\");\n/* harmony import */ var _recorder_audio_worklet_node_options__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./recorder-audio-worklet-node-options */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/recorder-audio-worklet-node-options.js\");\n/* harmony import */ var _state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./state */ \"(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/state.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvdHlwZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEQ7QUFDRztBQUNQO0FBQzlCO0FBQ3hCIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlY29yZGVyLWF1ZGlvLXdvcmtsZXRcXGJ1aWxkXFxlczIwMTlcXHR5cGVzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL2FueS1yZWNvcmRlci1hdWRpby13b3JrbGV0LW5vZGUtb3B0aW9ucyc7XG5leHBvcnQgKiBmcm9tICcuL25hdGl2ZS1yZWNvcmRlci1hdWRpby13b3JrbGV0LW5vZGUtb3B0aW9ucyc7XG5leHBvcnQgKiBmcm9tICcuL3JlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbm9kZS1vcHRpb25zJztcbmV4cG9ydCAqIGZyb20gJy4vc3RhdGUnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/native-recorder-audio-worklet-node-options.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/types/native-recorder-audio-worklet-node-options.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=native-recorder-audio-worklet-node-options.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvdHlwZXMvbmF0aXZlLXJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbm9kZS1vcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7QUFBVTtBQUNWIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHJlY29yZGVyLWF1ZGlvLXdvcmtsZXRcXGJ1aWxkXFxlczIwMTlcXHR5cGVzXFxuYXRpdmUtcmVjb3JkZXItYXVkaW8td29ya2xldC1ub2RlLW9wdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF0aXZlLXJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbm9kZS1vcHRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/native-recorder-audio-worklet-node-options.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/recorder-audio-worklet-node-options.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/types/recorder-audio-worklet-node-options.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=recorder-audio-worklet-node-options.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvdHlwZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC1ub2RlLW9wdGlvbnMuanMiLCJtYXBwaW5ncyI6IjtBQUFVO0FBQ1YiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcaGFja29uX3R0c1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVjb3JkZXItYXVkaW8td29ya2xldFxcYnVpbGRcXGVzMjAxOVxcdHlwZXNcXHJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbm9kZS1vcHRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlY29yZGVyLWF1ZGlvLXdvcmtsZXQtbm9kZS1vcHRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/recorder-audio-worklet-node-options.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/state.js":
/*!*************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/types/state.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=state.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVjb3JkZXItYXVkaW8td29ya2xldC9idWlsZC9lczIwMTkvdHlwZXMvc3RhdGUuanMiLCJtYXBwaW5ncyI6IjtBQUFVO0FBQ1YiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcaGFja29uX3R0c1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xccmVjb3JkZXItYXVkaW8td29ya2xldFxcYnVpbGRcXGVzMjAxOVxcdHlwZXNcXHN0YXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN0YXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/types/state.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/recorder-audio-worklet/build/es2019/worklet/worklet.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/recorder-audio-worklet/build/es2019/worklet/worklet.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   worklet: () => (/* binding */ worklet)\n/* harmony export */ });\n// This is the minified and stringified code of the recorder-audio-worklet-processor package.\nconst worklet = `(()=>{\"use strict\";class e extends AudioWorkletProcessor{constructor(){super(),this._encoderPort=null,this._numberOfChannels=0,this._state=\"inactive\",this.port.onmessage=({data:e})=>{\"pause\"===e.method?\"active\"===this._state||\"recording\"===this._state?(this._state=\"paused\",this._sendAcknowledgement(e.id)):this._sendUnexpectedStateError(e.id):\"record\"===e.method?\"inactive\"===this._state?(this._encoderPort=e.params.encoderPort,this._state=\"active\",this._sendAcknowledgement(e.id)):this._sendUnexpectedStateError(e.id):\"resume\"===e.method?\"paused\"===this._state?(this._state=\"active\",this._sendAcknowledgement(e.id)):this._sendUnexpectedStateError(e.id):\"stop\"===e.method?\"active\"!==this._state&&\"paused\"!==this._state&&\"recording\"!==this._state||null===this._encoderPort?this._sendUnexpectedStateError(e.id):(this._stop(this._encoderPort),this._sendAcknowledgement(e.id)):\"number\"==typeof e.id&&this.port.postMessage({error:{code:-32601,message:\"The requested method is not supported.\"},id:e.id})}}process([e]){if(\"inactive\"===this._state||\"paused\"===this._state)return!0;if(\"active\"===this._state){if(void 0===e)throw new Error(\"No channelData was received for the first input.\");if(0===e.length)return!0;this._state=\"recording\"}if(\"recording\"===this._state&&null!==this._encoderPort){if(void 0===e)throw new Error(\"No channelData was received for the first input.\");return 0===e.length?this._encoderPort.postMessage(Array.from({length:this._numberOfChannels},(()=>128))):(this._encoderPort.postMessage(e,e.map((({buffer:e})=>e))),this._numberOfChannels=e.length),!0}return!1}_sendAcknowledgement(e){this.port.postMessage({id:e,result:null})}_sendUnexpectedStateError(e){this.port.postMessage({error:{code:-32603,message:\"The internal state does not allow to process the given message.\"},id:e})}_stop(e){e.postMessage([]),e.close(),this._encoderPort=null,this._state=\"stopped\"}}e.parameterDescriptors=[],registerProcessor(\"recorder-audio-worklet-processor\",e)})();`; // tslint:disable-line:max-line-length\n//# sourceMappingURL=worklet.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/recorder-audio-worklet/build/es2019/worklet/worklet.js\n");

/***/ })

};
;
{"version": 3, "file": "maths.d.ts", "sourceRoot": "", "sources": ["../../src/utils/maths.js"], "names": [], "mappings": "AACA;;;;;;;GAOG;AAEH;;;;GAIG;AAEH;;GAEG;AACH,wCAFW,UAAU,yIAqEpB;AAGD;;;;;;;GAOG;AACH,6BAN6B,CAAC,SAAhB,aAAc,SACjB,CAAC,QACD,MAAM,EAAE,QACR,MAAM,EAAE,GACN,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAgCzB;AAGD;;;;;GAKG;AACH,wBAJmC,CAAC,SAAtB,UAAU,GAAC,MAAM,EAAG,OACvB,CAAC,GACC,CAAC,CAiBb;AAED;;;;;GAKG;AACH,4BAJmC,CAAC,SAAtB,UAAU,GAAC,MAAM,EAAG,OACvB,CAAC,GACC,CAAC,CAmBb;AAED;;;;;GAKG;AACH,0BAJW,MAAM,EAAE,QACR,MAAM,EAAE,GACN,MAAM,CAQlB;AAED;;;;;;GAMG;AACH,8BAJW,MAAM,EAAE,QACR,MAAM,EAAE,GACN,MAAM,CAgBlB;AAED;;;;GAIG;AACH,+BAHW,MAAM,EAAE,GACN,MAAM,CAIlB;AAGD;;;;;;GAMG;AACH,oBAL+C,CAAC,SAAlC,MAAM,EAAE,GAAC,MAAM,EAAE,GAAC,aAAc,OACnC,CAAC,GACC,CAAC,SAAS,MAAM,EAAE,GAAC,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAclF;AAGD;;;;;;GAMG;AACH,oBAL+C,CAAC,SAAlC,MAAM,EAAE,GAAC,MAAM,EAAE,GAAC,aAAc,OACnC,CAAC,GACC,CAAC,SAAS,MAAM,EAAE,GAAC,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAclF;AAuoBD;;;;GAIG;AACH,mCAHW,aAAa,cACb,MAAM,OAmChB;AAED;;;;;GAKG;AACH,2BAJW,MAAM,YACN,MAAM,GACJ,MAAM,CAKlB;AAED;;;;;;;GAOG;AACH,iCAHW,MAAM,GACJ,MAAM,CAMlB;AAGD;;;;;GAKG;AACH,6CAHW,MAAM,EAAE,EAAE,GACR,MAAM,EAAE,EAAE,CA+EtB;AA5KD;IACI,6BAUC;IATG,gBAA4B;IAC5B,sBAA4C;IAExC,oBAAgC;IAChC,yBAAsC;IAO9C,0CAEC;IAED,sCAEC;CACJ;yBAj4BY,SAAS,GAAG,UAAU,GAAG,iBAAiB,GAAG,UAAU,GAAG,WAAW,GAAG,UAAU,GAAG,WAAW,GAAG,YAAY,GAAG,YAAY,GAAG,YAAY;4BAC7I,aAAa,GAAG,cAAc;4BAC9B,UAAU,GAAG,aAAa;AAkQvC;;;;;;GAMG;AACH;IACI;;;OAGG;IACH,kBAHW,MAAM,EAoChB;IAhCG,aAAoB;IAIpB,eAAuB;IAEvB,iCAA4C;IAe5C,eAAiD;IAGjD,iCAA+C;IAUnD;;;;OAIG;IACH,sBAFa,YAAY,CAIxB;IAED;;;;;;OAMG;IACH,0BAJW,YAAY,YACZ,MAAM,EAAE,GACN,MAAM,EAAE,CAOpB;IAED;;;;;OAKG;IACH,sBAJW,YAAY,YACZ,YAAY,GACV,YAAY,CASxB;IAED;;;;;;;;;OASG;IACH,eAPW,YAAY,QACZ,YAAY,GAIV,IAAI,CAOhB;IAED;;;;;;;;;OASG;IACH,mBALW,YAAY,QACZ,YAAY,QAStB;IAED;;;;;;;;;OASG;IACH,sBALW,YAAY,QACZ,YAAY,GAEV,IAAI,CAShB;IAED;;;;;;;OAOG;IACH,iBALW,YAAY,QACZ,YAAY,OACZ,MAAM,GACJ,IAAI,CA2FhB;IAED;;;;;;;;;OASG;IACH,wBAPW,YAAY,OACZ,YAAY,UACZ,MAAM,OACN,MAAM,QACN,MAAM,GACJ,IAAI,CAehB;IAED;;;;;;;;;;;OAWG;IACH,wBATW,YAAY,OACZ,YAAY,UACZ,MAAM,OACN,MAAM,QACN,MAAM,OACN,MAAM,GAEJ,IAAI,CAqChB;IAED;;;;;OAKG;IACH,qBAJW,YAAY,QACZ,YAAY,OACZ,MAAM,QAoHhB;IAED;;;;;;;;;;OAUG;IACH,4BARW,YAAY,OACZ,YAAY,UACZ,MAAM,OACN,MAAM,QACN,MAAM,GAEJ,IAAI,CAahB;IAED;;;;;;;;;;OAUG;IACH,4BAPW,YAAY,OACZ,YAAY,UACZ,MAAM,OACN,MAAM,QACN,MAAM,OACN,MAAM,QA6BhB;CACJ;AAED;;;;;GAKG;AACH;IAEI;;;OAGG;IACH,wBAFW,MAAM,EAkDhB;IA3CG,mBAAwB;IACxB,WAAW;IAMX,wCAA4C;IAC5C,oCAAwC;IACxC,oCAAwC;IACxC,uCAA2C;IAC3C,uCAA2C;IA0B3C,8CAA8C;IAI9C,UAAgC;IAIpC,qDA8CC;IAED,yCAEC;IAED,6CAEC;CACJ"}
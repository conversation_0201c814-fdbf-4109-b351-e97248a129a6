import { TTestAudioContextDecodeAudioDataMethodTypeErrorSupportFactory } from '../types';
/**
 * Edge up to version 14, Firefox up to version 52, Safari up to version 9 and maybe other browsers
 * did not refuse to decode invalid parameters with a TypeError.
 */
export declare const createTestAudioContextDecodeAudioDataMethodTypeErrorSupport: TTestAudioContextDecodeAudioDataMethodTypeErrorSupportFactory;
//# sourceMappingURL=test-audio-context-decode-audio-data-method-type-error-support.d.ts.map
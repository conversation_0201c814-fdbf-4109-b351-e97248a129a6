import { TGetAudioNodeRendererFunction } from './get-audio-node-renderer-function';
import { TGetAudioParamConnectionsFunction } from './get-audio-param-connections-function';
import { TIsPartOfACycleFunction } from './is-part-of-a-cycle-function';
import { TRenderInputsOfAudioParamFunction } from './render-inputs-of-audio-param-function';
export type TRenderInputsOfAudioParamFactory = (getAudioNodeRenderer: TGetAudioNodeRendererFunction, getAudioParamConnections: TGetAudioParamConnectionsFunction, isPartOfACycle: TIsPartOfACycleFunction) => TRenderInputsOfAudioParamFunction;
//# sourceMappingURL=render-inputs-of-audio-param-factory.d.ts.map
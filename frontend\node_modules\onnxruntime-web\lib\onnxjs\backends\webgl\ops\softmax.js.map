{"version": 3, "file": "softmax.js", "sourceRoot": "", "sources": ["softmax.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAuG;AAIvG,wCAA0C;AAC1C,gDAAyC;AAEzC,oCAAoD;AAEpD,2CAA6D;AAM7D,MAAM,gCAAgC,GAAG;IACvC,IAAI,EAAE,mBAAmB;IACzB,UAAU,EAAE,CAAC,GAAG,CAAC;IACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;CACnC,CAAC;AAEF,MAAM,kCAAkC,GAAG;IACzC,IAAI,EAAE,qBAAqB;IAC3B,UAAU,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;IACxB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;CACzD,CAAC;AAEF,MAAM,sBAAsB,GAAG;IAC7B,IAAI,EAAE,SAAS;IACf,UAAU,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC;IAChC,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,EAAE,mBAAW,CAAC,QAAQ,CAAC;CAC/E,CAAC;AAEK,MAAM,OAAO,GAA8C,CAChE,gBAAuC,EACvC,MAAgB,EAChB,UAA6B,EACnB,EAAE;IACZ,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC1C,MAAM,IAAI,GAAG,gBAAS,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;IACzE,MAAM,eAAe,GAAG,gBAAS,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACpE,MAAM,YAAY,GAAG,gBAAS,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAEnE,MAAM,MAAM,GAAG,cAAc,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;IACnG,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAdW,QAAA,OAAO,WAclB;AAEK,MAAM,sBAAsB,GAA8C,CAC/E,IAAgB,EACG,EAAE,CAAC,IAAA,sDAA2B,EAAC,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAFpF,QAAA,sBAAsB,0BAE8D;AAE1F,MAAM,yBAAyB,GAA8C,CAClF,IAAgB,EACG,EAAE,CAAC,IAAA,sDAA2B,EAAC,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAFrF,QAAA,yBAAyB,6BAE4D;AAElG,0DAA0D;AAC1D,mFAAmF;AACnF,qGAAqG;AACrG,0GAA0G;AAC1G,gHAAgH;AAChH,oBAAoB;AACb,MAAM,UAAU,GAA8C,CACnE,gBAAuC,EACvC,MAAgB,EAChB,UAA6B,EACnB,EAAE;IACZ,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;IAC1C,MAAM,IAAI,GAAG,gBAAS,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;IACzE,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC;IAE/B,MAAM,mBAAmB,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAC7D,MAAM,oBAAoB,GAAa,EAAE,CAAC;IAC1C,IAAI,IAAI,GAAa,EAAE,CAAC;IACxB,IAAI,gBAAgB,GAAa,EAAE,CAAC;IACpC,IAAI,kBAAuC,CAAC;IAE5C,IAAI,mBAAmB,EAAE;QACvB,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAErD,4DAA4D;QAC5D,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1D,kBAAkB,GAAG,IAAA,sDAA2B,EAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAC3D,gBAAgB,GAAG,IAAA,qBAAS,EAAC,gBAAgB,EAAE,MAAM,EAAE,kBAAkB,CAAC,CAAC;KAC5E;IAED,MAAM,eAAe,GAAG,mBAAmB;QACzC,CAAC,CAAC,gBAAS,CAAC,eAAe,CAAC,oBAAoB,EAAE,IAAI,GAAG,CAAC,CAAC;QAC3D,CAAC,CAAC,gBAAS,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;IACpD,MAAM,YAAY,GAAG,mBAAmB;QACtC,CAAC,CAAC,gBAAS,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,IAAI,GAAG,CAAC,CAAC;QAC7D,CAAC,CAAC,gBAAS,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC;IAEtD,MAAM,MAAM,GAAG,cAAc,CAC3B,gBAAgB,EAChB,mBAAmB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,EAC/C,UAAU,EACV,eAAe,EACf,YAAY,CACb,CAAC;IAEF,IAAI,mBAAmB,EAAE;QACvB,MAAM,cAAc,GAAG,IAAA,qBAAS,EAAC,gBAAgB,EAAE,MAAM,EAAE,kBAAmB,CAAC,CAAC;QAChF,OAAO,cAAc,CAAC;KACvB;SAAM;QACL,OAAO,MAAM,CAAC;KACf;AACH,CAAC,CAAC;AAnDW,QAAA,UAAU,cAmDrB;AAEF,MAAM,cAAc,GAAG,CACrB,gBAAuC,EACvC,MAAgB,EAChB,UAA6B,EAC7B,eAAuB,EACvB,YAAoB,EACV,EAAE;IACZ,MAAM,qBAAqB,GAAG,2BAA2B,CACvD,gBAAgB,EAChB,MAAM,CAAC,CAAC,CAAC,EACT,eAAe,EACf,YAAY,EACZ,CAAC,eAAe,CAAC,CAClB,CAAC;IACF,MAAM,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAC9B,EAAE,GAAG,gCAAgC,EAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,qBAAqB,EAAE,EACzG,MAAM,CACP,CAAC;IAEF,MAAM,uBAAuB,GAAG,4BAA4B,CAC1D,gBAAgB,EAChB,MAAM,CAAC,CAAC,CAAC,EACT,eAAe,EACf,YAAY,EACZ,qBAAqB,CAAC,MAAM,CAAC,IAAI,EACjC,CAAC,eAAe,CAAC,CAClB,CAAC;IACF,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAChC,EAAE,GAAG,kCAAkC,EAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,uBAAuB,EAAE,EAC7G,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CACjB,CAAC;IAEF,MAAM,kBAAkB,GAAG,wBAAwB,CACjD,gBAAgB,EAChB,MAAM,CAAC,CAAC,CAAC,EACT,eAAe,EACf,YAAY,EACZ,qBAAqB,CAAC,MAAM,CAAC,IAAI,EACjC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CACpC,CAAC;IACF,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CACjC,EAAE,GAAG,sBAAsB,EAAE,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,kBAAkB,EAAE,EAC5F,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CACxB,CAAC;IACF,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,2BAA2B,GAAG,CAClC,gBAAuC,EACvC,KAAa,EACb,eAAuB,EACvB,YAAoB,EACpB,WAAqB,EACR,EAAE;IACf,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,gBAAgB,CAAC,8BAA8B,CACnF,KAAK,CAAC,IAAI,EACV,mBAAW,CAAC,QAAQ,CACrB,CAAC;IACF,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAEhC,IAAI,eAAe,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;KAC/F;IAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;KAC7D;IAED,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;KAC7E;IAED,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,YAAY,GAAG;0BACG,IAAI;sDACwB,YAAY;;sCAE5B,IAAI,CAAC,SAAS,gDAAgD,YAAY;UACtG,aAAa;yBACE,YAAY;;4CAEO,IAAI,CAAC,SAAS;cAC5C,YAAY,KAAK,aAAa;;;;;;QAMpC,CAAC;IACP,OAAO;QACL,GAAG,gCAAgC;QACnC,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QAClF,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,4BAA4B,GAAG,CACnC,gBAAuC,EACvC,KAAa,EACb,eAAuB,EACvB,YAAoB,EACpB,uBAA0C,EAC1C,WAAqB,EACR,EAAE;IACf,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,gBAAgB,CAAC,8BAA8B,CACnF,KAAK,CAAC,IAAI,EACV,mBAAW,CAAC,QAAQ,CACrB,CAAC;IACF,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAEhC,IAAI,eAAe,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;KAC/F;IAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;KAC7D;IAED,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;KAC7E;IAED,IAAI,uBAAuB,CAAC,MAAM,KAAK,CAAC,EAAE;QACxC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;KAC3E;IAED,IAAI,uBAAuB,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;QAClD,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;KAC3F;IAED,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,YAAY,GAAG;0BACG,IAAI;sDACwB,YAAY;;;;yBAIzC,YAAY;;+CAEU,IAAI,CAAC,SAAS;cAC/C,YAAY,KAAK,aAAa;;;;QAIpC,CAAC;IACP,OAAO;QACL,GAAG,kCAAkC;QACrC,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QAClF,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,wBAAwB,GAAG,CAC/B,gBAAuC,EACvC,KAAa,EACb,eAAuB,EACvB,YAAoB,EACpB,uBAA0C,EAC1C,0BAA6C,EAChC,EAAE;IACf,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,gBAAgB,CAAC,8BAA8B,CACnF,KAAK,CAAC,IAAI,EACV,mBAAW,CAAC,QAAQ,CACrB,CAAC;IACF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;IAE/B,IAAI,eAAe,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,EAAE;QAC3C,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;KAC/F;IAED,IAAI,uBAAuB,CAAC,MAAM,KAAK,CAAC,IAAI,0BAA0B,CAAC,MAAM,KAAK,CAAC,EAAE;QACnF,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;KAC3E;IAED,IAAI,uBAAuB,CAAC,CAAC,CAAC,KAAK,eAAe,IAAI,0BAA0B,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;QACvG,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;KAC3F;IAED,MAAM,YAAY,GAAG;0BACG,IAAI;;;+CAGiB,YAAY,KAAK,aAAa;;;;wCAIrC,YAAY;;;;;;;;;;;MAW9C,CAAC;IACL,OAAO;QACL,GAAG,sBAAsB;QACzB,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QACjF,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;QAChE,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;KACvC;AACH,CAAC,CAAC"}
import { TNativeAudioWorkletNodeConstructor } from './native-audio-worklet-node-constructor';
import { TNativeOfflineAudioContextConstructor } from './native-offline-audio-context-constructor';
export type TTestAudioWorkletProcessorNoOutputsSupportFactory = (nativeAudioWorkletNodeConstructor: null | TNativeAudioWorkletNodeConstructor, nativeOfflineAudioContextConstructor: null | TNativeOfflineAudioContextConstructor) => () => Promise<boolean>;
//# sourceMappingURL=test-audio-worklet-processor-no-outputs-support-factory.d.ts.map
import { TTestAudioWorkletProcessorNoOutputsSupportFactory } from '../types';
/**
 * Chrome version 66 and 67 did not call the process() function of an AudioWorkletProcessor if it had no outputs. AudioWorklet support was
 * enabled by default in version 66.
 */
export declare const createTestAudioWorkletProcessorNoOutputsSupport: TTestAudioWorkletProcessorNoOutputsSupportFactory;
//# sourceMappingURL=test-audio-worklet-processor-no-outputs-support.d.ts.map
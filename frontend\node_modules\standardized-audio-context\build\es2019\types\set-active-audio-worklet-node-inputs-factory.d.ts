import { TActiveAudioWorkletNodeInputsStore } from './active-audio-worklet-node-inputs-store';
import { TSetActiveAudioWorkletNodeInputsFunction } from './set-active-audio-worklet-node-inputs-function';
export type TSetActiveAudioWorkletNodeInputsFactory = (activeAudioWorkletNodeInputsStore: TActiveAudioWorkletNodeInputsStore) => TSetActiveAudioWorkletNodeInputsFunction;
//# sourceMappingURL=set-active-audio-worklet-node-inputs-factory.d.ts.map
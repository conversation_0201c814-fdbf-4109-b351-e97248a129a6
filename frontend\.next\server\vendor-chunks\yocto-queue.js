"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/yocto-queue";
exports.ids = ["vendor-chunks/yocto-queue"];
exports.modules = {

/***/ "(ssr)/./node_modules/yocto-queue/index.js":
/*!*******************************************!*\
  !*** ./node_modules/yocto-queue/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Queue)\n/* harmony export */ });\n/*\nHow it works:\n`this.#head` is an instance of `Node` which keeps track of its current value and nests another instance of `Node` that keeps the value that comes after it. When a value is provided to `.enqueue()`, the code needs to iterate through `this.#head`, going deeper and deeper to find the last value. However, iterating through every single item is slow. This problem is solved by saving a reference to the last value as `this.#tail` so that it can reference it to add a new value.\n*/\n\nclass Node {\n\tvalue;\n\tnext;\n\n\tconstructor(value) {\n\t\tthis.value = value;\n\t}\n}\n\nclass Queue {\n\t#head;\n\t#tail;\n\t#size;\n\n\tconstructor() {\n\t\tthis.clear();\n\t}\n\n\tenqueue(value) {\n\t\tconst node = new Node(value);\n\n\t\tif (this.#head) {\n\t\t\tthis.#tail.next = node;\n\t\t\tthis.#tail = node;\n\t\t} else {\n\t\t\tthis.#head = node;\n\t\t\tthis.#tail = node;\n\t\t}\n\n\t\tthis.#size++;\n\t}\n\n\tdequeue() {\n\t\tconst current = this.#head;\n\t\tif (!current) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis.#head = this.#head.next;\n\t\tthis.#size--;\n\t\treturn current.value;\n\t}\n\n\tpeek() {\n\t\tif (!this.#head) {\n\t\t\treturn;\n\t\t}\n\n\t\treturn this.#head.value;\n\n\t\t// TODO: Node.js 18.\n\t\t// return this.#head?.value;\n\t}\n\n\tclear() {\n\t\tthis.#head = undefined;\n\t\tthis.#tail = undefined;\n\t\tthis.#size = 0;\n\t}\n\n\tget size() {\n\t\treturn this.#size;\n\t}\n\n\t* [Symbol.iterator]() {\n\t\tlet current = this.#head;\n\n\t\twhile (current) {\n\t\t\tyield current.value;\n\t\t\tcurrent = current.next;\n\t\t}\n\t}\n\n\t* drain() {\n\t\twhile (this.#head) {\n\t\t\tyield this.dequeue();\n\t\t}\n\t}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/yocto-queue/index.js\n");

/***/ })

};
;
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2hhY2tvbl90dHMlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3RzXFxcXGhhY2tvbl90dHNcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcaGFja29uX3R0c1xcZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXGhhY2tvbl90dHNcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcaGFja29uX3R0c1xcZnJvbnRlbmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8gfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBnZWlzdFNhbnMgPSBHZWlzdCh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1zYW5zXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJDcmVhdGUgTmV4dCBBcHBcIixcbiAgZGVzY3JpcHRpb246IFwiR2VuZXJhdGVkIGJ5IGNyZWF0ZSBuZXh0IGFwcFwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT17YCR7Z2Vpc3RTYW5zLnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gYW50aWFsaWFzZWRgfVxuICAgICAgPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImdlaXN0U2FucyIsImdlaXN0TW9ubyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\projects\\hackon_tts\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0cyU1QyU1Q2hhY2tvbl90dHMlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3RzXFxcXGhhY2tvbl90dHNcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cprojects%5C%5Chackon_tts%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var vad_web__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! vad-web */ \"(ssr)/./node_modules/vad-web/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Helper function to create WAV buffer with PCM 16-bit, 16kHz, Mono\nconst createWAVBuffer = (pcmData, sampleRate)=>{\n    const dataLength = pcmData.length * 2; // 2 bytes per sample (16-bit)\n    const buffer = new ArrayBuffer(44 + dataLength);\n    const view = new DataView(buffer);\n    // WAV header\n    // \"RIFF\" chunk descriptor\n    view.setUint32(0, 0x52494646, false); // \"RIFF\"\n    view.setUint32(4, 36 + dataLength, true); // File size - 8\n    view.setUint32(8, 0x57415645, false); // \"WAVE\"\n    // \"fmt \" sub-chunk\n    view.setUint32(12, 0x666d7420, false); // \"fmt \"\n    view.setUint32(16, 16, true); // Subchunk1Size (16 for PCM)\n    view.setUint16(20, 1, true); // AudioFormat (1 for PCM)\n    view.setUint16(22, 1, true); // NumChannels (1 for mono)\n    view.setUint32(24, sampleRate, true); // SampleRate\n    view.setUint32(28, sampleRate * 2, true); // ByteRate (SampleRate * NumChannels * BitsPerSample/8)\n    view.setUint16(32, 2, true); // BlockAlign (NumChannels * BitsPerSample/8)\n    view.setUint16(34, 16, true); // BitsPerSample (16-bit)\n    // \"data\" sub-chunk\n    view.setUint32(36, 0x64617461, false); // \"data\"\n    view.setUint32(40, dataLength, true); // Subchunk2Size\n    // Write PCM data\n    let offset = 44;\n    for(let i = 0; i < pcmData.length; i++){\n        view.setInt16(offset, pcmData[i], true);\n        offset += 2;\n    }\n    return buffer;\n};\n// Helper function to resample audio data\nconst resampleAudio = (audioData, originalSampleRate, targetSampleRate)=>{\n    if (originalSampleRate === targetSampleRate) {\n        return audioData;\n    }\n    const ratio = originalSampleRate / targetSampleRate;\n    const newLength = Math.round(audioData.length / ratio);\n    const result = new Float32Array(newLength);\n    for(let i = 0; i < newLength; i++){\n        const index = i * ratio;\n        const indexFloor = Math.floor(index);\n        const indexCeil = Math.min(indexFloor + 1, audioData.length - 1);\n        const fraction = index - indexFloor;\n        // Linear interpolation\n        result[i] = audioData[indexFloor] * (1 - fraction) + audioData[indexCeil] * fraction;\n    }\n    return result;\n};\nfunction Home() {\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Disconnected\");\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioPlayerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const vadDisposeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            return ({\n                \"Home.useEffect\": ()=>{\n                    if (socketRef.current) {\n                        socketRef.current.close();\n                    }\n                    if (vadDisposeRef.current) {\n                        vadDisposeRef.current();\n                    }\n                    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                        mediaRecorderRef.current.stop();\n                    }\n                    if (streamRef.current) {\n                        streamRef.current.getTracks().forEach({\n                            \"Home.useEffect\": (track)=>track.stop()\n                        }[\"Home.useEffect\"]);\n                    }\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    // Function to update audio level visualization\n    const updateAudioLevel = (audioData)=>{\n        // Calculate audio level from the audio data\n        let sum = 0;\n        for(let i = 0; i < audioData.length; i++){\n            sum += Math.abs(audioData[i]);\n        }\n        const avg = sum / audioData.length;\n        // Normalize and amplify for better visualization\n        const level = Math.min(1, avg * 5);\n        setAudioLevel(level);\n        // Log audio levels periodically to help debug\n        if (Math.random() < 0.01) {\n            console.log(\"Current audio level:\", level.toFixed(3));\n        }\n    };\n    const handleToggleRecording = ()=>{\n        if (isRecording) {\n            // Stop recording\n            console.log(\"Stopping recording...\");\n            // Stop VAD\n            if (vadDisposeRef.current) {\n                vadDisposeRef.current();\n                vadDisposeRef.current = null;\n            }\n            // Stop MediaRecorder\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            // Stop media stream\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach((track)=>track.stop());\n                streamRef.current = null;\n            }\n            // Close WebSocket\n            if (socketRef.current) {\n                socketRef.current.close();\n            }\n            setIsRecording(false);\n            setAudioLevel(0);\n        } else {\n            // Start recording\n            console.log(\"Starting recording...\");\n            setMessages([]);\n            connectSocketAndStartRecording();\n        }\n    };\n    const connectSocketAndStartRecording = (isReconnect = false)=>{\n        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {\n            console.log(\"WebSocket already connected, starting recording.\");\n            startRecording();\n            return;\n        }\n        setConnectionStatus(\"Connecting...\");\n        console.log(\"Attempting to connect WebSocket...\");\n        const socket = new WebSocket(\"ws://127.0.0.1:8000/api/agent/voice\");\n        socketRef.current = socket;\n        socket.onopen = ()=>{\n            setConnectionStatus(\"Connected\");\n            console.log(\"WebSocket connection established.\");\n            // Only start recording if it's not a reconnect action\n            if (!isReconnect) {\n                startRecording();\n            }\n        };\n        socket.onmessage = (event)=>{\n            console.log(\"WebSocket message received:\", event.data);\n            if (typeof event.data === \"string\") {\n                const message = JSON.parse(event.data);\n                setMessages((prev)=>[\n                        ...prev,\n                        message\n                    ]);\n            } else {\n                // Handle audio data\n                const audioBlob = new Blob([\n                    event.data\n                ], {\n                    type: \"audio/wav\"\n                });\n                const audioUrl = URL.createObjectURL(audioBlob);\n                if (audioPlayerRef.current) {\n                    audioPlayerRef.current.src = audioUrl;\n                    audioPlayerRef.current.play();\n                }\n            }\n        };\n        socket.onclose = ()=>{\n            setConnectionStatus(\"Disconnected\");\n            setIsRecording(false);\n            setAudioLevel(0);\n            console.log(\"WebSocket connection closed.\");\n            // Clean up VAD\n            if (vadDisposeRef.current) {\n                vadDisposeRef.current();\n                vadDisposeRef.current = null;\n            }\n            // Clean up MediaRecorder\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            // Clean up media stream\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach((track)=>track.stop());\n                streamRef.current = null;\n            }\n        };\n        socket.onerror = (error)=>{\n            console.error(\"WebSocket error:\", error);\n            setConnectionStatus(\"Error: Connection failed\");\n            setIsRecording(false);\n        };\n    };\n    const startRecording = async ()=>{\n        if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {\n            console.error(\"Cannot start recording, WebSocket is not open.\");\n            setConnectionStatus(\"Error: Not connected\");\n            return;\n        }\n        try {\n            setIsRecording(true);\n            // Get user media\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    echoCancellation: false,\n                    noiseSuppression: false,\n                    autoGainControl: true,\n                    channelCount: 1,\n                    sampleRate: 16000\n                }\n            });\n            streamRef.current = stream;\n            // Set up MediaRecorder with WAV format processing\n            // Use MediaRecorder to capture audio, then convert to WAV\n            const mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm;codecs=opus\"\n            });\n            mediaRecorderRef.current = mediaRecorder;\n            // Process audio data when available\n            mediaRecorder.ondataavailable = async (event)=>{\n                if (event.data.size > 0) {\n                    try {\n                        // Convert the blob to audio data\n                        const arrayBuffer = await event.data.arrayBuffer();\n                        const audioContext = new AudioContext({\n                            sampleRate: 16000\n                        });\n                        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);\n                        // Get mono channel data (convert to mono if stereo)\n                        const channelData = audioBuffer.getChannelData(0);\n                        // Resample to 16kHz if needed\n                        const targetSampleRate = 16000;\n                        const resampledData = resampleAudio(channelData, audioBuffer.sampleRate, targetSampleRate);\n                        // Convert to PCM 16-bit\n                        const pcmData = new Int16Array(resampledData.length);\n                        for(let i = 0; i < resampledData.length; i++){\n                            const sample = Math.max(-1, Math.min(1, resampledData[i]));\n                            pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;\n                        }\n                        // Create WAV buffer\n                        const wavBuffer = createWAVBuffer(pcmData, targetSampleRate);\n                        if (socketRef.current?.readyState === WebSocket.OPEN) {\n                            console.log(\"Sending WAV audio chunk, size:\", wavBuffer.byteLength);\n                            socketRef.current.send(wavBuffer);\n                        }\n                        await audioContext.close();\n                    } catch (error) {\n                        console.error(\"Error processing audio data:\", error);\n                    }\n                }\n            };\n            // Start recording with small time slices\n            mediaRecorder.start(250); // 250ms chunks for real-time processing\n            // Set up VAD\n            const vadDispose = await (0,vad_web__WEBPACK_IMPORTED_MODULE_2__.recordAudio)({\n                onSpeechStart: ()=>{\n                    console.log(\"Speech detected - starting to send audio\");\n                    setAudioLevel(0.8); // Visual feedback for speech detection\n                },\n                onSpeechEnd: ()=>{\n                    console.log(\"Speech ended\");\n                    setAudioLevel(0);\n                },\n                onSpeechAvailable: (speechData)=>{\n                    console.log(\"Speech available:\", speechData);\n                    // Update audio level based on speech data\n                    updateAudioLevel(speechData.audioData);\n                },\n                onSpeechOngoing: (speechData)=>{\n                    // Update audio level during ongoing speech\n                    updateAudioLevel(speechData.audioData);\n                }\n            });\n            vadDisposeRef.current = vadDispose;\n            console.log(\"Recording started with VAD and MediaRecorder\");\n        } catch (error) {\n            console.error(\"Error starting recording:\", error);\n            setConnectionStatus(\"Error: Could not access microphone\");\n            setIsRecording(false);\n            socketRef.current?.close();\n        }\n    };\n    const handleReconnect = ()=>{\n        console.log(\"Reconnect button clicked.\");\n        connectSocketAndStartRecording(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 font-sans\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-center text-gray-800 dark:text-white mb-4\",\n                    children: \"Real-time Audio Chat\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleToggleRecording,\n                                            className: `px-6 py-3 rounded-full text-white font-semibold transition-colors ${isRecording ? \"bg-red-500 hover:bg-red-600\" : \"bg-blue-500 hover:bg-blue-600\"}`,\n                                            disabled: connectionStatus === \"Connecting...\" || isRecording && connectionStatus !== \"Connected\",\n                                            children: isRecording ? \"Stop Recording\" : \"Start Recording\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        (connectionStatus === \"Disconnected\" || connectionStatus.startsWith(\"Error\")) && !isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"px-6 py-3 rounded-full text-white font-semibold transition-colors bg-green-500 hover:bg-green-600\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-sm text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        \"Status: \",\n                                        connectionStatus\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Audio Level:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-green-500\",\n                                                style: {\n                                                    width: `${Math.max(5, audioLevel * 100)}%`,\n                                                    transition: \"width 0.1s ease-out\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        audioLevel === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-500 mt-1\",\n                                            children: \"Warning: No audio detected! Check your microphone settings.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 space-y-4\",\n                            children: messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-3 rounded-lg ${msg.type === \"user_transcript\" ? \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 self-end\" : \"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 self-start\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: msg.type === \"user_transcript\" ? \"You\" : \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: msg.data\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                    ref: audioPlayerRef,\n                    className: \"hidden\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 356,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 355,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/vad-web","vendor-chunks/comlink","vendor-chunks/p-limit","vendor-chunks/yocto-queue","vendor-chunks/just-once","vendor-chunks/subscribable-things","vendor-chunks/worker-factory","vendor-chunks/recorder-audio-worklet","vendor-chunks/@swc","vendor-chunks/fast-unique-numbers","vendor-chunks/rxjs-interop"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cprojects%5Chackon_tts%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
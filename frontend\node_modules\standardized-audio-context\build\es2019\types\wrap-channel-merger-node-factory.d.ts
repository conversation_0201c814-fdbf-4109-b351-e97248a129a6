import { TInvalidStateErrorFactory } from './invalid-state-error-factory';
import { TMonitorConnectionsFunction } from './monitor-connections-function';
import { TWrapChannelMergerNodeFunction } from './wrap-channel-merger-node-function';
export type TWrapChannelMergerNodeFactory = (createInvalidStateError: TInvalidStateErrorFactory, monitorConnectionsFunction: TMonitorConnectionsFunction) => TWrapChannelMergerNodeFunction;
//# sourceMappingURL=wrap-channel-merger-node-factory.d.ts.map
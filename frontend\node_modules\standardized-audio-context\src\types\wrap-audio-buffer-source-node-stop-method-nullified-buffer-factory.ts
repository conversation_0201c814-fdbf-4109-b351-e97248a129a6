import { TOverwriteAccessorsFunction } from './overwrite-accessors-function';
import { TWrapAudioBufferSourceNodeStopMethodNullifiedBufferFunction } from './wrap-audio-buffer-source-node-stop-method-nullified-buffer-function';

export type TWrapAudioBufferSourceNodeStopMethodNullifiedBufferFactory = (
    overwriteAccessors: TOverwriteAccessorsFunction
) => TWrapAudioBufferSourceNodeStopMethodNullifiedBufferFunction;

"use client";

import { useState, useRef, useEffect } from "react";
import type RecordRTC from "recordrtc";

// Helper function to convert AudioBuffer to WAV format
const audioBufferToWav = (buffer: AudioBuffer): Blob => {
  const numOfChannels = buffer.numberOfChannels;
  const sampleRate = buffer.sampleRate;
  const length = buffer.length;
  const bytesPerSample = 2; // Use 16-bit samples
  const bitsPerSample = bytesPerSample * 8;

  // Create the buffer for the WAV file
  const dataLength = length * numOfChannels * bytesPerSample;
  const arrayBuffer = new ArrayBuffer(44 + dataLength);
  const view = new DataView(arrayBuffer);

  // Write the WAV header
  // "RIFF" chunk descriptor
  writeString(view, 0, "RIFF");
  view.setUint32(4, 36 + dataLength, true);
  writeString(view, 8, "WAVE");

  // "fmt " sub-chunk
  writeString(view, 12, "fmt ");
  view.setUint32(16, 16, true); // Subchunk1Size (16 for PCM)
  view.setUint16(20, 1, true); // AudioFormat (1 for PCM)
  view.setUint16(22, numOfChannels, true); // NumChannels
  view.setUint32(24, sampleRate, true); // SampleRate
  view.setUint32(28, sampleRate * numOfChannels * bytesPerSample, true); // ByteRate
  view.setUint16(32, numOfChannels * bytesPerSample, true); // BlockAlign
  view.setUint16(34, bitsPerSample, true); // BitsPerSample

  // "data" sub-chunk
  writeString(view, 36, "data");
  view.setUint32(40, dataLength, true); // Subchunk2Size

  // Write the PCM samples
  const channelData = [];
  let offset = 44;

  for (let i = 0; i < numOfChannels; i++) {
    channelData.push(buffer.getChannelData(i));
  }

  for (let i = 0; i < length; i++) {
    for (let c = 0; c < numOfChannels; c++) {
      // Convert float value (-1 to 1) to int16
      const sample = Math.max(-1, Math.min(1, channelData[c][i]));
      const value = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
      view.setInt16(offset, value, true);
      offset += bytesPerSample;
    }
  }

  function writeString(view: DataView, offset: number, string: string) {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  }

  return new Blob([view], { type: "audio/wav" });
};

type Message = {
  type: "user_transcript" | "ai_response_text";
  data: string;
};

export default function Home() {
  const [isRecording, setIsRecording] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [connectionStatus, setConnectionStatus] = useState("Disconnected");
  const [savedRecordings, setSavedRecordings] = useState<string[]>([]);
  const [audioLevel, setAudioLevel] = useState<number>(0);
  const socketRef = useRef<WebSocket | null>(null);
  const audioAnalyserRef = useRef<AnalyserNode | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  // Define a custom type for our enhanced recorder
  type EnhancedRecorder = {
    highQualityRecorder: RecordRTC;
    stopRecording: (callback: () => void) => void;
    getBlob: (callback: (blob: Blob) => void) => void;
    destroy: () => void;
    state: string;
  } & RecordRTC;

  const recorderRef = useRef<EnhancedRecorder | null>(null);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);
  const RecordRTCRef = useRef<any | null>(null);
  const rawAudioChunksRef = useRef<Blob[]>([]);

  useEffect(() => {
    import("recordrtc").then((mod) => {
      RecordRTCRef.current = mod.default;
    });

    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
      if (recorderRef.current) {
        recorderRef.current.destroy();
      }
    };
  }, []);

  // Add a function to update audio level visualization with enhanced sensitivity
  const updateAudioVisualization = () => {
    if (audioAnalyserRef.current) {
      const dataArray = new Uint8Array(audioAnalyserRef.current.fftSize);
      audioAnalyserRef.current.getByteTimeDomainData(dataArray);

      // Calculate audio level with increased sensitivity for earphones
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        // Use a higher multiplier to amplify small differences from the midpoint (128)
        sum += Math.abs(dataArray[i] - 128) * 1.5;
      }
      const avg = sum / dataArray.length;
      // Apply a lower threshold and amplification factor to make the meter more responsive
      // Amplify the level by 2x for better visualization with quiet inputs
      const level = Math.min(1, (avg / 64) * 2); // More sensitive normalization

      setAudioLevel(level);

      // Log audio levels periodically to help debug
      if (Math.random() < 0.01) {
        // Log roughly once per second
        console.log(
          "Current audio level:",
          level.toFixed(3),
          "Raw avg:",
          avg.toFixed(3)
        );
      }

      // Continue animation loop
      animationFrameRef.current = requestAnimationFrame(
        updateAudioVisualization
      );
    }
  };

  // Clean up resources on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  const handleToggleRecording = () => {
    if (isRecording) {
      // Stop recording
      console.log("Stopping recording...");

      // Clean up audio visualization
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      if (audioContextRef.current) {
        audioContextRef.current
          .suspend()
          .catch((err) =>
            console.error("Error suspending audio context:", err)
          );
      }

      if (recorderRef.current) {
        recorderRef.current.stopRecording(() => {
          // Save the complete recording before closing connection
          recorderRef.current?.getBlob((blob: Blob) => {
            const url = URL.createObjectURL(blob);
            setSavedRecordings((prev) => [...prev, url]);
            console.log("Recording saved at:", url);

            // Create a download link for the complete recording
            const a = document.createElement("a");
            a.style.display = "none";
            a.href = url;
            a.download = `high_quality_recording_${Date.now()}.wav`;
            document.body.appendChild(a);
            a.click();
            setTimeout(() => {
              document.body.removeChild(a);
            }, 100);
          });

          // Also save the raw audio chunks as a WAV file for better quality
          if (rawAudioChunksRef.current.length > 0) {
            try {
              // First attempt to save as PCM with proper WAV headers
              const audioContext = new (window.AudioContext ||
                (window as any).webkitAudioContext)();

              // Process the raw chunks to ensure they are properly formatted
              Promise.all(
                rawAudioChunksRef.current.map((blob) =>
                  new Response(blob)
                    .arrayBuffer()
                    .then((buffer) => audioContext.decodeAudioData(buffer))
                )
              )
                .then((audioBuffers) => {
                  // Calculate total length of all audio buffers
                  let totalLength = 0;
                  audioBuffers.forEach((buffer) => {
                    totalLength += buffer.length;
                  });

                  // Create offline context with size based on actual recording length
                  // Add a small buffer (1 second) to ensure we have enough space
                  const offlineContext = new OfflineAudioContext({
                    numberOfChannels: 1,
                    length: totalLength + 44100, // Actual length + 1 second buffer
                    sampleRate: 44100,
                  });

                  // Create source nodes and connect them
                  let offset = 0;
                  audioBuffers.forEach((buffer) => {
                    const source = offlineContext.createBufferSource();
                    source.buffer = buffer;
                    source.connect(offlineContext.destination);
                    source.start(offset);
                    offset += buffer.duration;
                  });

                  // Render and create WAV from the rendered buffer
                  offlineContext.startRendering().then((renderedBuffer) => {
                    // Convert to WAV
                    const wavBlob = audioBufferToWav(renderedBuffer);
                    const url = URL.createObjectURL(wavBlob);
                    setSavedRecordings((prev) => [...prev, url]);
                    console.log("Processed audio chunks saved as WAV at:", url);

                    // Create a download link for the processed audio
                    const a = document.createElement("a");
                    a.style.display = "none";
                    a.href = url;
                    a.download = `processed_audio_${Date.now()}.wav`;
                    document.body.appendChild(a);
                    a.click();
                    setTimeout(() => {
                      document.body.removeChild(a);
                    }, 100);
                  });
                })
                .catch((err) => {
                  console.error("Error processing audio chunks:", err);

                  // Fallback: Simply concatenate the raw chunks
                  const combinedBlob = new Blob(rawAudioChunksRef.current, {
                    type: "audio/wav",
                  });
                  const url = URL.createObjectURL(combinedBlob);
                  setSavedRecordings((prev) => [...prev, url]);
                  console.log(
                    "Raw audio chunks saved (fallback) as WAV at:",
                    url
                  );

                  // Create a download link for the raw chunks
                  const a = document.createElement("a");
                  a.style.display = "none";
                  a.href = url;
                  a.download = `raw_chunks_fallback_${Date.now()}.wav`;
                  document.body.appendChild(a);
                  a.click();
                  setTimeout(() => {
                    document.body.removeChild(a);
                  }, 100);
                });
            } catch (err) {
              console.error("Error in audio processing:", err);
              // Fallback to original method
              const combinedBlob = new Blob(rawAudioChunksRef.current, {
                type: "audio/wav",
              });
              const url = URL.createObjectURL(combinedBlob);
              setSavedRecordings((prev) => [...prev, url]);
              console.log("Raw audio chunks saved as WAV at:", url);

              // Create a download link for the raw chunks
              const a = document.createElement("a");
              a.style.display = "none";
              a.href = url;
              a.download = `raw_chunks_${Date.now()}.wav`;
              document.body.appendChild(a);
              a.click();
              setTimeout(() => {
                document.body.removeChild(a);
              }, 100);
            }
          }

          // Reset raw audio chunks for next recording
          rawAudioChunksRef.current = [];

          // The onclose event of the socket will handle the state changes.
          console.log("Recording stopped, closing WebSocket.");
          socketRef.current?.close();
        });
      }
    } else {
      // Start recording
      console.log("Starting recording...");
      if (!RecordRTCRef.current) {
        alert("RecordRTC is not loaded yet. Please try again.");
        return;
      }
      setMessages([]);
      connectSocketAndStartRecording();
    }
  };

  const connectSocketAndStartRecording = (isReconnect = false) => {
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      console.log("WebSocket already connected, starting recording.");
      startRecording();
      return;
    }

    setConnectionStatus("Connecting...");
    console.log("Attempting to connect WebSocket...");
    const socket = new WebSocket("ws://localhost:8000/ws");
    socketRef.current = socket;

    socket.onopen = () => {
      setConnectionStatus("Connected");
      console.log("WebSocket connection established.");
      // Only start recording if it's not a reconnect action
      if (!isReconnect) {
        startRecording();
      }
    };

    socket.onmessage = (event) => {
      console.log("WebSocket message received:", event.data);
      if (typeof event.data === "string") {
        const message: Message = JSON.parse(event.data);
        setMessages((prev) => [...prev, message]);
      } else {
        // Handle audio data
        const audioBlob = new Blob([event.data], { type: "audio/wav" });
        const audioUrl = URL.createObjectURL(audioBlob);
        if (audioPlayerRef.current) {
          audioPlayerRef.current.src = audioUrl;
          audioPlayerRef.current.play();
        }
      }
    };

    socket.onclose = () => {
      setConnectionStatus("Disconnected");
      setIsRecording(false);
      console.log("WebSocket connection closed.");

      // Clean up audio visualization
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      if (audioContextRef.current) {
        audioContextRef.current
          .suspend()
          .catch((err) =>
            console.error("Error suspending audio context:", err)
          );
      }

      if (audioAnalyserRef.current) {
        audioAnalyserRef.current = null;
      }

      if (recorderRef.current) {
        // Ensure recording is stopped and resources are released
        if (recorderRef.current.state === "recording") {
          try {
            recorderRef.current.stopRecording(() => {
              console.log(
                "Recording stopped successfully after connection closed"
              );
            });
          } catch (err) {
            console.error("Error stopping recording:", err);
          }
        }
        try {
          recorderRef.current.destroy();
          console.log("Recorder resources destroyed successfully");
        } catch (err) {
          console.error("Error destroying recorder resources:", err);
        }
        recorderRef.current = null;
      }
    };

    socket.onerror = (error) => {
      console.error("WebSocket error:", error);
      setConnectionStatus("Error: Connection failed");
      setIsRecording(false);
    };
  };
  const startRecording = () => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      console.error("Cannot start recording, WebSocket is not open.");
      setConnectionStatus("Error: Not connected");
      return;
    }
    setIsRecording(true);

    // Request audio with optimized constraints for earphones/headsets with amplified sound
    navigator.mediaDevices
      .getUserMedia({
        audio: {
          echoCancellation: false, // Disable echo cancellation for better recording with earphones
          noiseSuppression: false, // Disable noise suppression for cleaner audio capture
          autoGainControl: true, // Enable auto gain to boost volume levels
          channelCount: 1, // Mono is more reliable for voice input
          sampleRate: 44100, // Higher sample rate for better quality
        },
      })
      .then((stream) => {
        if (!RecordRTCRef.current) {
          throw new Error("RecordRTC not available");
        }

        // Set up audio analyzer for visualization with higher sensitivity
        try {
          // Create audio context
          const audioContext = new (window.AudioContext ||
            (window as any).webkitAudioContext)();
          audioContextRef.current = audioContext;

          // Create analyzer with increased sensitivity
          const analyser = audioContext.createAnalyser();
          analyser.fftSize = 256; // Smaller FFT size for more responsive visualization
          analyser.minDecibels = -90; // Increase sensitivity by lowering minimum dB threshold (default is -100)
          analyser.maxDecibels = -30; // Lower maximum threshold to amplify small sounds (default is -30)
          analyser.smoothingTimeConstant = 0.5; // Medium smoothing for better visualization
          audioAnalyserRef.current = analyser;

          // Connect stream to analyzer
          const source = audioContext.createMediaStreamSource(stream);
          source.connect(analyser);

          // Start visualization
          updateAudioVisualization();

          console.log("High-sensitivity audio analyzer set up successfully");
        } catch (err) {
          console.error("Failed to set up audio analyzer:", err);
        }

        // Create a separate high-quality recorder for local saving with optimized settings for earphones
        const highQualityRecorder = new RecordRTCRef.current(stream, {
          type: "audio",
          mimeType: "audio/wav",
          recorderType: RecordRTCRef.current.StereoAudioRecorder,
          numberOfAudioChannels: 1, // Mono for consistent recording with earphones
          sampleRate: 44100, // High quality sample rate
          desiredSampRate: 44100,
          disableLogs: false, // Enable logs to debug recording issues
          bufferSize: 4096, // Smaller buffer for more responsive audio capture
          // Increase the volume by setting a higher volume parameter
          volume: 1.0,
          // Don't need timeSlice here as we just want a complete recording
        });
        highQualityRecorder.startRecording();

        // Recorder for streaming to the backend with enhanced settings for earphones
        const streamRecorder = new RecordRTCRef.current(stream, {
          type: "audio",
          mimeType: "audio/webm", // WebM format is better supported across browsers
          recorderType: RecordRTCRef.current.StereoAudioRecorder,
          timeSlice: 500, // Send data more frequently for better responsiveness
          ondataavailable: (blob: Blob) => {
            console.log("Audio chunk received, size:", blob.size);
            // Only save non-empty blobs
            if (blob.size > 0) {
              // Save the blob for later use
              rawAudioChunksRef.current.push(blob);

              if (socketRef.current?.readyState === WebSocket.OPEN) {
                socketRef.current.send(blob);
              }
            } else {
              console.warn("Empty audio chunk received and skipped");
            }
          },
          sampleRate: 44100,
          desiredSampRate: 44100,
          numberOfAudioChannels: 1, // Mono for consistent recording with earphones
          bufferSize: 8192, // Medium buffer size for balance of responsiveness and stability
          volume: 1.0, // Maximum volume
          // Apply extra processing to enhance audio from earphones
          audioProcessingConfiguration: {
            applyGainToMicrophoneInput: true, // Apply gain to boost low-volume inputs
          },
        });
        streamRecorder.startRecording();

        // Store the stream recorder as the main recorder reference
        recorderRef.current = {
          ...streamRecorder,
          highQualityRecorder,
          stopRecording: (callback: () => void) => {
            streamRecorder.stopRecording(() => {
              highQualityRecorder.stopRecording(() => {
                callback();
              });
            });
          },
          getBlob: (callback) => {
            highQualityRecorder.getBlob(callback);
          },
          destroy: () => {
            streamRecorder.destroy();
            highQualityRecorder.destroy();
          },
          state: streamRecorder.state,
        };

        // We already set up audio processing for visualization earlier,
        // no need to create duplicate audio contexts or analyzers
        // Just ensure visualization is running
        if (animationFrameRef.current === null) {
          updateAudioVisualization();
        }

        console.log(
          "Recording started with high quality local recording enabled."
        );
      })
      .catch((error) => {
        console.error("Error accessing microphone:", error);
        setConnectionStatus("Error: Could not access microphone");
        socketRef.current?.close();
      });
  };

  const handleReconnect = () => {
    console.log("Reconnect button clicked.");
    connectSocketAndStartRecording(true);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 font-sans">
      <div className="w-full max-w-2xl p-4">
        <h1 className="text-4xl font-bold text-center text-gray-800 dark:text-white mb-4">
          Real-time Audio Chat
        </h1>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex flex-col items-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleToggleRecording}
                className={`px-6 py-3 rounded-full text-white font-semibold transition-colors ${
                  isRecording
                    ? "bg-red-500 hover:bg-red-600"
                    : "bg-blue-500 hover:bg-blue-600"
                }`}
                disabled={
                  connectionStatus === "Connecting..." ||
                  (isRecording && connectionStatus !== "Connected")
                }
              >
                {isRecording ? "Stop Recording" : "Start Recording"}
              </button>
              {(connectionStatus === "Disconnected" ||
                connectionStatus.startsWith("Error")) &&
                !isRecording && (
                  <button
                    onClick={handleReconnect}
                    className="px-6 py-3 rounded-full text-white font-semibold transition-colors bg-green-500 hover:bg-green-600"
                  >
                    Reconnect
                  </button>
                )}
            </div>
            <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              Status: {connectionStatus}
            </p>

            {isRecording && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Audio Level:
                </p>
                <div className="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-green-500"
                    style={{
                      width: `${Math.max(5, audioLevel * 100)}%`,
                      transition: "width 0.1s ease-out",
                    }}
                  />
                </div>
                {audioLevel === 0 && (
                  <p className="text-xs text-red-500 mt-1">
                    Warning: No audio detected! Check your microphone settings.
                  </p>
                )}
              </div>
            )}
          </div>
          <div className="mt-6 space-y-4">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg ${
                  msg.type === "user_transcript"
                    ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 self-end"
                    : "bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 self-start"
                }`}
              >
                <p className="font-semibold">
                  {msg.type === "user_transcript" ? "You" : "AI"}
                </p>
                <p>{msg.data}</p>
              </div>
            ))}
          </div>
        </div>

        {savedRecordings.length > 0 && (
          <div className="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
              Saved Recordings
            </h2>
            <div className="space-y-4">
              {savedRecordings.map((url, index) => (
                <div key={index} className="flex flex-col space-y-2">
                  <p className="font-medium text-gray-700 dark:text-gray-300">
                    Recording {index + 1}
                  </p>
                  <audio
                    controls
                    src={url}
                    className="w-full"
                    onError={(e) => console.error("Audio playback error:", e)}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        <audio ref={audioPlayerRef} className="hidden" />
      </div>
    </div>
  );
}

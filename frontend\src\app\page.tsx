"use client";

import { useState, useRef, useEffect } from "react";
import { recordAudio, type SpeechData, type DisposeFunction } from "vad-web";

type Message = {
  type: "user_transcript" | "ai_response_text";
  data: string;
};

export default function Home() {
  const [isRecording, setIsRecording] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [connectionStatus, setConnectionStatus] = useState("Disconnected");
  const [audioLevel, setAudioLevel] = useState<number>(0);
  const socketRef = useRef<WebSocket | null>(null);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);
  const vadDisposeRef = useRef<DisposeFunction | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  useEffect(() => {
    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
      if (vadDisposeRef.current) {
        vadDisposeRef.current();
      }
      if (
        mediaRecorderRef.current &&
        mediaRecorderRef.current.state !== "inactive"
      ) {
        mediaRecorderRef.current.stop();
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  // Function to update audio level visualization
  const updateAudioLevel = (audioData: Float32Array) => {
    // Calculate audio level from the audio data
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += Math.abs(audioData[i]);
    }
    const avg = sum / audioData.length;
    // Normalize and amplify for better visualization
    const level = Math.min(1, avg * 5);

    setAudioLevel(level);

    // Log audio levels periodically to help debug
    if (Math.random() < 0.01) {
      console.log("Current audio level:", level.toFixed(3));
    }
  };

  const handleToggleRecording = () => {
    if (isRecording) {
      // Stop recording
      console.log("Stopping recording...");

      // Stop VAD
      if (vadDisposeRef.current) {
        vadDisposeRef.current();
        vadDisposeRef.current = null;
      }

      // Stop MediaRecorder
      if (
        mediaRecorderRef.current &&
        mediaRecorderRef.current.state !== "inactive"
      ) {
        mediaRecorderRef.current.stop();
      }

      // Stop media stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
        streamRef.current = null;
      }

      // Close WebSocket
      if (socketRef.current) {
        socketRef.current.close();
      }

      setIsRecording(false);
      setAudioLevel(0);
    } else {
      // Start recording
      console.log("Starting recording...");
      setMessages([]);
      connectSocketAndStartRecording();
    }
  };

  const connectSocketAndStartRecording = (isReconnect = false) => {
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      console.log("WebSocket already connected, starting recording.");
      startRecording();
      return;
    }

    setConnectionStatus("Connecting...");
    console.log("Attempting to connect WebSocket...");
    const socket = new WebSocket("ws://127.0.0.1:8000/api/agent/voice");
    socketRef.current = socket;

    socket.onopen = () => {
      setConnectionStatus("Connected");
      console.log("WebSocket connection established.");
      // Only start recording if it's not a reconnect action
      if (!isReconnect) {
        startRecording();
      }
    };

    socket.onmessage = (event) => {
      console.log("WebSocket message received:", event.data);
      if (typeof event.data === "string") {
        const message: Message = JSON.parse(event.data);
        setMessages((prev) => [...prev, message]);
      } else {
        // Handle audio data
        const audioBlob = new Blob([event.data], { type: "audio/wav" });
        const audioUrl = URL.createObjectURL(audioBlob);
        if (audioPlayerRef.current) {
          audioPlayerRef.current.src = audioUrl;
          audioPlayerRef.current.play();
        }
      }
    };

    socket.onclose = () => {
      setConnectionStatus("Disconnected");
      setIsRecording(false);
      setAudioLevel(0);
      console.log("WebSocket connection closed.");

      // Clean up VAD
      if (vadDisposeRef.current) {
        vadDisposeRef.current();
        vadDisposeRef.current = null;
      }

      // Clean up MediaRecorder
      if (
        mediaRecorderRef.current &&
        mediaRecorderRef.current.state !== "inactive"
      ) {
        mediaRecorderRef.current.stop();
      }

      // Clean up media stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
        streamRef.current = null;
      }
    };

    socket.onerror = (error) => {
      console.error("WebSocket error:", error);
      setConnectionStatus("Error: Connection failed");
      setIsRecording(false);
    };
  };
  const startRecording = async () => {
    if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {
      console.error("Cannot start recording, WebSocket is not open.");
      setConnectionStatus("Error: Not connected");
      return;
    }

    try {
      setIsRecording(true);

      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: true,
          channelCount: 1,
          sampleRate: 16000, // VAD works best with 16kHz
        },
      });

      streamRef.current = stream;

      // Set up MediaRecorder for real-time streaming
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "audio/webm;codecs=opus",
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (
          event.data.size > 0 &&
          socketRef.current?.readyState === WebSocket.OPEN
        ) {
          console.log("Sending audio chunk, size:", event.data.size);
          socketRef.current.send(event.data);
        }
      };

      // Start MediaRecorder with small time slices for real-time streaming
      mediaRecorder.start(100); // 100ms chunks

      // Set up VAD
      const vadDispose = await recordAudio({
        onSpeechStart: () => {
          console.log("Speech detected - starting to send audio");
          setAudioLevel(0.8); // Visual feedback for speech detection
        },
        onSpeechEnd: () => {
          console.log("Speech ended");
          setAudioLevel(0);
        },
        onSpeechAvailable: (speechData: SpeechData) => {
          console.log("Speech available:", speechData);
          // Update audio level based on speech data
          updateAudioLevel(speechData.audioData);
        },
        onSpeechOngoing: (speechData: SpeechData) => {
          // Update audio level during ongoing speech
          updateAudioLevel(speechData.audioData);
        },
      });

      vadDisposeRef.current = vadDispose;

      console.log("Recording started with VAD and MediaRecorder");
    } catch (error) {
      console.error("Error starting recording:", error);
      setConnectionStatus("Error: Could not access microphone");
      setIsRecording(false);
      socketRef.current?.close();
    }
  };

  const handleReconnect = () => {
    console.log("Reconnect button clicked.");
    connectSocketAndStartRecording(true);
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 font-sans">
      <div className="w-full max-w-2xl p-4">
        <h1 className="text-4xl font-bold text-center text-gray-800 dark:text-white mb-4">
          Real-time Audio Chat
        </h1>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex flex-col items-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleToggleRecording}
                className={`px-6 py-3 rounded-full text-white font-semibold transition-colors ${
                  isRecording
                    ? "bg-red-500 hover:bg-red-600"
                    : "bg-blue-500 hover:bg-blue-600"
                }`}
                disabled={
                  connectionStatus === "Connecting..." ||
                  (isRecording && connectionStatus !== "Connected")
                }
              >
                {isRecording ? "Stop Recording" : "Start Recording"}
              </button>
              {(connectionStatus === "Disconnected" ||
                connectionStatus.startsWith("Error")) &&
                !isRecording && (
                  <button
                    onClick={handleReconnect}
                    className="px-6 py-3 rounded-full text-white font-semibold transition-colors bg-green-500 hover:bg-green-600"
                  >
                    Reconnect
                  </button>
                )}
            </div>
            <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              Status: {connectionStatus}
            </p>

            {isRecording && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Audio Level:
                </p>
                <div className="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-green-500"
                    style={{
                      width: `${Math.max(5, audioLevel * 100)}%`,
                      transition: "width 0.1s ease-out",
                    }}
                  />
                </div>
                {audioLevel === 0 && (
                  <p className="text-xs text-red-500 mt-1">
                    Warning: No audio detected! Check your microphone settings.
                  </p>
                )}
              </div>
            )}
          </div>
          <div className="mt-6 space-y-4">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg ${
                  msg.type === "user_transcript"
                    ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 self-end"
                    : "bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 self-start"
                }`}
              >
                <p className="font-semibold">
                  {msg.type === "user_transcript" ? "You" : "AI"}
                </p>
                <p>{msg.data}</p>
              </div>
            ))}
          </div>
        </div>

        <audio ref={audioPlayerRef} className="hidden" />
      </div>
    </div>
  );
}

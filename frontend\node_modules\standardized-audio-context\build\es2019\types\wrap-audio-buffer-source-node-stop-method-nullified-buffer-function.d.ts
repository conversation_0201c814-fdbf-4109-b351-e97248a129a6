import { TNativeAudioBufferSourceNode } from './native-audio-buffer-source-node';
import { TNativeContext } from './native-context';
export type TWrapAudioBufferSourceNodeStopMethodNullifiedBufferFunction = (nativeAudioBufferSourceNode: TNativeAudioBufferSourceNode, nativeContext: TNativeContext) => void;
//# sourceMappingURL=wrap-audio-buffer-source-node-stop-method-nullified-buffer-function.d.ts.map
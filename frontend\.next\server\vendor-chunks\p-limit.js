"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/p-limit";
exports.ids = ["vendor-chunks/p-limit"];
exports.modules = {

/***/ "(ssr)/./node_modules/p-limit/index.js":
/*!***************************************!*\
  !*** ./node_modules/p-limit/index.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pLimit),\n/* harmony export */   limitFunction: () => (/* binding */ limitFunction)\n/* harmony export */ });\n/* harmony import */ var yocto_queue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! yocto-queue */ \"(ssr)/./node_modules/yocto-queue/index.js\");\n\n\nfunction pLimit(concurrency) {\n\tvalidateConcurrency(concurrency);\n\n\tconst queue = new yocto_queue__WEBPACK_IMPORTED_MODULE_0__[\"default\"]();\n\tlet activeCount = 0;\n\n\tconst resumeNext = () => {\n\t\tif (activeCount < concurrency && queue.size > 0) {\n\t\t\tqueue.dequeue()();\n\t\t\t// Since `pendingCount` has been decreased by one, increase `activeCount` by one.\n\t\t\tactiveCount++;\n\t\t}\n\t};\n\n\tconst next = () => {\n\t\tactiveCount--;\n\n\t\tresumeNext();\n\t};\n\n\tconst run = async (function_, resolve, arguments_) => {\n\t\tconst result = (async () => function_(...arguments_))();\n\n\t\tresolve(result);\n\n\t\ttry {\n\t\t\tawait result;\n\t\t} catch {}\n\n\t\tnext();\n\t};\n\n\tconst enqueue = (function_, resolve, arguments_) => {\n\t\t// Queue `internalResolve` instead of the `run` function\n\t\t// to preserve asynchronous context.\n\t\tnew Promise(internalResolve => {\n\t\t\tqueue.enqueue(internalResolve);\n\t\t}).then(\n\t\t\trun.bind(undefined, function_, resolve, arguments_),\n\t\t);\n\n\t\t(async () => {\n\t\t\t// This function needs to wait until the next microtask before comparing\n\t\t\t// `activeCount` to `concurrency`, because `activeCount` is updated asynchronously\n\t\t\t// after the `internalResolve` function is dequeued and called. The comparison in the if-statement\n\t\t\t// needs to happen asynchronously as well to get an up-to-date value for `activeCount`.\n\t\t\tawait Promise.resolve();\n\n\t\t\tif (activeCount < concurrency) {\n\t\t\t\tresumeNext();\n\t\t\t}\n\t\t})();\n\t};\n\n\tconst generator = (function_, ...arguments_) => new Promise(resolve => {\n\t\tenqueue(function_, resolve, arguments_);\n\t});\n\n\tObject.defineProperties(generator, {\n\t\tactiveCount: {\n\t\t\tget: () => activeCount,\n\t\t},\n\t\tpendingCount: {\n\t\t\tget: () => queue.size,\n\t\t},\n\t\tclearQueue: {\n\t\t\tvalue() {\n\t\t\t\tqueue.clear();\n\t\t\t},\n\t\t},\n\t\tconcurrency: {\n\t\t\tget: () => concurrency,\n\n\t\t\tset(newConcurrency) {\n\t\t\t\tvalidateConcurrency(newConcurrency);\n\t\t\t\tconcurrency = newConcurrency;\n\n\t\t\t\tqueueMicrotask(() => {\n\t\t\t\t\t// eslint-disable-next-line no-unmodified-loop-condition\n\t\t\t\t\twhile (activeCount < concurrency && queue.size > 0) {\n\t\t\t\t\t\tresumeNext();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t},\n\t});\n\n\treturn generator;\n}\n\nfunction limitFunction(function_, option) {\n\tconst {concurrency} = option;\n\tconst limit = pLimit(concurrency);\n\n\treturn (...arguments_) => limit(() => function_(...arguments_));\n}\n\nfunction validateConcurrency(concurrency) {\n\tif (!((Number.isInteger(concurrency) || concurrency === Number.POSITIVE_INFINITY) && concurrency > 0)) {\n\t\tthrow new TypeError('Expected `concurrency` to be a number from 1 and up');\n\t}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/p-limit/index.js\n");

/***/ })

};
;
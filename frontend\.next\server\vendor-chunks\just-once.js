"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/just-once";
exports.ids = ["vendor-chunks/just-once"];
exports.modules = {

/***/ "(ssr)/./node_modules/just-once/index.mjs":
/*!******************************************!*\
  !*** ./node_modules/just-once/index.mjs ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ functionOnce)\n/* harmony export */ });\nvar functionOnce = once;\n\n/*\nlet i = 0;\nconst getFirst = once(() => ++i);\ngetFirst(); // 1\ngetFirst(); // 1\n*/\n\nfunction once(fn) {\n  var called, value;\n\n  if (typeof fn !== 'function') {\n    throw new Error('expected a function but got ' + fn);\n  }\n\n  return function wrap() {\n    if (called) {\n      return value;\n    }\n    called = true;\n    value = fn.apply(this, arguments);\n    fn = undefined;\n    return value;\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanVzdC1vbmNlL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFaUMiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcaGFja29uX3R0c1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcanVzdC1vbmNlXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGZ1bmN0aW9uT25jZSA9IG9uY2U7XG5cbi8qXG5sZXQgaSA9IDA7XG5jb25zdCBnZXRGaXJzdCA9IG9uY2UoKCkgPT4gKytpKTtcbmdldEZpcnN0KCk7IC8vIDFcbmdldEZpcnN0KCk7IC8vIDFcbiovXG5cbmZ1bmN0aW9uIG9uY2UoZm4pIHtcbiAgdmFyIGNhbGxlZCwgdmFsdWU7XG5cbiAgaWYgKHR5cGVvZiBmbiAhPT0gJ2Z1bmN0aW9uJykge1xuICAgIHRocm93IG5ldyBFcnJvcignZXhwZWN0ZWQgYSBmdW5jdGlvbiBidXQgZ290ICcgKyBmbik7XG4gIH1cblxuICByZXR1cm4gZnVuY3Rpb24gd3JhcCgpIHtcbiAgICBpZiAoY2FsbGVkKSB7XG4gICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIGNhbGxlZCA9IHRydWU7XG4gICAgdmFsdWUgPSBmbi5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgIGZuID0gdW5kZWZpbmVkO1xuICAgIHJldHVybiB2YWx1ZTtcbiAgfTtcbn1cblxuZXhwb3J0IHtmdW5jdGlvbk9uY2UgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/just-once/index.mjs\n");

/***/ })

};
;
{"version": 3, "file": "upsample.js", "sourceRoot": "", "sources": ["upsample.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAuG;AAIvG,gDAAyC;AAEzC,oCAAoD;AAoBpD,MAAM,uBAAuB,GAAG;IAC9B,IAAI,EAAE,UAAU;IAChB,UAAU,EAAE,CAAC,GAAG,CAAC;IACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;CACnC,CAAC;AAEK,MAAM,QAAQ,GAA+C,CAClE,gBAAuC,EACvC,MAAgB,EAChB,UAA8B,EACpB,EAAE;IACZ,IAAA,sBAAc,EAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IACnC,MAAM,MAAM,GAAG,gBAAgB,CAAC,GAAG,CACjC;QACE,GAAG,uBAAuB;QAC1B,SAAS,EAAE,UAAU,CAAC,QAAQ;QAC9B,GAAG,EAAE,GAAG,EAAE,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,MAAM,EAAE,UAAU,CAAC;KAC3E,EACD,MAAM,CACP,CAAC;IACF,OAAO,CAAC,MAAM,CAAC,CAAC;AAClB,CAAC,CAAC;AAfW,QAAA,QAAQ,YAenB;AAEK,MAAM,yBAAyB,GAA+C,CACnF,IAAgB,EACI,EAAE,CAAC,IAAA,+BAAuB,EAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAF7C,QAAA,yBAAyB,6BAEoB;AAEnD,MAAM,yBAAyB,GAA+C,CACnF,IAAgB,EACI,EAAE,CAAC,IAAA,+BAAuB,EAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAF7C,QAAA,yBAAyB,6BAEoB;AAEnD,MAAM,uBAAuB,GAAG,CAAC,IAAgB,EAAE,KAAa,EAAsB,EAAE;IAC7F,MAAM,QAAQ,GAAG,KAAK,IAAI,EAAE,CAAC;IAE7B,6BAA6B;IAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAC1D,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,GAAG,EAAE,IAAI,IAAI,KAAK,OAAO,CAAC,EAAE;QAC/E,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAC;KAC/C;IAED,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,IAAI,KAAK,GAAG,CAAC,EAAE;QACb,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAA,wBAAgB,EAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;KAC1C;IAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;IAEhF,MAAM,uBAAuB,GAC3B,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,gCAAgC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IACxG,IACE;QACE,YAAY;QACZ,oBAAoB;QACpB,sBAAsB;QACtB,eAAe;QACf,oBAAoB;QACpB,YAAY;KACb,CAAC,OAAO,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,EACzC;QACA,MAAM,IAAI,KAAK,CAAC,8BAA8B,uBAAuB,oBAAoB,CAAC,CAAC;KAC5F;IACD,MAAM,YAAY,GAAG,uBAAuB,KAAK,oBAAoB,CAAC;IACtE,MAAM,gBAAgB,GAAG,YAAY,CAAC;IAEtC,MAAM,WAAW,GACf,IAAI,KAAK,SAAS,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,cAAc,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3G,IAAI,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;QAChG,MAAM,IAAI,KAAK,CAAC,iBAAiB,WAAW,oBAAoB,CAAC,CAAC;KACnE;IAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC;IAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;IAC1E,IAAI,cAAc,IAAI,IAAI,KAAK,OAAO,EAAE;QACtC,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;KAC7E;IAED,MAAM,wBAAwB,GAC5B,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,uBAAuB,KAAK,YAAY,IAAI,WAAW,KAAK,OAAO,CAAC;IAEhH,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,IAAI,aAAa,GAAG,CAAC,CAAC;IAEtB,IAAI,KAAK,GAAG,EAAE,EAAE;QACd,oCAAoC;QACpC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,WAAW,GAAG,CAAC,CAAC;YAChB,cAAc,GAAG,CAAC,CAAC;YACnB,aAAa,GAAG,CAAC,CAAC;SACnB;aAAM;YACL,cAAc,GAAG,CAAC,CAAC;YACnB,aAAa,GAAG,CAAC,CAAC;SACnB;KACF;SAAM,IAAI,KAAK,KAAK,CAAC,EAAE;QACtB,cAAc,GAAG,CAAC,CAAC;KACpB;IAED,OAAO,IAAA,sDAA2B,EAAC;QACjC,KAAK;QACL,QAAQ;QACR,IAAI;QACJ,MAAM;QACN,kBAAkB;QAClB,uBAAuB;QACvB,gBAAgB;QAChB,YAAY;QACZ,WAAW;QACX,iBAAiB;QACjB,cAAc;QACd,wBAAwB;QACxB,WAAW;QACX,cAAc;QACd,aAAa;KACd,CAAC,CAAC;AACL,CAAC,CAAC;AApFW,QAAA,uBAAuB,2BAoFlC;AAEF,MAAM,yBAAyB,GAAG,CAChC,gBAAuC,EACvC,MAAgB,EAChB,UAA8B,EACjB,EAAE;IACf,MAAM,IAAI,GAAG,IAAA,qBAAO,EAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACzE,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAAG,gBAAgB,CAAC,8BAA8B,CAC/E,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EACd,mBAAW,CAAC,QAAQ,CACrB,CAAC;IAEF,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3F,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,gBAAgB,CAAC,8BAA8B,CACjF,WAAW,EACX,mBAAW,CAAC,QAAQ,CACrB,CAAC;IACF,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC;IAE/B,MAAM,aAAa,GAAG,IAAI,KAAK,CAAS,GAAG,CAAC,CAAC;IAC7C,MAAM,YAAY,GAAG,IAAI,KAAK,CAAS,GAAG,CAAC,CAAC;IAC5C,IAAI,oBAAoB,GAAG;2BACF,GAAG;0BACJ,GAAG;OACtB,CAAC;IACN,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QACjC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjF,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAElF,oBAAoB,IAAI;yBACH,CAAC,OAAO,aAAa,CAAC,CAAC,CAAC;wBACzB,CAAC,OAAO,YAAY,CAAC,CAAC,CAAC;SACtC,CAAC;KACP;IACD,MAAM,qBAAqB,GAAG;;8CAEc,UAAU,KAAK,WAAW;wCAChC,IAAI,CAAC,SAAS;;;OAG/C,CAAC;IAEN,MAAM,YAAY,GAChB,UAAU,CAAC,IAAI,KAAK,SAAS;QAC3B,CAAC,CAAC,UAAU;YACV;MACF,qBAAqB;gCACK,GAAG;;qDAEkB,WAAW,KAAK,YAAY;;QAEzE,oBAAoB;;;gCAGI,GAAG;;;;;;;;;;;;;;MAc7B;QACA,CAAC,CAAC,GAAG,KAAK,CAAC;YACT,CAAC,CAAC,cAAc;gBACd;MACJ,qBAAqB;;;qDAG0B,WAAW,KAAK,YAAY;;QAEzE,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;oCA2BQ,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;MAqB/C;YACE,CAAC,CAAC,cAAc;gBACd;MACJ,qBAAqB;;;qDAG0B,WAAW,KAAK,YAAY;;QAEzE,oBAAoB;;;;;;;;;;;;;;;;;;;;oCAoBQ,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;MAqB/C,CAAC;IACL,OAAO;QACL,GAAG,uBAAuB;QAC1B,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QACtF,YAAY;QACZ,SAAS,EAAE;YACT;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,KAAK;gBACX,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM;gBACrC,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACjD;SACF;KACF,CAAC;AACJ,CAAC,CAAC;AAEK,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAE,SAA6B,EAAQ,EAAE;IACtF,IACE,CAAC,MAAM;QACP,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;QAC5C,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,IAAI,SAAS,CAAC,KAAK,GAAG,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC;QACrE,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5C;QACA,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;KACpC;IAED,IAAI,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE;QACpF,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KACzC;IAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;KAChD;AACH,CAAC,CAAC;AAjBW,QAAA,cAAc,kBAiBzB;AAEK,MAAM,gBAAgB,GAAG,CAAC,MAAgB,EAAE,IAAY,EAAE,QAAiB,EAAQ,EAAE;IAC1F,IAAI,CAAC,QAAQ,EAAE;QACb,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;aACtE;SACF;KACF;SAAM;QACL,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,KAAK,IAAI,CAAC,EAAE;gBACd,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;aAC1D;SACF;KACF;IACD,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,OAAO,EAAE;QACzC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YACtF,MAAM,IAAI,KAAK,CAAC;;iBAEL,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,YAAY,CAAC,CAAC;SAC1D;KACF;AACH,CAAC,CAAC;AArBW,QAAA,gBAAgB,oBAqB3B"}
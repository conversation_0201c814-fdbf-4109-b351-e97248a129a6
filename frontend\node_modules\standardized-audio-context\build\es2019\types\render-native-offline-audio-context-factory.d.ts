import { TCacheTestResultFunction } from './cache-test-result-function';
import { TNativeGainNodeFactory } from './native-gain-node-factory';
import { TNativeScriptProcessorNodeFactory } from './native-script-processor-node-factory';
import { TRenderNativeOfflineAudioContextFunction } from './render-native-offline-audio-context-function';
export type TRenderNativeOfflineAudioContextFactory = (cacheTestResult: TCacheTestResultFunction, createNativeGainNode: TNativeGainNodeFactory, createNativeScriptProcessorNode: TNativeScriptProcessorNodeFactory, testOfflineAudioContextCurrentTimeSupport: () => Promise<boolean>) => TRenderNativeOfflineAudioContextFunction;
//# sourceMappingURL=render-native-offline-audio-context-factory.d.ts.map
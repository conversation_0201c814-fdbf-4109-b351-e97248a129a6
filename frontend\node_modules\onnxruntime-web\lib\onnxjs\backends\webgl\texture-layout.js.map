{"version": 3, "file": "texture-layout.js", "sourceRoot": "", "sources": ["texture-layout.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,qCAAuC;AAGvC,mCAAqD;AAE9C,MAAM,kCAAkC,GAAG,CAChD,qBAA4C,EAC5C,KAAwB,EACxB,WAAwB,EACT,EAAE;IACjB,MAAM,OAAO,GAAG,WAAW,KAAK,mBAAW,CAAC,QAAQ,IAAI,WAAW,KAAK,mBAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7G,MAAM,QAAQ,GAAG,WAAW,KAAK,mBAAW,CAAC,MAAM,CAAC;IACpD,MAAM,SAAS,GAAG,WAAW,KAAK,mBAAW,CAAC,gBAAgB,IAAI,WAAW,KAAK,mBAAW,CAAC,MAAM,CAAC;IACrG,MAAM,SAAS,GAAG,WAAW,KAAK,mBAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACjG,MAAM,aAAa,GACjB,WAAW,KAAK,mBAAW,CAAC,mBAAmB;QAC7C,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,SAAS,CAAC;IAChB,OAAO,IAAA,oCAA4B,EAAC,qBAAqB,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE;QACxF,QAAQ;QACR,SAAS;QACT,SAAS;KACV,CAAC,CAAC;AACL,CAAC,CAAC;AAlBW,QAAA,kCAAkC,sCAkB7C;AAEK,MAAM,8BAA8B,GAAG,CAC5C,qBAA4C,EAC5C,KAAwB,EACxB,WAAwB,EACN,EAAE;IACpB,MAAM,MAAM,GAAG,IAAA,0CAAkC,EAAC,qBAAqB,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAC7F,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC,CAAC;AAPW,QAAA,8BAA8B,kCAOzC;AAEF;;GAEG;AACI,MAAM,4BAA4B,GAAG,CAC1C,qBAA4C,EAC5C,KAAwB,EACxB,WAAkB,CAAC,EACnB,aAAiC,EACjC,KAAwB,EACT,EAAE;IACjB,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7C,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACjH,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;IAC1B,IAAI,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAClC,IAAI,IAAI,KAAK,CAAC,EAAE;QACd,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;KACpB;IACD,IAAI,QAAQ,KAAK,CAAC,EAAE;QAClB,oGAAoG;QACpG,aAAa,GAAG,KAAK,CAAC;KACvB;SAAM,IAAI,QAAQ,EAAE;QACnB,IAAI,QAAQ,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACvD;QACD,aAAa,GAAG,KAAK,CAAC;QACtB,IAAI,IAAI,GAAG,CAAC,EAAE;YACZ,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAChE;QACD,IAAI,IAAI,GAAG,CAAC,EAAE;YACZ,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SAChE;KACF;SAAM,IAAI,CAAC,aAAa,EAAE;QACzB,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;KACrE;IACD,OAAO;QACL,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,KAAK,EAAE,YAAY;QACnB,OAAO,EAAE,gBAAS,CAAC,cAAc,CAAC,YAAY,CAAC;QAC/C,aAAa;QACb,UAAU,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS;KACrC,CAAC;AACJ,CAAC,CAAC;AAzCW,QAAA,4BAA4B,gCAyCvC"}
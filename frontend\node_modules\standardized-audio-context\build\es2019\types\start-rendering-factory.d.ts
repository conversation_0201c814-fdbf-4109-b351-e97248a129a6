import { TAudioBufferStore } from './audio-buffer-store';
import { TCacheTestResultFunction } from './cache-test-result-function';
import { TGetAudioNodeRendererFunction } from './get-audio-node-renderer-function';
import { TGetUnrenderedAudioWorkletNodesFunction } from './get-unrendered-audio-worklet-nodes-function';
import { TNativeAudioBuffer } from './native-audio-buffer';
import { TRenderNativeOfflineAudioContextFunction } from './render-native-offline-audio-context-function';
import { TStartRenderingFunction } from './start-rendering-function';
import { TWrapAudioBufferCopyChannelMethodsFunction } from './wrap-audio-buffer-copy-channel-methods-function';
import { TWrapAudioBufferCopyChannelMethodsOutOfBoundsFunction } from './wrap-audio-buffer-copy-channel-methods-out-of-bounds-function';
export type TStartRenderingFactory = (audioBufferStore: TAudioBufferStore, cacheTestResult: TCacheTestResultFunction, getAudioNodeRenderer: TGetAudioNodeRendererFunction, getUnrenderedAudioWorkletNodes: TGetUnrenderedAudioWorkletNodesFunction, renderNativeOfflineAudioContext: TRenderNativeOfflineAudioContextFunction, testAudioBufferCopyChannelMethodsOutOfBoundsSupport: (nativeAudioBuffer: TNativeAudioBuffer) => boolean, wrapAudioBufferCopyChannelMethods: TWrapAudioBufferCopyChannelMethodsFunction, wrapAudioBufferCopyChannelMethodsOutOfBounds: TWrapAudioBufferCopyChannelMethodsOutOfBoundsFunction) => TStartRenderingFunction;
//# sourceMappingURL=start-rendering-factory.d.ts.map
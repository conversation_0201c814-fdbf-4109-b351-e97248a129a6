import { IAudioParam } from '../interfaces';
import { TNativeAudioParam } from './native-audio-param';
import { TNativeOfflineAudioContext } from './native-offline-audio-context';
export type TRenderInputsOfAudioParamFunction = (audioParam: IAudioParam, nativeOfflineAudioContext: TNativeOfflineAudioContext, nativeAudioParam: TNativeAudioParam) => Promise<void>;
//# sourceMappingURL=render-inputs-of-audio-param-function.d.ts.map